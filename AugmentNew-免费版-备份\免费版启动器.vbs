' ========================================
' AugmentNew 免费版启动器
' ========================================
' 🆓 完全免费，永久免费，拒绝收费！
' 🚫 无需激活码，无需验证码
' 🔓 完全开源，源码透明
' ========================================

Option Explicit

Dim objShell, objFSO, scriptDir, pythonExe, mainPy

' 创建对象
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' 获取脚本目录
scriptDir = objFSO.GetParentFolderName(WScript.ScriptFullName)
mainPy = objFSO.BuildPath(scriptDir, "gui_main.py")

' 显示免费版声明
ShowFreeVersionNotice()

' 检查主程序文件
If Not objFSO.FileExists(mainPy) Then
    MsgBox "❌ 错误：找不到主程序文件！" & vbCrLf & vbCrLf & _
           "文件路径：" & mainPy & vbCrLf & vbCrLf & _
           "请确保所有文件完整", vbCritical, "AugmentNew 免费版"
    WScript.Quit 1
End If

' 查找Python
pythonExe = FindPython()
If pythonExe = "" Then
    ShowPythonInstallGuide()
    WScript.Quit 1
End If

' 启动程序
LaunchProgram()

' 清理
Set objShell = Nothing
Set objFSO = Nothing
WScript.Quit 0

' ========================================
' 函数定义
' ========================================

Sub ShowFreeVersionNotice()
    MsgBox "🆓 AugmentNew 免费版启动器" & vbCrLf & vbCrLf & _
           "✅ 完全免费，永久免费" & vbCrLf & _
           "✅ 无需激活码或验证码" & vbCrLf & _
           "✅ 完全开源，源码透明" & vbCrLf & _
           "✅ 安全可靠，自动备份" & vbCrLf & vbCrLf & _
           "⚠️ 如有人向您收费，请立即举报！" & vbCrLf & _
           "⚠️ 真正的开源软件永远免费！" & vbCrLf & vbCrLf & _
           "点击确定开始启动程序...", _
           vbInformation, "AugmentNew 免费版"
End Sub

Function FindPython()
    Dim pythonPaths, i, testCmd, result
    
    ' Python可能的路径
    pythonPaths = Array( _
        "python", _
        "python3", _
        "py", _
        "C:\Python310\python.exe", _
        "C:\Python311\python.exe", _
        "C:\Python312\python.exe", _
        "C:\Python313\python.exe" _
    )
    
    FindPython = ""
    
    For i = 0 To UBound(pythonPaths)
        On Error Resume Next
        result = objShell.Run(pythonPaths(i) & " --version", 0, True)
        If Err.Number = 0 And result = 0 Then
            FindPython = pythonPaths(i)
            Exit For
        End If
        On Error GoTo 0
    Next
End Function

Sub ShowPythonInstallGuide()
    MsgBox "❌ 未找到Python解释器！" & vbCrLf & vbCrLf & _
           "AugmentNew需要Python 3.10或更高版本" & vbCrLf & vbCrLf & _
           "📥 请访问以下地址下载Python：" & vbCrLf & _
           "https://www.python.org/downloads/" & vbCrLf & vbCrLf & _
           "💡 安装时请勾选 'Add Python to PATH'" & vbCrLf & vbCrLf & _
           "安装完成后重新运行此脚本", _
           vbCritical, "需要安装Python"
End Sub

Sub LaunchProgram()
    Dim cmd, result
    
    ' 构建启动命令
    cmd = """" & pythonExe & """ """ & mainPy & """"
    
    ' 设置工作目录
    objShell.CurrentDirectory = scriptDir
    
    ' 启动程序
    On Error Resume Next
    result = objShell.Run(cmd, 1, False)
    
    If Err.Number <> 0 Then
        MsgBox "❌ 启动失败！" & vbCrLf & vbCrLf & _
               "错误信息：" & Err.Description & vbCrLf & vbCrLf & _
               "请检查Python环境是否正确安装", _
               vbCritical, "启动失败"
        WScript.Quit 1
    Else
        MsgBox "✅ AugmentNew 免费版启动成功！" & vbCrLf & vbCrLf & _
               "程序正在后台运行，请稍等..." & vbCrLf & vbCrLf & _
               "🆓 记住：本软件永久免费！" & vbCrLf & _
               "🚫 拒绝任何收费版本！", _
               vbInformation, "启动成功"
    End If
    
    On Error GoTo 0
End Sub
