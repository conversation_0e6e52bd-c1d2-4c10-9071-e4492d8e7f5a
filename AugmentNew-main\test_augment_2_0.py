"""
测试AugmentNew 2.0版本功能
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.augment_account_resetter import AugmentA<PERSON>unt<PERSON><PERSON><PERSON>

def test_augment_2_0():
    """测试AugmentNew 2.0版本功能"""
    print("🚀 测试AugmentNew 2.0版本功能...")
    
    try:
        # 设置2.0版本环境变量
        os.environ['AUGMENT_VERSION'] = '2.0'
        os.environ['AUGMENT_DEEP_RESET'] = '1'
        os.environ['AUGMENT_YUAOTIAN_METHOD'] = '1'
        
        # 创建2.0版本重置器
        resetter = AugmentAccountResetter("auto", version="2.0")
        
        print(f"✅ 重置器版本: {resetter.version}")
        print(f"✅ IDE类型: {resetter.ide_type}")
        print(f"✅ 检测到的IDE: {resetter.available_ides}")
        
        # 测试高级技术配置
        print("\n🔥 2.0版本高级技术配置:")
        for technique, enabled in resetter.advanced_techniques.items():
            status = "✅ 启用" if enabled else "❌ 禁用"
            print(f"  {technique}: {status}")
        
        # 测试yuaotian配置
        print("\n🔧 yuaotian方法配置:")
        print(f"  Cursor试用键数量: {len(resetter.yuaotian_config['cursor_trial_keys'])}")
        print(f"  Augment试用键数量: {len(resetter.yuaotian_config['augment_trial_keys'])}")
        print(f"  存储JSON键数量: {len(resetter.yuaotian_config['storage_json_keys'])}")
        
        # 测试设备指纹目标
        print("\n🔒 设备指纹重置目标:")
        for category, targets in resetter.fingerprint_targets.items():
            print(f"  {category}: {len(targets)}个目标")
        
        # 测试重置目标信息
        target_info = resetter.get_reset_target_info()
        print(f"\n🎯 重置目标信息:")
        print(f"  IDE类型: {target_info['ide_type']}")
        print(f"  可用IDE: {target_info['available_ides']}")
        print(f"  目标路径数量: {len(target_info['target_paths'])}")
        print(f"  警告数量: {len(target_info['warnings'])}")
        
        # 模拟2.0重置预览（不实际执行）
        print("\n🔍 2.0重置预览（模拟）:")
        print("  ✅ yuaotian/go-cursor-help方法")
        print("  ✅ 深度设备指纹重置")
        print("  ✅ 浏览器指纹清理")
        print("  ✅ 反检测措施")
        print("  ✅ 注册表深度清理")
        
        print("\n🎉 AugmentNew 2.0版本功能测试完成！")
        print("💡 所有2.0技术已就绪，可以执行终极重置")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_version_detection():
    """测试版本检测功能"""
    print("\n🔍 测试版本检测功能...")
    
    # 测试不同环境变量组合
    test_cases = [
        {'AUGMENT_VERSION': '2.0'},
        {'AUGMENT_DEEP_RESET': '1'},
        {'AUGMENT_YUAOTIAN_METHOD': '1'},
        {}  # 默认情况
    ]
    
    for i, env_vars in enumerate(test_cases):
        print(f"\n--- 测试案例 {i+1} ---")
        
        # 清理环境变量
        for key in ['AUGMENT_VERSION', 'AUGMENT_DEEP_RESET', 'AUGMENT_YUAOTIAN_METHOD']:
            if key in os.environ:
                del os.environ[key]
        
        # 设置测试环境变量
        for key, value in env_vars.items():
            os.environ[key] = value
        
        # 检测版本
        version_2_enabled = (
            os.environ.get('AUGMENT_VERSION') == '2.0' or 
            os.environ.get('AUGMENT_DEEP_RESET') == '1'
        )
        
        print(f"  环境变量: {env_vars}")
        print(f"  2.0版本启用: {'✅ 是' if version_2_enabled else '❌ 否'}")

if __name__ == "__main__":
    test_augment_2_0()
    test_version_detection()
