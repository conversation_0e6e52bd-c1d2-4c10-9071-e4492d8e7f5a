"""
测试IDE选择对话框GUI
"""

import os
import sys
import customtkinter as ctk

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui.ide_selector_dialog import IDESelectorDialog
from utils.augment_account_resetter import AugmentAccountResetter

class TestApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        
        self.title("IDE选择对话框测试")
        self.geometry("400x300")
        
        # 创建测试按钮
        test_btn = ctk.CTkButton(
            self,
            text="测试IDE选择对话框",
            command=self.test_ide_selector
        )
        test_btn.pack(pady=50)
        
        # 结果显示
        self.result_label = ctk.CTkLabel(self, text="点击按钮测试")
        self.result_label.pack(pady=20)
        
    def test_ide_selector(self):
        """测试IDE选择对话框"""
        try:
            # 检测可用的IDE
            resetter = AugmentAccountResetter("auto")
            available_ides = resetter.get_detected_ides()
            
            # 显示对话框
            dialog = IDESelectorDialog(self, available_ides)
            self.wait_window(dialog)
            
            # 获取结果
            result = dialog.get_result()
            if result:
                self.result_label.configure(text=f"选择的IDE: {result}")
                
                # 测试使用选择的IDE类型
                test_resetter = AugmentAccountResetter(result)
                target_info = test_resetter.get_reset_target_info()
                
                print(f"选择的IDE类型: {result}")
                print(f"目标信息: {target_info}")
            else:
                self.result_label.configure(text="用户取消了选择")
                
        except Exception as e:
            self.result_label.configure(text=f"错误: {str(e)}")
            print(f"测试错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    # 设置外观模式
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    
    app = TestApp()
    app.mainloop()
