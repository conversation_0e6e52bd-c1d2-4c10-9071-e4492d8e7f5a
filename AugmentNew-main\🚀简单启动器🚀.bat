@echo off
chcp 65001 >nul
title 🚀 AugmentNew 简单启动器 🚀

echo.
echo 🚀 AugmentNew 简单启动器 🚀
echo ================================
echo.

:: 切换到程序目录
cd /d "%~dp0"

echo 📍 当前目录: %CD%
echo.

:: 检查Python
echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，尝试使用py命令...
    py --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ 错误：未找到Python环境
        echo 请先安装Python 3.8或更高版本
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=py
    )
) else (
    set PYTHON_CMD=python
)

echo ✅ Python环境正常: %PYTHON_CMD%
echo.

:: 安装缺失的依赖
echo 📦 检查并安装依赖包...
%PYTHON_CMD% -c "import psutil" >nul 2>&1
if errorlevel 1 (
    echo 📦 安装 psutil...
    %PYTHON_CMD% -m pip install psutil --quiet
)

%PYTHON_CMD% -c "import customtkinter" >nul 2>&1
if errorlevel 1 (
    echo 📦 安装 customtkinter...
    %PYTHON_CMD% -m pip install customtkinter --quiet
)

%PYTHON_CMD% -c "import PIL" >nul 2>&1
if errorlevel 1 (
    echo 📦 安装 Pillow...
    %PYTHON_CMD% -m pip install Pillow --quiet
)

%PYTHON_CMD% -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo 📦 安装 requests...
    %PYTHON_CMD% -m pip install requests --quiet
)

echo ✅ 依赖包检查完成
echo.

:: 启动程序
echo 🚀 启动AugmentNew...
echo.

:: 设置环境变量
set AUGMENT_SUPER_MODE=1
set AUGMENT_ALL_FEATURES=1
set AUGMENT_NO_LIMITS=1

:: 启动程序
%PYTHON_CMD% main.py

:: 如果程序退出，显示信息
echo.
echo 👋 程序已退出
pause
