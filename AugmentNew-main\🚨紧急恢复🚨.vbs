' 🚨 AugmentNew 紧急恢复脚本 🚨
' 当系统出现问题时，使用此脚本进行紧急恢复
' 包含系统还原、文件恢复、服务重启等功能

Option Explicit

Dim objShell, objFSO, objWMI
Dim strCurrentDir

' 初始化对象
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")
Set objWMI = CreateObject("WbemScripting.SWbemLocator").ConnectServer()

' 获取当前目录
strCurrentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' 显示紧急恢复信息
ShowEmergencyInfo()

' 主恢复流程
Main()

Sub ShowEmergencyInfo()
    Dim strInfo
    strInfo = "🚨 AugmentNew 紧急恢复系统 🚨" & vbCrLf & vbCrLf & _
              "⚠️ 检测到系统可能出现问题" & vbCrLf & vbCrLf & _
              "🔧 恢复功能:" & vbCrLf & _
              "• 系统还原点恢复" & vbCrLf & _
              "• 关键文件恢复" & vbCrLf & _
              "• 系统服务重启" & vbCrLf & _
              "• 注册表修复" & vbCrLf & _
              "• 临时文件清理" & vbCrLf & _
              "• 网络连接重置" & vbCrLf & vbCrLf & _
              "🛡️ 安全保证:" & vbCrLf & _
              "• 不会删除用户数据" & vbCrLf & _
              "• 只恢复系统设置" & vbCrLf & _
              "• 完整操作日志" & vbCrLf & vbCrLf & _
              "是否开始紧急恢复？"
    
    If MsgBox(strInfo, vbYesNo + vbExclamation, "紧急恢复确认") = vbNo Then
        WScript.Quit
    End If
End Sub

Sub Main()
    On Error Resume Next
    
    Dim intChoice
    
    Do
        ' 显示恢复选项
        intChoice = ShowRecoveryMenu()
        
        Select Case intChoice
            Case 1
                SystemRestore()
            Case 2
                FileRecovery()
            Case 3
                ServiceRestart()
            Case 4
                RegistryRepair()
            Case 5
                TempFileCleanup()
            Case 6
                NetworkReset()
            Case 7
                FullRecovery()
            Case 8
                SystemDiagnosis()
            Case 0
                Exit Do
            Case Else
                MsgBox "无效选择，请重试", vbExclamation
        End Select
        
    Loop While intChoice <> 0
    
    MsgBox "🎉 紧急恢复操作完成！" & vbCrLf & vbCrLf & _
           "建议重启计算机以确保所有更改生效。", vbInformation, "恢复完成"
    
End Sub

Function ShowRecoveryMenu()
    Dim strMenu
    strMenu = "🚨 紧急恢复菜单 🚨" & vbCrLf & vbCrLf & _
              "请选择恢复操作:" & vbCrLf & vbCrLf & _
              "1. 🔄 系统还原点恢复" & vbCrLf & _
              "2. 📁 关键文件恢复" & vbCrLf & _
              "3. ⚙️ 系统服务重启" & vbCrLf & _
              "4. 📝 注册表修复" & vbCrLf & _
              "5. 🧹 临时文件清理" & vbCrLf & _
              "6. 🌐 网络连接重置" & vbCrLf & _
              "7. 💥 完整恢复 (推荐)" & vbCrLf & _
              "8. 🔍 系统诊断" & vbCrLf & _
              "0. 🚪 退出" & vbCrLf & vbCrLf & _
              "请输入选项编号:"
    
    ShowRecoveryMenu = InputBox(strMenu, "紧急恢复菜单", "7")
    If ShowRecoveryMenu = "" Then ShowRecoveryMenu = 0
End Function

Sub SystemRestore()
    On Error Resume Next
    
    MsgBox "🔄 开始系统还原..." & vbCrLf & vbCrLf & _
           "正在查找最近的还原点...", vbInformation
    
    ' 启动系统还原
    Dim strRestoreCmd
    strRestoreCmd = "rstrui.exe"
    objShell.Run strRestoreCmd, 1, False
    
    MsgBox "✅ 系统还原程序已启动" & vbCrLf & vbCrLf & _
           "请在系统还原界面中选择 'AugmentNew_SafetyPoint' 还原点", vbInformation
End Sub

Sub FileRecovery()
    On Error Resume Next
    
    MsgBox "📁 开始关键文件恢复...", vbInformation
    
    Dim intRecovered
    intRecovered = 0
    
    ' 恢复备份文件
    Dim strBackupDir
    strBackupDir = strCurrentDir & "\emergency_backups"
    
    If objFSO.FolderExists(strBackupDir) Then
        Dim objFolder, objFile
        Set objFolder = objFSO.GetFolder(strBackupDir)
        
        For Each objFile In objFolder.Files
            If Right(objFile.Name, 5) = ".json" Or Right(objFile.Name, 4) = ".txt" Then
                Dim strTargetPath
                strTargetPath = strCurrentDir & "\" & objFile.Name
                
                If objFSO.FileExists(strTargetPath) Then
                    objFSO.DeleteFile strTargetPath
                End If
                
                objFSO.CopyFile objFile.Path, strTargetPath
                intRecovered = intRecovered + 1
            End If
        Next
    End If
    
    ' 重新创建必要目录
    Dim arrDirs, strDir
    arrDirs = Array("logs", "backups", "temp", "emergency_backups")
    
    For Each strDir In arrDirs
        Dim strFullDir
        strFullDir = strCurrentDir & "\" & strDir
        If Not objFSO.FolderExists(strFullDir) Then
            objFSO.CreateFolder(strFullDir)
            intRecovered = intRecovered + 1
        End If
    Next
    
    MsgBox "✅ 文件恢复完成" & vbCrLf & vbCrLf & _
           "恢复了 " & intRecovered & " 个文件/目录", vbInformation
End Sub

Sub ServiceRestart()
    On Error Resume Next
    
    MsgBox "⚙️ 开始重启系统服务...", vbInformation
    
    ' 需要重启的服务
    Dim arrServices, strService
    arrServices = Array("Themes", "AudioSrv", "BITS", "Spooler", "Winmgmt", "EventLog")
    
    Dim intRestarted
    intRestarted = 0
    
    For Each strService In arrServices
        ' 停止服务
        objShell.Run "sc stop " & strService, 0, True
        WScript.Sleep 2000
        
        ' 启动服务
        objShell.Run "sc start " & strService, 0, True
        WScript.Sleep 1000
        
        intRestarted = intRestarted + 1
    Next
    
    MsgBox "✅ 服务重启完成" & vbCrLf & vbCrLf & _
           "重启了 " & intRestarted & " 个系统服务", vbInformation
End Sub

Sub RegistryRepair()
    On Error Resume Next
    
    MsgBox "📝 开始注册表修复...", vbInformation
    
    ' 运行系统文件检查
    objShell.Run "sfc /scannow", 0, True
    
    ' 运行注册表检查
    objShell.Run "chkdsk C: /f", 0, True
    
    ' 重建图标缓存
    objShell.Run "ie4uinit.exe -ClearIconCache", 0, True
    
    ' 刷新组策略
    objShell.Run "gpupdate /force", 0, True
    
    MsgBox "✅ 注册表修复完成" & vbCrLf & vbCrLf & _
           "已执行系统文件检查和注册表修复", vbInformation
End Sub

Sub TempFileCleanup()
    On Error Resume Next
    
    MsgBox "🧹 开始临时文件清理...", vbInformation
    
    Dim intCleaned
    intCleaned = 0
    
    ' 清理系统临时目录
    Dim arrTempDirs, strTempDir
    arrTempDirs = Array( _
        objShell.ExpandEnvironmentStrings("%TEMP%"), _
        objShell.ExpandEnvironmentStrings("%TMP%"), _
        "C:\Windows\Temp", _
        objShell.ExpandEnvironmentStrings("%LOCALAPPDATA%\Temp") _
    )
    
    For Each strTempDir In arrTempDirs
        If objFSO.FolderExists(strTempDir) Then
            Dim objTempFolder, objTempFile
            Set objTempFolder = objFSO.GetFolder(strTempDir)
            
            For Each objTempFile In objTempFolder.Files
                If DateDiff("d", objTempFile.DateLastModified, Now()) > 7 Then
                    objFSO.DeleteFile objTempFile.Path, True
                    intCleaned = intCleaned + 1
                End If
            Next
        End If
    Next
    
    ' 清理回收站
    objShell.Run "PowerShell.exe -Command Clear-RecycleBin -Force", 0, True
    
    ' 清理DNS缓存
    objShell.Run "ipconfig /flushdns", 0, True
    
    MsgBox "✅ 临时文件清理完成" & vbCrLf & vbCrLf & _
           "清理了 " & intCleaned & " 个临时文件", vbInformation
End Sub

Sub NetworkReset()
    On Error Resume Next
    
    MsgBox "🌐 开始网络连接重置...", vbInformation
    
    ' 重置网络配置
    Dim arrNetCommands, strCmd
    arrNetCommands = Array( _
        "netsh winsock reset", _
        "netsh int ip reset", _
        "netsh int ipv4 reset", _
        "netsh int ipv6 reset", _
        "ipconfig /release", _
        "ipconfig /renew", _
        "ipconfig /flushdns", _
        "ipconfig /registerdns" _
    )
    
    For Each strCmd In arrNetCommands
        objShell.Run strCmd, 0, True
        WScript.Sleep 1000
    Next
    
    MsgBox "✅ 网络重置完成" & vbCrLf & vbCrLf & _
           "网络配置已重置，建议重启计算机", vbInformation
End Sub

Sub FullRecovery()
    On Error Resume Next
    
    MsgBox "💥 开始完整恢复..." & vbCrLf & vbCrLf & _
           "这将执行所有恢复操作，请耐心等待", vbInformation
    
    ' 执行所有恢复操作
    FileRecovery()
    WScript.Sleep 2000
    
    ServiceRestart()
    WScript.Sleep 2000
    
    TempFileCleanup()
    WScript.Sleep 2000
    
    NetworkReset()
    WScript.Sleep 2000
    
    RegistryRepair()
    
    MsgBox "🎉 完整恢复完成！" & vbCrLf & vbCrLf & _
           "所有恢复操作已执行完毕" & vbCrLf & _
           "强烈建议立即重启计算机", vbInformation
End Sub

Sub SystemDiagnosis()
    On Error Resume Next
    
    Dim strDiagResult
    strDiagResult = "🔍 系统诊断报告" & vbCrLf & vbCrLf
    
    ' 检查磁盘空间
    Dim objDrive
    Set objDrive = objFSO.GetDrive("C:")
    strDiagResult = strDiagResult & "💾 磁盘空间: " & _
        FormatNumber(objDrive.FreeSpace / 1024 / 1024 / 1024, 2) & " GB 可用" & vbCrLf
    
    ' 检查内存使用
    Dim objWMIService, colItems, objItem
    Set objWMIService = GetObject("winmgmts:\\.\root\cimv2")
    Set colItems = objWMIService.ExecQuery("SELECT * FROM Win32_OperatingSystem")
    
    For Each objItem In colItems
        Dim dblMemoryUsage
        dblMemoryUsage = ((objItem.TotalVisibleMemorySize - objItem.FreePhysicalMemory) / objItem.TotalVisibleMemorySize) * 100
        strDiagResult = strDiagResult & "🧠 内存使用: " & FormatNumber(dblMemoryUsage, 1) & "%" & vbCrLf
    Next
    
    ' 检查关键文件
    Dim arrCriticalFiles, strFile, intMissingFiles
    arrCriticalFiles = Array("main.py", "gui\main_window.py", "utils\__init__.py")
    intMissingFiles = 0
    
    For Each strFile In arrCriticalFiles
        If Not objFSO.FileExists(strCurrentDir & "\" & strFile) Then
            intMissingFiles = intMissingFiles + 1
        End If
    Next
    
    strDiagResult = strDiagResult & "📁 缺失文件: " & intMissingFiles & " 个" & vbCrLf
    
    ' 检查Python环境
    objShell.Run "python --version", 0, True
    If objShell.Environment("Process")("ERRORLEVEL") = "0" Then
        strDiagResult = strDiagResult & "🐍 Python环境: 正常" & vbCrLf
    Else
        strDiagResult = strDiagResult & "🐍 Python环境: 异常" & vbCrLf
    End If
    
    strDiagResult = strDiagResult & vbCrLf & "📋 建议操作:" & vbCrLf
    
    If intMissingFiles > 0 Then
        strDiagResult = strDiagResult & "• 执行文件恢复" & vbCrLf
    End If
    
    If dblMemoryUsage > 80 Then
        strDiagResult = strDiagResult & "• 关闭不必要的程序" & vbCrLf
    End If
    
    If objDrive.FreeSpace < 1073741824 Then
        strDiagResult = strDiagResult & "• 清理磁盘空间" & vbCrLf
    End If
    
    MsgBox strDiagResult, vbInformation, "系统诊断"
End Sub

' 清理对象
Set objShell = Nothing
Set objFSO = Nothing
Set objWMI = Nothing
