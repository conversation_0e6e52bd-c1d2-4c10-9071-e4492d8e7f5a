"""
测试AugmentNew 2.0的真实重置能力
验证Augment Code和Cursor AI的实际重置效果
"""

import os
import sys
import json
import time
import subprocess
import winreg
from pathlib import Path

def test_augment_code_reset_capability():
    """测试Augment Code重置能力"""
    print("🎯 测试Augment Code重置能力")
    print("=" * 50)
    
    capabilities = {
        'vscode_detection': False,
        'telemetry_access': False,
        'database_access': False,
        'extension_access': False,
        'registry_access': False
    }
    
    # 1. 检测VSCode安装
    vscode_paths = [
        os.path.expandvars(r"%APPDATA%\Code"),
        os.path.expandvars(r"%LOCALAPPDATA%\Programs\Microsoft VS Code"),
        os.path.expandvars(r"%PROGRAMFILES%\Microsoft VS Code")
    ]
    
    for path in vscode_paths:
        if os.path.exists(path):
            capabilities['vscode_detection'] = True
            print(f"✅ VSCode检测: 发现安装路径 {path}")
            break
    
    if not capabilities['vscode_detection']:
        print("❌ VSCode检测: 未发现VSCode安装")
    
    # 2. 检测Telemetry文件访问能力
    telemetry_files = [
        os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\storage.json"),
        os.path.expandvars(r"%APPDATA%\Code\User\settings.json")
    ]
    
    for file_path in telemetry_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                capabilities['telemetry_access'] = True
                print(f"✅ Telemetry访问: 可以读取 {os.path.basename(file_path)}")
                
                # 检查是否包含Augment相关数据
                if 'augment' in content.lower():
                    print(f"🎯 发现Augment相关数据在 {os.path.basename(file_path)}")
                break
            except Exception as e:
                print(f"⚠️ Telemetry访问: 读取失败 {file_path} - {e}")
    
    # 3. 检测数据库访问能力
    database_files = [
        os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\state.vscdb"),
        os.path.expandvars(r"%APPDATA%\Code\CachedData")
    ]
    
    for db_path in database_files:
        if os.path.exists(db_path):
            capabilities['database_access'] = True
            print(f"✅ 数据库访问: 发现数据库 {os.path.basename(db_path)}")
            break
    
    # 4. 检测扩展目录访问能力
    extension_dirs = [
        os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
        os.path.expandvars(r"%APPDATA%\Code\User\workspaceStorage")
    ]
    
    for ext_dir in extension_dirs:
        if os.path.exists(ext_dir):
            try:
                items = os.listdir(ext_dir)
                capabilities['extension_access'] = True
                print(f"✅ 扩展访问: 可以访问 {os.path.basename(ext_dir)} ({len(items)}个项目)")
                
                # 检查Augment扩展
                augment_items = [item for item in items if 'augment' in item.lower()]
                if augment_items:
                    print(f"🎯 发现Augment扩展数据: {augment_items}")
                break
            except Exception as e:
                print(f"⚠️ 扩展访问: 访问失败 {ext_dir} - {e}")
    
    # 5. 检测注册表访问能力
    try:
        # 尝试访问VSCode相关注册表
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall") as key:
            capabilities['registry_access'] = True
            print("✅ 注册表访问: 具备注册表读取权限")
    except Exception as e:
        print(f"❌ 注册表访问: 权限不足 - {e}")
    
    # 计算Augment Code重置能力评分
    score = sum(capabilities.values()) / len(capabilities) * 100
    print(f"\n📊 Augment Code重置能力评分: {score:.1f}/100")
    
    return capabilities, score

def test_cursor_ai_reset_capability():
    """测试Cursor AI重置能力"""
    print("\n🎯 测试Cursor AI重置能力")
    print("=" * 50)
    
    capabilities = {
        'cursor_detection': False,
        'machine_id_access': False,
        'storage_access': False,
        'local_state_access': False,
        'registry_access': False
    }
    
    # 1. 检测Cursor安装
    cursor_paths = [
        os.path.expandvars(r"%APPDATA%\Cursor"),
        os.path.expandvars(r"%LOCALAPPDATA%\Cursor"),
        os.path.expandvars(r"%LOCALAPPDATA%\Programs\Cursor")
    ]
    
    for path in cursor_paths:
        if os.path.exists(path):
            capabilities['cursor_detection'] = True
            print(f"✅ Cursor检测: 发现安装路径 {path}")
            break
    
    if not capabilities['cursor_detection']:
        print("❌ Cursor检测: 未发现Cursor安装")
    
    # 2. 检测machineid文件访问能力
    machine_id_path = os.path.expandvars(r"%APPDATA%\Cursor\machineid")
    if os.path.exists(machine_id_path):
        try:
            with open(machine_id_path, 'r', encoding='utf-8') as f:
                machine_id = f.read().strip()
            capabilities['machine_id_access'] = True
            print(f"✅ 机器ID访问: 可以读取machineid ({machine_id[:8]}...)")
        except Exception as e:
            print(f"⚠️ 机器ID访问: 读取失败 - {e}")
    else:
        print("❌ 机器ID访问: machineid文件不存在")
    
    # 3. 检测storage.json访问能力
    storage_json_path = os.path.expandvars(r"%APPDATA%\Cursor\User\globalStorage\storage.json")
    if os.path.exists(storage_json_path):
        try:
            with open(storage_json_path, 'r', encoding='utf-8') as f:
                storage_data = json.load(f)
            capabilities['storage_access'] = True
            print(f"✅ 存储访问: 可以读取storage.json ({len(storage_data)}个键)")
            
            # 检查试用相关数据
            trial_keys = [key for key in storage_data.keys() if 'trial' in key.lower() or 'cursor' in key.lower()]
            if trial_keys:
                print(f"🎯 发现试用相关数据: {trial_keys[:3]}...")
        except Exception as e:
            print(f"⚠️ 存储访问: 读取失败 - {e}")
    else:
        print("❌ 存储访问: storage.json文件不存在")
    
    # 4. 检测Local State访问能力
    local_state_path = os.path.expandvars(r"%LOCALAPPDATA%\Cursor\User Data\Local State")
    if os.path.exists(local_state_path):
        try:
            with open(local_state_path, 'r', encoding='utf-8') as f:
                local_state = json.load(f)
            capabilities['local_state_access'] = True
            print(f"✅ 本地状态访问: 可以读取Local State ({len(local_state)}个键)")
        except Exception as e:
            print(f"⚠️ 本地状态访问: 读取失败 - {e}")
    else:
        print("❌ 本地状态访问: Local State文件不存在")
    
    # 5. 检测Cursor注册表访问能力
    try:
        # 尝试访问Cursor相关注册表
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"SOFTWARE") as key:
            capabilities['registry_access'] = True
            print("✅ 注册表访问: 具备注册表读取权限")
    except Exception as e:
        print(f"❌ 注册表访问: 权限不足 - {e}")
    
    # 计算Cursor AI重置能力评分
    score = sum(capabilities.values()) / len(capabilities) * 100
    print(f"\n📊 Cursor AI重置能力评分: {score:.1f}/100")
    
    return capabilities, score

def test_system_permissions():
    """测试系统权限"""
    print("\n🔐 测试系统权限")
    print("=" * 30)
    
    permissions = {
        'admin_rights': False,
        'file_write': False,
        'registry_write': False,
        'process_control': False
    }
    
    # 1. 检测管理员权限
    try:
        import ctypes
        permissions['admin_rights'] = ctypes.windll.shell32.IsUserAnAdmin()
        if permissions['admin_rights']:
            print("✅ 管理员权限: 具备管理员权限")
        else:
            print("❌ 管理员权限: 需要管理员权限")
    except Exception as e:
        print(f"⚠️ 管理员权限: 检测失败 - {e}")
    
    # 2. 检测文件写入权限
    try:
        test_file = os.path.expandvars(r"%TEMP%\augment_test.txt")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        permissions['file_write'] = True
        print("✅ 文件写入: 具备文件写入权限")
    except Exception as e:
        print(f"❌ 文件写入: 权限不足 - {e}")
    
    # 3. 检测注册表写入权限
    try:
        test_key = r"SOFTWARE\AugmentTest"
        with winreg.CreateKey(winreg.HKEY_CURRENT_USER, test_key) as key:
            winreg.SetValueEx(key, "test", 0, winreg.REG_SZ, "test")
        winreg.DeleteKey(winreg.HKEY_CURRENT_USER, test_key)
        permissions['registry_write'] = True
        print("✅ 注册表写入: 具备注册表写入权限")
    except Exception as e:
        print(f"❌ 注册表写入: 权限不足 - {e}")
    
    # 4. 检测进程控制权限
    try:
        result = subprocess.run(['tasklist'], capture_output=True, text=True)
        if result.returncode == 0:
            permissions['process_control'] = True
            print("✅ 进程控制: 具备进程查看权限")
        else:
            print("❌ 进程控制: 权限不足")
    except Exception as e:
        print(f"❌ 进程控制: 检测失败 - {e}")
    
    # 计算系统权限评分
    score = sum(permissions.values()) / len(permissions) * 100
    print(f"\n📊 系统权限评分: {score:.1f}/100")
    
    return permissions, score

def generate_capability_report(augment_caps, augment_score, cursor_caps, cursor_score, sys_perms, sys_score):
    """生成能力报告"""
    print("\n" + "=" * 60)
    print("📋 AugmentNew 2.0 真实能力评估报告")
    print("=" * 60)
    
    print(f"\n🎯 Augment Code重置能力: {augment_score:.1f}/100")
    for cap, status in augment_caps.items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {cap}: {'可用' if status else '不可用'}")
    
    print(f"\n🎯 Cursor AI重置能力: {cursor_score:.1f}/100")
    for cap, status in cursor_caps.items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {cap}: {'可用' if status else '不可用'}")
    
    print(f"\n🔐 系统权限: {sys_score:.1f}/100")
    for perm, status in sys_perms.items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {perm}: {'具备' if status else '缺失'}")
    
    # 计算总体能力评分
    total_score = (augment_score + cursor_score + sys_score) / 3
    print(f"\n🏆 总体能力评分: {total_score:.1f}/100")
    
    # 给出建议
    print(f"\n💡 建议:")
    if total_score >= 80:
        print("  🟢 系统具备强大的重置能力，可以正常使用所有功能")
    elif total_score >= 60:
        print("  🟡 系统具备基本的重置能力，建议提升权限或安装目标软件")
    else:
        print("  🔴 系统重置能力有限，需要安装目标软件并提升权限")
    
    if not sys_perms['admin_rights']:
        print("  ⚠️ 强烈建议以管理员权限运行以获得最佳效果")
    
    if not augment_caps['vscode_detection'] and not cursor_caps['cursor_detection']:
        print("  ⚠️ 未检测到VSCode或Cursor，请先安装目标软件")

if __name__ == "__main__":
    print("🧪 AugmentNew 2.0 真实能力测试")
    print("测试系统对Augment Code和Cursor AI的实际重置能力")
    print("=" * 60)
    
    # 执行所有测试
    augment_caps, augment_score = test_augment_code_reset_capability()
    cursor_caps, cursor_score = test_cursor_ai_reset_capability()
    sys_perms, sys_score = test_system_permissions()
    
    # 生成报告
    generate_capability_report(augment_caps, augment_score, cursor_caps, cursor_score, sys_perms, sys_score)
    
    print(f"\n🎉 测试完成！")
    print(f"💡 这个测试验证了系统的真实重置能力，而不是理论能力")
    print(f"🚀 基于测试结果，您可以了解当前环境下的实际可行性")
