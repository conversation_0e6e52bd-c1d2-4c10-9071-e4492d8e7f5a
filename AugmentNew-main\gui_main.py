#!/usr/bin/env python3
"""
AugmentNew GUI版本主程序入口
"""

import sys
import os
import json
import argparse
import tkinter as tk
from tkinter import messagebox
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """检查依赖项"""
    try:
        import customtkinter
        return True
    except ImportError:
        return False

def install_dependencies():
    """安装依赖项"""
    try:
        import subprocess
        import sys
        
        # 安装customtkinter
        subprocess.check_call([sys.executable, "-m", "pip", "install", "customtkinter>=5.2.0"])
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pillow>=10.0.0"])
        
        return True
    except Exception as e:
        print(f"安装依赖失败: {e}")
        return False

def check_super_mode():
    """检查是否启用超级模式"""
    super_config = {
        'super_mode': False,
        'all_features_enabled': False,
        'admin_privileges': False
    }

    try:
        # 检查命令行参数
        parser = argparse.ArgumentParser()
        parser.add_argument('--super-mode', action='store_true', help='启用超级模式')
        parser.add_argument('--all-features', action='store_true', help='启用所有功能')
        parser.add_argument('--no-limits', action='store_true', help='移除所有限制')
        args, _ = parser.parse_known_args()

        if args.super_mode or args.all_features or args.no_limits:
            super_config['super_mode'] = True
            super_config['all_features_enabled'] = True

        # 检查环境变量
        if os.environ.get('AUGMENT_SUPER_MODE') == '1':
            super_config['super_mode'] = True
        if os.environ.get('AUGMENT_ALL_FEATURES') == '1':
            super_config['all_features_enabled'] = True
        if os.environ.get('AUGMENT_ADMIN_MODE') == 'True':
            super_config['admin_privileges'] = True

        # 检查配置文件
        config_file = Path(__file__).parent / "super_config.json"
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                super_config.update(config_data)
            except:
                pass

        # 检查功能激活标记文件
        feature_files = [
            Path(__file__).parent / ".device_fingerprint_enabled",
            Path(__file__).parent / ".augment_reset_enabled",
            Path(__file__).parent / ".super_browser_enabled",
            Path(__file__).parent / ".nuclear_reset_enabled",
            Path(__file__).parent / ".all_features_unlocked"
        ]

        all_features_unlocked = all(f.exists() for f in feature_files)
        if all_features_unlocked:
            super_config['super_mode'] = True
            super_config['all_features_enabled'] = True

    except Exception as e:
        print(f"检查超级模式时出错: {e}")

    return super_config

def main():
    """主函数"""
    # 检查超级模式
    super_config = check_super_mode()

    if super_config['super_mode']:
        print("🚀 AugmentNew 超级模式已激活！")
        print("✨ 所有高级功能已解锁")
        if super_config['admin_privileges']:
            print("👑 检测到管理员权限")

    # 检查依赖项
    if not check_dependencies():
        print("检测到缺少必要的依赖项...")
        
        # 创建一个简单的tkinter窗口询问是否安装
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        result = messagebox.askyesno(
            "缺少依赖项",
            "程序需要安装以下依赖项才能运行：\n\n"
            "- customtkinter (现代化GUI库)\n"
            "- pillow (图像处理库)\n\n"
            "是否现在自动安装？\n\n"
            "注意：需要网络连接"
        )
        
        root.destroy()
        
        if result:
            print("正在安装依赖项...")
            if install_dependencies():
                print("依赖项安装成功！")
                messagebox.showinfo("安装完成", "依赖项安装成功！\n程序将重新启动。")
                
                # 重新启动程序
                import subprocess
                subprocess.Popen([sys.executable] + sys.argv)
                sys.exit(0)
            else:
                messagebox.showerror(
                    "安装失败", 
                    "依赖项安装失败！\n\n"
                    "请手动运行以下命令安装：\n"
                    "pip install customtkinter pillow"
                )
                sys.exit(1)
        else:
            print("用户取消安装，程序退出")
            sys.exit(1)
    
    try:
        # 导入GUI模块
        from gui.main_window import MainWindow

        # 创建并运行主窗口（传递超级配置）
        app = MainWindow(super_config=super_config)
        app.mainloop()
        
    except Exception as e:
        # 如果GUI启动失败，显示错误信息
        root = tk.Tk()
        root.withdraw()
        
        messagebox.showerror(
            "启动失败",
            f"GUI启动失败：\n\n{str(e)}\n\n"
            "请检查是否正确安装了所有依赖项。"
        )
        
        print(f"GUI启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
