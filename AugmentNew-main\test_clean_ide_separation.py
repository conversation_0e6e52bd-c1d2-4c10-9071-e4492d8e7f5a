"""
测试清理功能的IDE区分
验证Cursor IDE和VSCode Augment插件的清理逻辑分离
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ide_detection_for_cleaning():
    """测试清理功能的IDE检测"""
    print("🔍 测试清理功能的IDE检测...")
    
    try:
        from utils.augment_account_resetter import AugmentAccountResetter
        
        # 测试不同IDE类型的检测
        for ide_type in ['vscode', 'cursor', 'auto']:
            print(f"\n--- 测试 {ide_type} 清理模式 ---")
            
            resetter = AugmentAccountResetter(ide_type)
            available_ides = resetter.get_detected_ides()
            target_info = resetter.get_reset_target_info()
            
            print(f"IDE类型: {ide_type}")
            print(f"检测到的IDE: {available_ides}")
            print(f"目标路径: {list(target_info['target_paths'].keys())}")
            print(f"警告: {target_info['warnings']}")
            
            # 模拟清理预览
            if ide_type == 'vscode' or (ide_type == 'auto' and available_ides['vscode']):
                print("  📝 VSCode清理项目:")
                print("    • Telemetry ID修改")
                print("    • 数据库清理")
                print("    • 工作区存储清理")
                print("    • Augment插件数据清理")
            
            if ide_type == 'cursor' or (ide_type == 'auto' and available_ides['cursor']):
                print("  🎯 Cursor清理项目:")
                print("    • 机器ID重置")
                print("    • 存储数据清理")
                print("    • 缓存数据清理")
                print("    • AI助手试用状态清理")
        
        print("\n✅ IDE检测功能测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_cursor_specific_cleaning():
    """测试Cursor特定的清理功能"""
    print("\n🎯 测试Cursor特定的清理功能...")
    
    try:
        from utils.cursor_ai_resetter import CursorAIResetter
        
        cursor_resetter = CursorAIResetter()
        
        print("Cursor重置器配置:")
        print(f"  • 重置策略数量: {len(cursor_resetter.reset_strategies)}")
        print(f"  • Cursor路径配置: {len(cursor_resetter.cursor_paths)}")
        
        # 测试重置策略
        for strategy_name, strategy_config in cursor_resetter.reset_strategies.items():
            print(f"  • {strategy_name}: {strategy_config['description']}")
        
        print("✅ Cursor特定清理功能测试完成")
        
    except Exception as e:
        print(f"❌ Cursor清理测试失败: {e}")

def test_vscode_specific_cleaning():
    """测试VSCode特定的清理功能"""
    print("\n📝 测试VSCode特定的清理功能...")
    
    try:
        # 测试VSCode相关的清理模块
        modules_to_test = [
            'augutils.telemetry_modifier',
            'augutils.database_cleaner', 
            'augutils.workspace_cleaner'
        ]
        
        for module_name in modules_to_test:
            try:
                module = __import__(module_name, fromlist=[''])
                print(f"  ✅ {module_name} 模块可用")
                
                # 检查主要函数
                if hasattr(module, 'modify_telemetry_id'):
                    print(f"    • modify_telemetry_id 函数可用")
                if hasattr(module, 'clean_database'):
                    print(f"    • clean_database 函数可用")
                if hasattr(module, 'clean_workspace_storage'):
                    print(f"    • clean_workspace_storage 函数可用")
                    
            except ImportError as e:
                print(f"  ❌ {module_name} 模块不可用: {e}")
        
        print("✅ VSCode特定清理功能测试完成")
        
    except Exception as e:
        print(f"❌ VSCode清理测试失败: {e}")

def test_ide_selector_dialog():
    """测试IDE选择对话框"""
    print("\n🖥️ 测试IDE选择对话框...")
    
    try:
        from gui.ide_selector_dialog import IDESelectorDialog
        from utils.augment_account_resetter import AugmentAccountResetter
        
        # 获取可用IDE
        resetter = AugmentAccountResetter("auto")
        available_ides = resetter.get_detected_ides()
        
        print(f"可用IDE: {available_ides}")
        print("IDE选择对话框类已加载")
        
        # 模拟对话框配置
        dialog_config = {
            'available_ides': available_ides,
            'options': ['vscode', 'cursor', 'auto'],
            'default_selection': 'auto'
        }
        
        print(f"对话框配置: {dialog_config}")
        print("✅ IDE选择对话框测试完成")
        
    except Exception as e:
        print(f"❌ IDE选择对话框测试失败: {e}")

def test_cleaning_path_separation():
    """测试清理路径分离"""
    print("\n📁 测试清理路径分离...")
    
    try:
        from utils.augment_account_resetter import AugmentAccountResetter
        
        # 测试VSCode路径
        vscode_resetter = AugmentAccountResetter("vscode")
        vscode_paths = vscode_resetter.augment_paths
        
        print("VSCode清理路径:")
        for key, path in vscode_paths.items():
            if 'vscode' in key.lower():
                print(f"  • {key}: {path}")
        
        # 测试Cursor路径
        cursor_resetter = AugmentAccountResetter("cursor")
        cursor_paths = cursor_resetter.augment_paths
        
        print("\nCursor清理路径:")
        for key, path in cursor_paths.items():
            if 'cursor' in key.lower():
                print(f"  • {key}: {path}")
        
        # 验证路径不重叠
        vscode_path_set = set(vscode_paths.values())
        cursor_path_set = set(cursor_paths.values())
        overlap = vscode_path_set.intersection(cursor_path_set)
        
        if overlap:
            print(f"⚠️ 发现路径重叠: {overlap}")
        else:
            print("✅ VSCode和Cursor路径完全分离")
        
        print("✅ 清理路径分离测试完成")
        
    except Exception as e:
        print(f"❌ 路径分离测试失败: {e}")

if __name__ == "__main__":
    print("🧪 开始测试清理功能的IDE区分...")
    
    test_ide_detection_for_cleaning()
    test_cursor_specific_cleaning()
    test_vscode_specific_cleaning()
    test_ide_selector_dialog()
    test_cleaning_path_separation()
    
    print("\n🎉 所有测试完成！")
    print("💡 清理功能现在能够正确区分Cursor IDE和VSCode Augment插件")
