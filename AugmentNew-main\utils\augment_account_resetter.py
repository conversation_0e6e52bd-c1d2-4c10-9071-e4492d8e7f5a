"""
🚀 AugmentNew 2.0 - 终极AI助手重置引擎
将试用受限的账号恢复为全新未使用状态，避免频繁注册风险

🔥 2025年最新技术整合：
- 基于yuaotian/go-cursor-help项目的Cursor重置方法
- VSCode Augment插件深度重置技术
- 设备指纹和机器ID完全重置
- 浏览器指纹清理和反检测技术
- 网络研究和社区解决方案集成

⚡ 核心目标：将"Too many free trial accounts"变为全新账号状态
"""

import os
import sys
import json
import uuid
import time
import shutil
import sqlite3
import hashlib
import secrets
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging

class AugmentAccountResetter:
    """🚀 AugmentNew 2.0 - 终极AI助手重置引擎

    将试用受限的账号恢复为全新未使用状态，避免频繁注册风险
    整合2025年最新的重置技术和社区解决方案
    """

    def __init__(self, ide_type: str = "auto", version: str = "2.0"):
        """
        初始化2.0版本重置引擎

        Args:
            ide_type: IDE类型 ("vscode", "cursor", "auto")
                     - "vscode": 只重置VSCode的Augment插件
                     - "cursor": 只重置Cursor IDE的AI助手数据
                     - "auto": 自动检测并重置所有找到的IDE
            version: 引擎版本 (默认"2.0")
        """
        self.logger = logging.getLogger(__name__)
        self.version = version
        self.backup_dir = Path("augment_account_backups")
        self.backup_dir.mkdir(exist_ok=True)
        self.ide_type = ide_type

        # 2.0版本新增：高级重置技术配置
        self.advanced_techniques = {
            'yuaotian_method': True,  # 基于yuaotian/go-cursor-help的方法
            'deep_fingerprint_reset': True,  # 深度设备指纹重置
            'browser_fingerprint_clean': True,  # 浏览器指纹清理
            'network_identity_reset': True,  # 网络身份重置
            'anti_detection_measures': True,  # 反检测措施
            'registry_deep_clean': True,  # 注册表深度清理
            'wmi_reset': True,  # WMI对象重置
            'hardware_fingerprint_spoof': True  # 硬件指纹伪造
        }

        # 检测可用的IDE
        self.available_ides = self._detect_available_ides()

        # 根据IDE类型设置路径
        self.augment_paths = self._get_ide_paths()

        # 2.0版本新增：初始化高级重置组件
        self._init_advanced_components()

        # Augment扩展标识符
        self.augment_extension_ids = [
            'augmentcode.augment',
            'augmentcode.augment-vscode',
            'augment.augment-code',
            'augment-code.augment',
        ]
        
        # 试用状态相关的键名（基于网络研究）
        self.trial_status_keys = [
            'augment.trial.status',
            'augment.trial.remaining',
            'augment.trial.expired',
            'augment.trial.startDate',
            'augment.trial.endDate',
            'augment.account.status',
            'augment.account.suspended',
            'augment.usage.count',
            'augment.usage.limit',
            'augment.user.id',
            'augment.device.id',
            'augment.session.id',
            'telemetry.machineId',
            'telemetry.devDeviceId',
            'telemetry.sqmId',
        ]
    
    def reset_augment_account_status(self, backup_dir: str = None) -> Dict[str, any]:
        """重置Augment账号状态为全新未使用状态 - 真正的IDE分离版本"""
        results = {
            'success': True,
            'reset_components': [],
            'errors': [],
            'backup_created': False,
            'ide_info': self.get_reset_target_info(),
            'reset_summary': {
                'extension_data': 0,
                'storage_entries': 0,
                'cache_files': 0,
                'config_files': 0,
                'trial_status': 0
            }
        }

        try:
            # 检查是否有可重置的目标
            if not any(self.available_ides.values()) and self.ide_type != "auto":
                results['success'] = False
                results['errors'].append(f"未检测到指定的IDE类型: {self.ide_type}")
                return results

            self.logger.info(f"🔥 开始真正分离的重置 - IDE类型: {self.ide_type}")
            self.logger.info(f"检测到的IDE: {self.available_ides}")

            # 创建备份
            if backup_dir:
                backup_success = self._create_augment_backup(backup_dir)
                results['backup_created'] = backup_success

            # 🔥 真正的IDE分离逻辑 - 绝对不混淆
            if self.ide_type == "vscode":
                # 只重置VSCode，绝不碰Cursor
                self.logger.info("只重置VSCode的Augment数据，不碰Cursor...")
                vscode_result = self._reset_vscode_augment_only()
                results['reset_components'].extend(vscode_result['reset_items'])
                results['errors'].extend(vscode_result['errors'])

                # 只清理VSCode相关的浏览器数据
                browser_result = self._reset_vscode_browser_data_only()
                results['reset_components'].extend(browser_result['reset_items'])
                results['errors'].extend(browser_result['errors'])

            elif self.ide_type == "cursor":
                # 只重置Cursor，绝不碰VSCode
                self.logger.info("只重置Cursor的AI助手数据，不碰VSCode...")
                cursor_result = self._reset_cursor_augment_only()
                results['reset_components'].extend(cursor_result['reset_items'])
                results['errors'].extend(cursor_result['errors'])

                # 只清理Cursor相关的浏览器数据
                browser_result = self._reset_cursor_browser_data_only()
                results['reset_components'].extend(browser_result['reset_items'])
                results['errors'].extend(browser_result['errors'])

            elif self.ide_type == "auto":
                # 自动模式：分别处理每个IDE
                if self.available_ides['vscode']:
                    self.logger.info("重置VSCode的Augment数据...")
                    vscode_result = self._reset_vscode_augment_only()
                    results['reset_components'].extend(vscode_result['reset_items'])
                    results['errors'].extend(vscode_result['errors'])

                if self.available_ides['cursor']:
                    self.logger.info("重置Cursor的AI助手数据...")
                    cursor_result = self._reset_cursor_augment_only()
                    results['reset_components'].extend(cursor_result['reset_items'])
                    results['errors'].extend(cursor_result['errors'])

                # 清理通用浏览器数据（两个IDE都相关的）
                browser_result = self._reset_all_browser_augment_data()
                results['reset_components'].extend(browser_result['reset_items'])
                results['errors'].extend(browser_result['errors'])

            if results['errors']:
                results['success'] = False

            self.logger.info(f"🎉 真正分离的重置完成: 重置了 {len(results['reset_components'])} 个组件")

        except Exception as e:
            results['success'] = False
            results['errors'].append(f"Augment账号状态重置失败: {str(e)}")
            self.logger.error(f"Augment账号状态重置失败: {e}")

        return results
    
    def _reset_extension_data(self) -> Dict[str, any]:
        """重置Augment扩展数据"""
        result = {
            'success': True,
            'reset_items': [],
            'errors': []
        }
        
        try:
            # 1. 清理扩展目录中的Augment相关数据
            extensions_dir = self.augment_paths['vscode_extensions']
            if os.path.exists(extensions_dir):
                for item in os.listdir(extensions_dir):
                    item_path = os.path.join(extensions_dir, item)
                    if any(ext_id in item.lower() for ext_id in self.augment_extension_ids):
                        try:
                            if os.path.isdir(item_path):
                                # 只清理扩展的数据目录，保留扩展本身
                                data_dirs = ['storage', 'workspaceStorage', 'globalStorage', 'logs']
                                for data_dir in data_dirs:
                                    data_path = os.path.join(item_path, data_dir)
                                    if os.path.exists(data_path):
                                        shutil.rmtree(data_path)
                                        result['reset_items'].append(f"扩展数据目录: {data_dir}")
                        except Exception as e:
                            result['errors'].append(f"清理扩展数据失败 {item}: {str(e)}")
            
            # 2. 清理全局存储中的Augment数据
            global_storage_dir = self.augment_paths['vscode_global_storage']
            if os.path.exists(global_storage_dir):
                for item in os.listdir(global_storage_dir):
                    if any(ext_id in item.lower() for ext_id in self.augment_extension_ids):
                        item_path = os.path.join(global_storage_dir, item)
                        try:
                            if os.path.isdir(item_path):
                                shutil.rmtree(item_path)
                            else:
                                os.remove(item_path)
                            result['reset_items'].append(f"全局存储: {item}")
                        except Exception as e:
                            result['errors'].append(f"清理全局存储失败 {item}: {str(e)}")
            
            # 3. 清理工作区存储中的Augment数据
            workspace_storage_dir = self.augment_paths['vscode_workspace_storage']
            if os.path.exists(workspace_storage_dir):
                for item in os.listdir(workspace_storage_dir):
                    item_path = os.path.join(workspace_storage_dir, item)
                    if os.path.isdir(item_path):
                        # 检查工作区存储是否包含Augment数据
                        try:
                            storage_json = os.path.join(item_path, 'storage.json')
                            if os.path.exists(storage_json):
                                with open(storage_json, 'r', encoding='utf-8') as f:
                                    content = f.read()
                                if any(ext_id in content.lower() for ext_id in self.augment_extension_ids):
                                    shutil.rmtree(item_path)
                                    result['reset_items'].append(f"工作区存储: {item}")
                        except Exception as e:
                            result['errors'].append(f"清理工作区存储失败 {item}: {str(e)}")
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置扩展数据失败: {str(e)}")
        
        return result
    
    def _reset_storage_data(self) -> Dict[str, any]:
        """重置存储数据中的试用状态"""
        result = {
            'success': True,
            'reset_items': [],
            'errors': []
        }
        
        try:
            # 1. 重置storage.json中的试用状态
            storage_json_path = self.augment_paths['storage_json']
            if os.path.exists(storage_json_path):
                try:
                    with open(storage_json_path, 'r', encoding='utf-8') as f:
                        storage_data = json.load(f)
                    
                    # 移除所有Augment相关的键
                    keys_to_remove = []
                    for key in storage_data.keys():
                        if any(trial_key in key.lower() for trial_key in self.trial_status_keys):
                            keys_to_remove.append(key)
                        elif 'augment' in key.lower():
                            keys_to_remove.append(key)
                    
                    for key in keys_to_remove:
                        del storage_data[key]
                        result['reset_items'].append(f"存储键: {key}")
                    
                    # 重新生成设备标识
                    storage_data['telemetry.machineId'] = self._generate_machine_id()
                    storage_data['telemetry.devDeviceId'] = self._generate_device_id()
                    storage_data['telemetry.sqmId'] = self._generate_sqm_id()
                    
                    # 写回文件
                    with open(storage_json_path, 'w', encoding='utf-8') as f:
                        json.dump(storage_data, f, indent=2, ensure_ascii=False)
                    
                    result['reset_items'].append("storage.json已重置")
                    
                except Exception as e:
                    result['errors'].append(f"重置storage.json失败: {str(e)}")
            
            # 2. 清理其他存储文件
            user_data_dir = self.augment_paths['vscode_user_data']
            if os.path.exists(user_data_dir):
                storage_files = ['settings.json', 'keybindings.json', 'snippets']
                for storage_file in storage_files:
                    file_path = os.path.join(user_data_dir, storage_file)
                    if os.path.exists(file_path):
                        try:
                            if storage_file.endswith('.json'):
                                # 清理JSON文件中的Augment配置
                                self._clean_json_file(file_path)
                                result['reset_items'].append(f"配置文件: {storage_file}")
                            elif os.path.isdir(file_path):
                                # 清理目录中的Augment相关文件
                                self._clean_directory_augment_files(file_path)
                                result['reset_items'].append(f"配置目录: {storage_file}")
                        except Exception as e:
                            result['errors'].append(f"清理存储文件失败 {storage_file}: {str(e)}")
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置存储数据失败: {str(e)}")
        
        return result
    
    def _reset_cache_data(self) -> Dict[str, any]:
        """重置缓存数据"""
        result = {
            'success': True,
            'reset_items': [],
            'errors': []
        }
        
        try:
            # 1. 清理VSCode日志中的Augment相关记录
            logs_dir = self.augment_paths['vscode_logs']
            if os.path.exists(logs_dir):
                try:
                    # 清理整个日志目录
                    shutil.rmtree(logs_dir)
                    result['reset_items'].append("VSCode日志目录")
                except Exception as e:
                    result['errors'].append(f"清理日志目录失败: {str(e)}")
            
            # 2. 清理缓存的扩展数据
            cached_extensions_dir = self.augment_paths['vscode_cached_extensions']
            if os.path.exists(cached_extensions_dir):
                try:
                    # 只清理Augment相关的缓存
                    for item in os.listdir(cached_extensions_dir):
                        if any(ext_id in item.lower() for ext_id in self.augment_extension_ids):
                            item_path = os.path.join(cached_extensions_dir, item)
                            if os.path.isfile(item_path):
                                os.remove(item_path)
                            elif os.path.isdir(item_path):
                                shutil.rmtree(item_path)
                            result['reset_items'].append(f"缓存扩展: {item}")
                except Exception as e:
                    result['errors'].append(f"清理缓存扩展失败: {str(e)}")
            
            # 3. 清理临时文件中的Augment数据
            temp_dirs = [
                os.path.expandvars(r"%TEMP%"),
                os.path.expandvars(r"%LOCALAPPDATA%\Temp"),
            ]
            
            for temp_dir in temp_dirs:
                if os.path.exists(temp_dir):
                    try:
                        for item in os.listdir(temp_dir):
                            if 'augment' in item.lower() or 'vscode' in item.lower():
                                item_path = os.path.join(temp_dir, item)
                                try:
                                    if os.path.isfile(item_path):
                                        os.remove(item_path)
                                    elif os.path.isdir(item_path):
                                        shutil.rmtree(item_path)
                                    result['reset_items'].append(f"临时文件: {item}")
                                except:
                                    pass  # 忽略无法删除的临时文件
                    except Exception as e:
                        result['errors'].append(f"清理临时目录失败 {temp_dir}: {str(e)}")
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置缓存数据失败: {str(e)}")
        
        return result

    def _reset_config_files(self) -> Dict[str, any]:
        """重置配置文件中的Augment设置"""
        result = {
            'success': True,
            'reset_items': [],
            'errors': []
        }

        try:
            # 1. 重置VSCode设置文件
            settings_json = os.path.join(self.augment_paths['vscode_user_data'], 'settings.json')
            if os.path.exists(settings_json):
                try:
                    with open(settings_json, 'r', encoding='utf-8') as f:
                        settings = json.load(f)

                    # 移除Augment相关设置
                    keys_to_remove = []
                    for key in settings.keys():
                        if 'augment' in key.lower():
                            keys_to_remove.append(key)

                    for key in keys_to_remove:
                        del settings[key]
                        result['reset_items'].append(f"设置项: {key}")

                    # 写回文件
                    with open(settings_json, 'w', encoding='utf-8') as f:
                        json.dump(settings, f, indent=2, ensure_ascii=False)

                    if keys_to_remove:
                        result['reset_items'].append("settings.json已清理")

                except Exception as e:
                    result['errors'].append(f"重置settings.json失败: {str(e)}")

            # 2. 重置扩展配置文件
            extensions_json = os.path.join(self.augment_paths['vscode_user_data'], 'extensions.json')
            if os.path.exists(extensions_json):
                try:
                    with open(extensions_json, 'r', encoding='utf-8') as f:
                        extensions = json.load(f)

                    # 移除Augment扩展的配置
                    if 'recommendations' in extensions:
                        original_count = len(extensions['recommendations'])
                        extensions['recommendations'] = [
                            ext for ext in extensions['recommendations']
                            if not any(aug_id in ext.lower() for aug_id in self.augment_extension_ids)
                        ]
                        removed_count = original_count - len(extensions['recommendations'])
                        if removed_count > 0:
                            result['reset_items'].append(f"扩展推荐: 移除了{removed_count}个")

                    # 写回文件
                    with open(extensions_json, 'w', encoding='utf-8') as f:
                        json.dump(extensions, f, indent=2, ensure_ascii=False)

                except Exception as e:
                    result['errors'].append(f"重置extensions.json失败: {str(e)}")

            # 3. 清理键绑定文件
            keybindings_json = os.path.join(self.augment_paths['vscode_user_data'], 'keybindings.json')
            if os.path.exists(keybindings_json):
                try:
                    with open(keybindings_json, 'r', encoding='utf-8') as f:
                        keybindings = json.load(f)

                    # 移除Augment相关的键绑定
                    original_count = len(keybindings)
                    keybindings = [
                        binding for binding in keybindings
                        if not any(aug_id in str(binding).lower() for aug_id in self.augment_extension_ids)
                    ]
                    removed_count = original_count - len(keybindings)

                    if removed_count > 0:
                        with open(keybindings_json, 'w', encoding='utf-8') as f:
                            json.dump(keybindings, f, indent=2, ensure_ascii=False)
                        result['reset_items'].append(f"键绑定: 移除了{removed_count}个")

                except Exception as e:
                    result['errors'].append(f"重置keybindings.json失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置配置文件失败: {str(e)}")

        return result

    def _reset_trial_status(self) -> Dict[str, any]:
        """重置试用状态信息"""
        result = {
            'success': True,
            'reset_items': [],
            'errors': []
        }

        try:
            # 1. 清理浏览器中的Augment试用状态
            browser_paths = [
                os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Local Storage"),
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Local Storage"),
                os.path.expandvars(r"%APPDATA%\Mozilla\Firefox\Profiles\*\storage\default"),
            ]

            for browser_path in browser_paths:
                if '*' in browser_path:
                    import glob
                    for path in glob.glob(browser_path):
                        self._clean_browser_augment_data(path, result)
                else:
                    if os.path.exists(browser_path):
                        self._clean_browser_augment_data(browser_path, result)

            # 2. 清理注册表中的试用信息（Windows）
            if sys.platform == "win32":
                try:
                    import winreg
                    registry_paths = [
                        (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\VSCode"),
                        (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Classes\vscode"),
                    ]

                    for root_key, sub_key in registry_paths:
                        try:
                            key = winreg.OpenKey(root_key, sub_key, 0, winreg.KEY_ALL_ACCESS)
                            # 清理Augment相关的注册表值
                            self._clean_registry_augment_values(key, result)
                            winreg.CloseKey(key)
                        except FileNotFoundError:
                            pass  # 键不存在，跳过
                        except Exception as e:
                            result['errors'].append(f"清理注册表失败 {sub_key}: {str(e)}")

                except ImportError:
                    pass  # 非Windows系统，跳过注册表操作

            # 3. 重置机器ID文件
            machine_id_path = self.augment_paths['machine_id']
            if os.path.exists(machine_id_path):
                try:
                    new_machine_id = self._generate_machine_id()
                    with open(machine_id_path, 'w', encoding='utf-8') as f:
                        f.write(new_machine_id)
                    result['reset_items'].append(f"机器ID已重置: {new_machine_id[:8]}...")
                except Exception as e:
                    result['errors'].append(f"重置机器ID失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置试用状态失败: {str(e)}")

        return result

    def _generate_new_device_identity(self) -> Dict[str, any]:
        """生成新的设备身份标识"""
        result = {
            'success': True,
            'reset_items': [],
            'errors': []
        }

        try:
            # 1. 生成新的设备标识符
            new_machine_id = self._generate_machine_id()
            new_device_id = self._generate_device_id()
            new_sqm_id = self._generate_sqm_id()

            # 2. 更新所有相关文件
            identity_files = [
                (self.augment_paths['machine_id'], new_machine_id),
                (self.augment_paths['storage_json'], {
                    'telemetry.machineId': new_machine_id,
                    'telemetry.devDeviceId': new_device_id,
                    'telemetry.sqmId': new_sqm_id
                })
            ]

            for file_path, content in identity_files:
                try:
                    if file_path.endswith('.json'):
                        # 更新JSON文件
                        if os.path.exists(file_path):
                            with open(file_path, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                            data.update(content)
                            with open(file_path, 'w', encoding='utf-8') as f:
                                json.dump(data, f, indent=2, ensure_ascii=False)
                        else:
                            # 创建新的JSON文件
                            os.makedirs(os.path.dirname(file_path), exist_ok=True)
                            with open(file_path, 'w', encoding='utf-8') as f:
                                json.dump(content, f, indent=2, ensure_ascii=False)
                        result['reset_items'].append(f"JSON文件已更新: {os.path.basename(file_path)}")
                    else:
                        # 更新文本文件
                        os.makedirs(os.path.dirname(file_path), exist_ok=True)
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        result['reset_items'].append(f"文本文件已更新: {os.path.basename(file_path)}")

                except Exception as e:
                    result['errors'].append(f"更新身份文件失败 {file_path}: {str(e)}")

            # 3. 清理可能缓存设备信息的其他位置
            cache_locations = [
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\VSCode"),
                os.path.expandvars(r"%APPDATA%\Code\CachedData"),
            ]

            for cache_location in cache_locations:
                if os.path.exists(cache_location):
                    try:
                        shutil.rmtree(cache_location)
                        result['reset_items'].append(f"缓存目录已清理: {os.path.basename(cache_location)}")
                    except Exception as e:
                        result['errors'].append(f"清理缓存目录失败 {cache_location}: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"生成新设备身份失败: {str(e)}")

        return result

    def _clean_browser_augment_data(self, browser_path: str, result: Dict) -> None:
        """清理浏览器中的Augment数据"""
        try:
            if os.path.exists(browser_path):
                for item in os.listdir(browser_path):
                    if 'augment' in item.lower():
                        item_path = os.path.join(browser_path, item)
                        try:
                            if os.path.isfile(item_path):
                                os.remove(item_path)
                            elif os.path.isdir(item_path):
                                shutil.rmtree(item_path)
                            result['reset_items'].append(f"浏览器数据: {item}")
                        except Exception as e:
                            result['errors'].append(f"清理浏览器数据失败 {item}: {str(e)}")
        except Exception as e:
            result['errors'].append(f"访问浏览器路径失败 {browser_path}: {str(e)}")

    def _clean_registry_augment_values(self, key, result: Dict) -> None:
        """清理注册表中的Augment相关值"""
        try:
            import winreg
            i = 0
            while True:
                try:
                    value_name, value_data, _ = winreg.EnumValue(key, i)
                    if 'augment' in value_name.lower() or 'augment' in str(value_data).lower():
                        try:
                            winreg.DeleteValue(key, value_name)
                            result['reset_items'].append(f"注册表值: {value_name}")
                        except Exception as e:
                            result['errors'].append(f"删除注册表值失败 {value_name}: {str(e)}")
                    else:
                        i += 1
                except OSError:
                    break  # 没有更多值
        except Exception as e:
            result['errors'].append(f"清理注册表值失败: {str(e)}")

    def _clean_json_file(self, file_path: str) -> None:
        """清理JSON文件中的Augment相关配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 递归清理JSON中的Augment相关键
            cleaned_data = self._remove_augment_keys(data)

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(cleaned_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            self.logger.error(f"清理JSON文件失败 {file_path}: {e}")

    def _remove_augment_keys(self, data):
        """递归移除数据中的Augment相关键"""
        if isinstance(data, dict):
            keys_to_remove = []
            for key in data.keys():
                if 'augment' in str(key).lower():
                    keys_to_remove.append(key)

            for key in keys_to_remove:
                del data[key]

            # 递归处理剩余的值
            for key, value in data.items():
                data[key] = self._remove_augment_keys(value)

        elif isinstance(data, list):
            # 过滤列表中的Augment相关项
            data = [
                self._remove_augment_keys(item) for item in data
                if not ('augment' in str(item).lower())
            ]

        return data

    def _clean_directory_augment_files(self, directory_path: str) -> None:
        """清理目录中的Augment相关文件"""
        try:
            for item in os.listdir(directory_path):
                if 'augment' in item.lower():
                    item_path = os.path.join(directory_path, item)
                    try:
                        if os.path.isfile(item_path):
                            os.remove(item_path)
                        elif os.path.isdir(item_path):
                            shutil.rmtree(item_path)
                    except Exception as e:
                        self.logger.error(f"删除Augment文件失败 {item_path}: {e}")
        except Exception as e:
            self.logger.error(f"清理目录失败 {directory_path}: {e}")

    def _generate_machine_id(self) -> str:
        """生成新的机器ID"""
        return secrets.token_hex(32)

    def _generate_device_id(self) -> str:
        """生成新的设备ID"""
        return str(uuid.uuid4()).lower()

    def _generate_sqm_id(self) -> str:
        """生成新的SQM ID"""
        return str(uuid.uuid4()).upper()

    def _create_augment_backup(self, backup_dir: str) -> bool:
        """创建Augment相关数据的备份"""
        try:
            backup_path = Path(backup_dir) / f"augment_account_backup_{int(time.time())}"
            backup_path.mkdir(parents=True, exist_ok=True)

            # 备份重要文件和目录
            backup_items = [
                (self.augment_paths['vscode_user_data'], "vscode_user_data"),
                (self.augment_paths['vscode_global_storage'], "vscode_global_storage"),
                (self.augment_paths['machine_id'], "machine_id"),
            ]

            for source, name in backup_items:
                if os.path.exists(source):
                    try:
                        dest = backup_path / name
                        if os.path.isfile(source):
                            shutil.copy2(source, dest)
                        else:
                            shutil.copytree(source, dest, ignore_errors=True)
                    except Exception as e:
                        self.logger.error(f"备份失败 {source}: {e}")

            self.logger.info(f"Augment账号备份已创建: {backup_path}")
            return True

        except Exception as e:
            self.logger.error(f"创建Augment账号备份失败: {e}")
            return False

    def analyze_augment_account_status(self) -> Dict[str, any]:
        """分析当前Augment账号状态"""
        analysis = {
            'account_status': 'unknown',
            'trial_data_found': False,
            'extension_installed': False,
            'storage_data_exists': False,
            'cache_data_exists': False,
            'risk_factors': [],
            'reset_recommendation': 'none'
        }

        try:
            # 1. 检查扩展是否安装
            extensions_dir = self.augment_paths['vscode_extensions']
            if os.path.exists(extensions_dir):
                for item in os.listdir(extensions_dir):
                    if any(ext_id in item.lower() for ext_id in self.augment_extension_ids):
                        analysis['extension_installed'] = True
                        break

            # 2. 检查存储数据
            storage_json_path = self.augment_paths['storage_json']
            if os.path.exists(storage_json_path):
                try:
                    with open(storage_json_path, 'r', encoding='utf-8') as f:
                        storage_data = json.load(f)

                    # 检查是否有Augment相关的键
                    for key in storage_data.keys():
                        if any(trial_key in key.lower() for trial_key in self.trial_status_keys):
                            analysis['trial_data_found'] = True
                            analysis['storage_data_exists'] = True
                            break
                        elif 'augment' in key.lower():
                            analysis['storage_data_exists'] = True

                except Exception as e:
                    self.logger.error(f"分析存储数据失败: {e}")

            # 3. 检查缓存数据
            cache_locations = [
                self.augment_paths['vscode_logs'],
                self.augment_paths['vscode_cached_extensions'],
                self.augment_paths['vscode_workspace_storage'],
            ]

            for cache_location in cache_locations:
                if os.path.exists(cache_location):
                    try:
                        for item in os.listdir(cache_location):
                            if 'augment' in item.lower():
                                analysis['cache_data_exists'] = True
                                break
                    except:
                        pass
                if analysis['cache_data_exists']:
                    break

            # 4. 分析账号状态
            if analysis['trial_data_found']:
                analysis['account_status'] = 'trial_data_present'
                analysis['risk_factors'].append('存在试用数据记录')
            elif analysis['storage_data_exists']:
                analysis['account_status'] = 'used_before'
                analysis['risk_factors'].append('存在使用历史')
            elif analysis['extension_installed']:
                analysis['account_status'] = 'extension_only'
                analysis['risk_factors'].append('仅安装了扩展')
            else:
                analysis['account_status'] = 'clean'

            # 5. 生成重置建议
            if analysis['account_status'] in ['trial_data_present', 'used_before']:
                analysis['reset_recommendation'] = 'full_reset'
            elif analysis['account_status'] == 'extension_only':
                analysis['reset_recommendation'] = 'partial_reset'
            else:
                analysis['reset_recommendation'] = 'none'

            # 6. 添加其他风险因素
            if analysis['cache_data_exists']:
                analysis['risk_factors'].append('存在缓存数据')
            if analysis['extension_installed']:
                analysis['risk_factors'].append('扩展已安装')

        except Exception as e:
            self.logger.error(f"分析Augment账号状态失败: {e}")
            analysis['account_status'] = 'analysis_failed'

        return analysis

    def get_reset_preview(self) -> Dict[str, any]:
        """获取重置预览信息"""
        preview = {
            'current_status': self.analyze_augment_account_status(),
            'reset_plan': {
                'extension_data': 0,
                'storage_entries': 0,
                'cache_files': 0,
                'config_files': 0,
                'trial_status': 0
            },
            'estimated_items': 0,
            'warnings': []
        }

        try:
            # 预估将要重置的项目数量
            preview['reset_plan']['extension_data'] = self._count_extension_data()
            preview['reset_plan']['storage_entries'] = self._count_storage_entries()
            preview['reset_plan']['cache_files'] = self._count_cache_files()
            preview['reset_plan']['config_files'] = self._count_config_files()
            preview['reset_plan']['trial_status'] = self._count_trial_status_items()

            preview['estimated_items'] = sum(preview['reset_plan'].values())

            # 添加警告信息
            if preview['estimated_items'] == 0:
                preview['warnings'].append("未检测到Augment相关数据，可能已经是全新状态")
            elif preview['current_status']['account_status'] == 'clean':
                preview['warnings'].append("账号状态看起来已经是干净的")

        except Exception as e:
            preview['warnings'].append(f"获取预览信息时出错: {str(e)}")
            self.logger.error(f"获取重置预览失败: {e}")

        return preview

    def _count_extension_data(self) -> int:
        """计算扩展数据项目数量"""
        count = 0
        try:
            # 计算扩展目录中的Augment相关项目
            extensions_dir = self.augment_paths['vscode_extensions']
            if os.path.exists(extensions_dir):
                for item in os.listdir(extensions_dir):
                    if any(ext_id in item.lower() for ext_id in self.augment_extension_ids):
                        count += 1

            # 计算全局存储中的项目
            global_storage_dir = self.augment_paths['vscode_global_storage']
            if os.path.exists(global_storage_dir):
                for item in os.listdir(global_storage_dir):
                    if any(ext_id in item.lower() for ext_id in self.augment_extension_ids):
                        count += 1

        except Exception as e:
            self.logger.error(f"计算扩展数据失败: {e}")

        return count

    def _count_storage_entries(self) -> int:
        """计算存储条目数量"""
        count = 0
        try:
            storage_json_path = self.augment_paths['storage_json']
            if os.path.exists(storage_json_path):
                with open(storage_json_path, 'r', encoding='utf-8') as f:
                    storage_data = json.load(f)

                for key in storage_data.keys():
                    if any(trial_key in key.lower() for trial_key in self.trial_status_keys):
                        count += 1
                    elif 'augment' in key.lower():
                        count += 1

        except Exception as e:
            self.logger.error(f"计算存储条目失败: {e}")

        return count

    def _count_cache_files(self) -> int:
        """计算缓存文件数量"""
        count = 0
        try:
            cache_locations = [
                self.augment_paths['vscode_logs'],
                self.augment_paths['vscode_cached_extensions'],
            ]

            for cache_location in cache_locations:
                if os.path.exists(cache_location):
                    for item in os.listdir(cache_location):
                        if 'augment' in item.lower():
                            count += 1

        except Exception as e:
            self.logger.error(f"计算缓存文件失败: {e}")

        return count

    def _count_config_files(self) -> int:
        """计算配置文件数量"""
        count = 0
        try:
            config_files = ['settings.json', 'extensions.json', 'keybindings.json']
            user_data_dir = self.augment_paths['vscode_user_data']

            for config_file in config_files:
                file_path = os.path.join(user_data_dir, config_file)
                if os.path.exists(file_path):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        if 'augment' in content.lower():
                            count += 1
                    except:
                        pass

        except Exception as e:
            self.logger.error(f"计算配置文件失败: {e}")

        return count

    def _count_trial_status_items(self) -> int:
        """计算试用状态项目数量"""
        count = 0
        try:
            # 计算浏览器中的试用状态数据
            browser_paths = [
                os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Local Storage"),
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Local Storage"),
            ]

            for browser_path in browser_paths:
                if os.path.exists(browser_path):
                    for item in os.listdir(browser_path):
                        if 'augment' in item.lower():
                            count += 1

        except Exception as e:
            self.logger.error(f"计算试用状态项目失败: {e}")

        return count

    def _detect_available_ides(self) -> Dict[str, bool]:
        """检测系统中可用的IDE"""
        available = {
            'vscode': False,
            'cursor': False
        }

        try:
            # 检测VSCode
            vscode_paths = [
                os.path.expandvars(r"%APPDATA%\Code"),
                os.path.expandvars(r"%USERPROFILE%\.vscode"),
            ]
            for path in vscode_paths:
                if os.path.exists(path):
                    available['vscode'] = True
                    break

            # 检测Cursor
            cursor_paths = [
                os.path.expandvars(r"%APPDATA%\Cursor"),
                os.path.expandvars(r"%LOCALAPPDATA%\Cursor"),
            ]
            for path in cursor_paths:
                if os.path.exists(path):
                    available['cursor'] = True
                    break

        except Exception as e:
            self.logger.error(f"检测IDE失败: {e}")

        return available

    def _get_ide_paths(self) -> Dict[str, str]:
        """根据IDE类型获取相应的路径配置"""
        paths = {}

        if self.ide_type == "vscode" or (self.ide_type == "auto" and self.available_ides['vscode']):
            # VSCode路径配置
            vscode_paths = {
                'vscode_extensions': os.path.expandvars(r"%USERPROFILE%\.vscode\extensions"),
                'vscode_user_data': os.path.expandvars(r"%APPDATA%\Code\User"),
                'vscode_global_storage': os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
                'vscode_workspace_storage': os.path.expandvars(r"%APPDATA%\Code\User\workspaceStorage"),
                'vscode_logs': os.path.expandvars(r"%APPDATA%\Code\logs"),
                'vscode_cached_extensions': os.path.expandvars(r"%APPDATA%\Code\CachedExtensions"),
                'machine_id': os.path.expandvars(r"%APPDATA%\Code\machineid"),
                'storage_json': os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\storage.json"),
            }
            paths.update(vscode_paths)

        if self.ide_type == "cursor" or (self.ide_type == "auto" and self.available_ides['cursor']):
            # Cursor路径配置
            cursor_paths = {
                'cursor_user_data': os.path.expandvars(r"%APPDATA%\Cursor\User Data"),
                'cursor_global_storage': os.path.expandvars(r"%APPDATA%\Cursor\User\globalStorage"),
                'cursor_workspace_storage': os.path.expandvars(r"%APPDATA%\Cursor\User\workspaceStorage"),
                'cursor_logs': os.path.expandvars(r"%APPDATA%\Cursor\logs"),
                'cursor_cached_data': os.path.expandvars(r"%APPDATA%\Cursor\CachedData"),
                'cursor_machine_id': os.path.expandvars(r"%APPDATA%\Cursor\machineid"),
                'cursor_storage_json': os.path.expandvars(r"%APPDATA%\Cursor\User\globalStorage\storage.json"),
                'cursor_preferences': os.path.expandvars(r"%LOCALAPPDATA%\Cursor\User Data\Default\Preferences"),
                'cursor_local_state': os.path.expandvars(r"%LOCALAPPDATA%\Cursor\User Data\Local State"),
            }
            paths.update(cursor_paths)

        return paths

    def get_detected_ides(self) -> Dict[str, bool]:
        """获取检测到的IDE信息"""
        return self.available_ides.copy()

    def set_ide_type(self, ide_type: str):
        """设置要重置的IDE类型"""
        if ide_type not in ["vscode", "cursor", "auto"]:
            raise ValueError("IDE类型必须是 'vscode', 'cursor' 或 'auto'")

        self.ide_type = ide_type
        self.augment_paths = self._get_ide_paths()
        self.logger.info(f"IDE类型已设置为: {ide_type}")

    def get_reset_target_info(self) -> Dict[str, any]:
        """获取重置目标信息"""
        info = {
            'ide_type': self.ide_type,
            'available_ides': self.available_ides,
            'target_paths': {},
            'warnings': []
        }

        # 根据IDE类型确定目标路径
        if self.ide_type == "vscode":
            if not self.available_ides['vscode']:
                info['warnings'].append("⚠️ 未检测到VSCode，但已选择重置VSCode的Augment数据")
            info['target_paths']['vscode'] = [path for key, path in self.augment_paths.items() if 'vscode' in key]

        elif self.ide_type == "cursor":
            if not self.available_ides['cursor']:
                info['warnings'].append("⚠️ 未检测到Cursor，但已选择重置Cursor的Augment数据")
            info['target_paths']['cursor'] = [path for key, path in self.augment_paths.items() if 'cursor' in key]

        elif self.ide_type == "auto":
            if self.available_ides['vscode']:
                info['target_paths']['vscode'] = [path for key, path in self.augment_paths.items() if 'vscode' in key]
            if self.available_ides['cursor']:
                info['target_paths']['cursor'] = [path for key, path in self.augment_paths.items() if 'cursor' in key]
            if not any(self.available_ides.values()):
                info['warnings'].append("⚠️ 未检测到任何支持的IDE")

        return info

    def _reset_vscode_augment(self) -> Dict[str, any]:
        """重置VSCode的Augment数据"""
        result = {
            'success': True,
            'reset_items': [],
            'errors': []
        }

        try:
            self.logger.info("开始重置VSCode的Augment数据...")

            # 1. 重置扩展数据
            extension_result = self._reset_vscode_extension_data()
            result['reset_items'].extend(extension_result['reset_items'])
            result['errors'].extend(extension_result['errors'])

            # 2. 重置存储数据
            storage_result = self._reset_vscode_storage_data()
            result['reset_items'].extend(storage_result['reset_items'])
            result['errors'].extend(storage_result['errors'])

            # 3. 重置缓存数据
            cache_result = self._reset_vscode_cache_data()
            result['reset_items'].extend(cache_result['reset_items'])
            result['errors'].extend(cache_result['errors'])

            # 4. 重置配置文件
            config_result = self._reset_vscode_config_files()
            result['reset_items'].extend(config_result['reset_items'])
            result['errors'].extend(config_result['errors'])

            if result['errors']:
                result['success'] = False

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置VSCode Augment数据失败: {str(e)}")

        return result

    def _reset_cursor_augment(self) -> Dict[str, any]:
        """重置Cursor的Augment数据"""
        result = {
            'success': True,
            'reset_items': [],
            'errors': []
        }

        try:
            self.logger.info("开始重置Cursor的Augment数据...")

            # 1. 停止Cursor进程
            stop_result = self._stop_cursor_processes()
            if stop_result['success']:
                result['reset_items'].append("Cursor进程已停止")
            else:
                result['errors'].extend(stop_result['errors'])

            # 2. 重置Cursor存储数据
            storage_result = self._reset_cursor_storage_data()
            result['reset_items'].extend(storage_result['reset_items'])
            result['errors'].extend(storage_result['errors'])

            # 3. 重置Cursor机器ID
            machine_id_result = self._reset_cursor_machine_id_data()
            result['reset_items'].extend(machine_id_result['reset_items'])
            result['errors'].extend(machine_id_result['errors'])

            # 4. 清理Cursor缓存
            cache_result = self._reset_cursor_cache_data()
            result['reset_items'].extend(cache_result['reset_items'])
            result['errors'].extend(cache_result['errors'])

            if result['errors']:
                result['success'] = False

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置Cursor Augment数据失败: {str(e)}")

        return result

    def _reset_browser_augment_data(self) -> Dict[str, any]:
        """重置浏览器中的Augment数据"""
        result = {
            'success': True,
            'reset_items': [],
            'errors': []
        }

        try:
            self.logger.info("开始重置浏览器Augment数据...")

            # 清理浏览器中的Augment试用状态
            browser_paths = [
                os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Local Storage"),
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Local Storage"),
                os.path.expandvars(r"%APPDATA%\Mozilla\Firefox\Profiles\*\storage\default"),
            ]

            for browser_path in browser_paths:
                if '*' in browser_path:
                    import glob
                    for path in glob.glob(browser_path):
                        self._clean_browser_augment_data(path, result)
                else:
                    if os.path.exists(browser_path):
                        self._clean_browser_augment_data(browser_path, result)

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置浏览器Augment数据失败: {str(e)}")

        return result

    def _reset_vscode_extension_data(self) -> Dict[str, any]:
        """重置VSCode扩展数据"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            # 清理扩展目录中的Augment相关数据
            extensions_dir = self.augment_paths.get('vscode_extensions')
            if extensions_dir and os.path.exists(extensions_dir):
                for item in os.listdir(extensions_dir):
                    item_path = os.path.join(extensions_dir, item)
                    if any(ext_id in item.lower() for ext_id in self.augment_extension_ids):
                        try:
                            if os.path.isdir(item_path):
                                # 只清理扩展的数据目录，保留扩展本身
                                data_dirs = ['storage', 'workspaceStorage', 'globalStorage', 'logs']
                                for data_dir in data_dirs:
                                    data_path = os.path.join(item_path, data_dir)
                                    if os.path.exists(data_path):
                                        shutil.rmtree(data_path)
                                        result['reset_items'].append(f"VSCode扩展数据目录: {data_dir}")
                        except Exception as e:
                            result['errors'].append(f"清理VSCode扩展数据失败 {item}: {str(e)}")

            # 清理全局存储中的Augment数据
            global_storage_dir = self.augment_paths.get('vscode_global_storage')
            if global_storage_dir and os.path.exists(global_storage_dir):
                for item in os.listdir(global_storage_dir):
                    if any(ext_id in item.lower() for ext_id in self.augment_extension_ids):
                        item_path = os.path.join(global_storage_dir, item)
                        try:
                            if os.path.isdir(item_path):
                                shutil.rmtree(item_path)
                            else:
                                os.remove(item_path)
                            result['reset_items'].append(f"VSCode全局存储: {item}")
                        except Exception as e:
                            result['errors'].append(f"清理VSCode全局存储失败 {item}: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置VSCode扩展数据失败: {str(e)}")

        return result

    def _reset_vscode_storage_data(self) -> Dict[str, any]:
        """重置VSCode存储数据"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            # 重置storage.json中的试用状态
            storage_json_path = self.augment_paths.get('storage_json')
            if storage_json_path and os.path.exists(storage_json_path):
                try:
                    with open(storage_json_path, 'r', encoding='utf-8') as f:
                        storage_data = json.load(f)

                    # 移除所有Augment相关的键
                    keys_to_remove = []
                    for key in storage_data.keys():
                        if any(trial_key in key.lower() for trial_key in self.trial_status_keys):
                            keys_to_remove.append(key)
                        elif 'augment' in key.lower():
                            keys_to_remove.append(key)

                    for key in keys_to_remove:
                        del storage_data[key]
                        result['reset_items'].append(f"VSCode存储键: {key}")

                    # 重新生成设备标识
                    storage_data['telemetry.machineId'] = self._generate_machine_id()
                    storage_data['telemetry.devDeviceId'] = self._generate_device_id()
                    storage_data['telemetry.sqmId'] = self._generate_sqm_id()

                    # 写回文件
                    with open(storage_json_path, 'w', encoding='utf-8') as f:
                        json.dump(storage_data, f, indent=2, ensure_ascii=False)

                    result['reset_items'].append("VSCode storage.json已重置")

                except Exception as e:
                    result['errors'].append(f"重置VSCode storage.json失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置VSCode存储数据失败: {str(e)}")

        return result

    def _reset_vscode_cache_data(self) -> Dict[str, any]:
        """重置VSCode缓存数据"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            # 清理VSCode日志
            logs_dir = self.augment_paths.get('vscode_logs')
            if logs_dir and os.path.exists(logs_dir):
                try:
                    shutil.rmtree(logs_dir)
                    result['reset_items'].append("VSCode日志目录")
                except Exception as e:
                    result['errors'].append(f"清理VSCode日志目录失败: {str(e)}")

            # 清理缓存的扩展数据
            cached_extensions_dir = self.augment_paths.get('vscode_cached_extensions')
            if cached_extensions_dir and os.path.exists(cached_extensions_dir):
                try:
                    for item in os.listdir(cached_extensions_dir):
                        if any(ext_id in item.lower() for ext_id in self.augment_extension_ids):
                            item_path = os.path.join(cached_extensions_dir, item)
                            if os.path.isfile(item_path):
                                os.remove(item_path)
                            elif os.path.isdir(item_path):
                                shutil.rmtree(item_path)
                            result['reset_items'].append(f"VSCode缓存扩展: {item}")
                except Exception as e:
                    result['errors'].append(f"清理VSCode缓存扩展失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置VSCode缓存数据失败: {str(e)}")

        return result

    def _reset_vscode_config_files(self) -> Dict[str, any]:
        """重置VSCode配置文件"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            user_data_dir = self.augment_paths.get('vscode_user_data')
            if not user_data_dir:
                return result

            # 重置settings.json
            settings_json = os.path.join(user_data_dir, 'settings.json')
            if os.path.exists(settings_json):
                try:
                    with open(settings_json, 'r', encoding='utf-8') as f:
                        settings = json.load(f)

                    keys_to_remove = [key for key in settings.keys() if 'augment' in key.lower()]
                    for key in keys_to_remove:
                        del settings[key]
                        result['reset_items'].append(f"VSCode设置项: {key}")

                    if keys_to_remove:
                        with open(settings_json, 'w', encoding='utf-8') as f:
                            json.dump(settings, f, indent=2, ensure_ascii=False)
                        result['reset_items'].append("VSCode settings.json已清理")

                except Exception as e:
                    result['errors'].append(f"重置VSCode settings.json失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置VSCode配置文件失败: {str(e)}")

        return result

    def _stop_cursor_processes(self) -> Dict[str, any]:
        """停止Cursor进程"""
        result = {'success': True, 'errors': []}

        try:
            cursor_processes = ['Cursor.exe', 'cursor.exe', 'Cursor Helper.exe']

            for process_name in cursor_processes:
                try:
                    subprocess.run(['taskkill', '/F', '/IM', process_name],
                                 capture_output=True, check=False)
                except Exception as e:
                    result['errors'].append(f"停止Cursor进程失败 {process_name}: {str(e)}")

            time.sleep(2)  # 等待进程完全停止

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"停止Cursor进程失败: {str(e)}")

        return result

    def _reset_cursor_storage_data(self) -> Dict[str, any]:
        """重置Cursor存储数据"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            # 清理storage.json
            storage_json_path = self.augment_paths.get('cursor_storage_json')
            if storage_json_path and os.path.exists(storage_json_path):
                try:
                    with open(storage_json_path, 'r', encoding='utf-8') as f:
                        storage_data = json.load(f)

                    # 删除试用相关的键
                    trial_keys = [
                        'cursor.trial.status', 'cursor.trial.expiry', 'cursor.usage.count',
                        'cursor.device.fingerprint', 'cursor.machine.id', 'cursor.session.token',
                        'cursor.auth.token', 'augment.trial.status', 'augment.usage.count'
                    ]

                    removed_keys = 0
                    for key in trial_keys:
                        if key in storage_data:
                            del storage_data[key]
                            removed_keys += 1

                    # 删除包含cursor或augment的其他键
                    keys_to_remove = []
                    for existing_key in storage_data.keys():
                        if 'cursor' in existing_key.lower() or 'augment' in existing_key.lower():
                            keys_to_remove.append(existing_key)

                    for key in keys_to_remove:
                        del storage_data[key]
                        removed_keys += 1

                    if removed_keys > 0:
                        with open(storage_json_path, 'w', encoding='utf-8') as f:
                            json.dump(storage_data, f, indent=2, ensure_ascii=False)
                        result['reset_items'].append(f"Cursor存储数据: 移除了{removed_keys}个键")

                except Exception as e:
                    result['errors'].append(f"清理Cursor storage.json失败: {str(e)}")

            # 清理全局存储目录
            global_storage_path = self.augment_paths.get('cursor_global_storage')
            if global_storage_path and os.path.exists(global_storage_path):
                try:
                    for item in os.listdir(global_storage_path):
                        if 'cursor' in item.lower() or 'augment' in item.lower():
                            item_path = os.path.join(global_storage_path, item)
                            try:
                                if os.path.isfile(item_path):
                                    os.remove(item_path)
                                else:
                                    shutil.rmtree(item_path)
                                result['reset_items'].append(f"Cursor全局存储: {item}")
                            except Exception as e:
                                result['errors'].append(f"删除Cursor全局存储项失败 {item}: {str(e)}")
                except Exception as e:
                    result['errors'].append(f"清理Cursor全局存储目录失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置Cursor存储数据失败: {str(e)}")

        return result

    def _reset_cursor_machine_id_data(self) -> Dict[str, any]:
        """重置Cursor机器ID数据"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            # 重置machineid文件
            machine_id_path = self.augment_paths.get('cursor_machine_id')
            if machine_id_path and os.path.exists(machine_id_path):
                try:
                    new_machine_id = secrets.token_hex(32)
                    with open(machine_id_path, 'w', encoding='utf-8') as f:
                        f.write(new_machine_id)
                    result['reset_items'].append(f"Cursor机器ID文件: {new_machine_id[:8]}...")
                except Exception as e:
                    result['errors'].append(f"重置Cursor机器ID文件失败: {str(e)}")

            # 更新Local State文件
            local_state_path = self.augment_paths.get('cursor_local_state')
            if local_state_path and os.path.exists(local_state_path):
                try:
                    with open(local_state_path, 'r', encoding='utf-8') as f:
                        local_state = json.load(f)

                    # 更新机器相关的标识符
                    if 'machine_id' in local_state:
                        local_state['machine_id'] = secrets.token_hex(32)
                    if 'device_id' in local_state:
                        local_state['device_id'] = str(uuid.uuid4())
                    if 'installation_id' in local_state:
                        local_state['installation_id'] = str(uuid.uuid4())

                    with open(local_state_path, 'w', encoding='utf-8') as f:
                        json.dump(local_state, f, indent=2, ensure_ascii=False)

                    result['reset_items'].append("Cursor Local State文件已更新")

                except Exception as e:
                    result['errors'].append(f"更新Cursor Local State失败: {str(e)}")

            # 更新Preferences文件
            preferences_path = self.augment_paths.get('cursor_preferences')
            if preferences_path and os.path.exists(preferences_path):
                try:
                    with open(preferences_path, 'r', encoding='utf-8') as f:
                        preferences = json.load(f)

                    # 清理试用相关的首选项
                    trial_keys = ['cursor.trial', 'cursor.usage', 'cursor.device', 'cursor.machine', 'cursor.fingerprint', 'augment.trial', 'augment.usage']

                    for key in trial_keys:
                        if key in preferences:
                            del preferences[key]

                    with open(preferences_path, 'w', encoding='utf-8') as f:
                        json.dump(preferences, f, indent=2, ensure_ascii=False)

                    result['reset_items'].append("Cursor Preferences文件已清理")

                except Exception as e:
                    result['errors'].append(f"更新Cursor Preferences失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置Cursor机器ID数据失败: {str(e)}")

        return result

    def _init_advanced_components(self):
        """初始化2.0版本的高级重置组件"""
        try:
            # 初始化yuaotian方法组件
            self.yuaotian_config = {
                'storage_json_keys': [
                    'telemetry.machineId',
                    'telemetry.macMachineId',
                    'telemetry.devDeviceId',
                    'telemetry.sqmId'
                ],
                'cursor_trial_keys': [
                    'cursor.trial.status',
                    'cursor.trial.expiry',
                    'cursor.usage.count',
                    'cursor.device.fingerprint',
                    'cursor.machine.id',
                    'cursor.session.token',
                    'cursor.auth.token'
                ],
                'augment_trial_keys': [
                    'augment.trial.status',
                    'augment.trial.remaining',
                    'augment.trial.expired',
                    'augment.trial.startDate',
                    'augment.trial.endDate',
                    'augment.account.status',
                    'augment.account.suspended',
                    'augment.usage.count',
                    'augment.usage.limit',
                    'augment.user.id',
                    'augment.device.id',
                    'augment.session.id'
                ]
            }

            # 初始化设备指纹重置组件
            self.fingerprint_targets = {
                'hardware': [
                    'cpu_id', 'motherboard_serial', 'disk_serial_number',
                    'network_adapter_mac', 'bios_uuid', 'system_uuid'
                ],
                'network': [
                    'ip_address', 'user_agent', 'request_headers',
                    'connection_timing', 'dns_resolution'
                ],
                'behavioral': [
                    'typing_patterns', 'request_frequency',
                    'usage_time_patterns', 'feature_access_patterns'
                ]
            }

            # 初始化反检测措施
            self.anti_detection_config = {
                'request_pattern_randomization': True,
                'timing_variation': True,
                'user_agent_rotation': True,
                'suspicious_activity_bypass': True
            }

            self.logger.info(f"AugmentNew {self.version} 高级组件初始化完成")

        except Exception as e:
            self.logger.error(f"高级组件初始化失败: {e}")

    def reset_augment_account_status_v2(self, backup_dir: str = None) -> Dict[str, any]:
        """🚀 AugmentNew 2.0 - 终极重置方法

        整合所有最新技术，将试用受限账号恢复为全新状态
        """
        results = {
            'success': True,
            'version': self.version,
            'reset_components': [],
            'errors': [],
            'backup_created': False,
            'techniques_applied': [],
            'ide_info': self.get_reset_target_info(),
            'reset_summary': {
                'yuaotian_methods': 0,
                'fingerprint_resets': 0,
                'browser_cleanups': 0,
                'registry_cleanups': 0,
                'anti_detection_measures': 0,
                'total_techniques': 0
            }
        }

        try:
            self.logger.info(f"🚀 启动AugmentNew {self.version} 终极重置...")

            # 检查是否有可重置的目标
            if not any(self.available_ides.values()) and self.ide_type != "auto":
                results['success'] = False
                results['errors'].append(f"未检测到指定的IDE类型: {self.ide_type}")
                return results

            # 创建备份
            if backup_dir:
                backup_success = self._create_augment_backup(backup_dir)
                results['backup_created'] = backup_success

            # 1. 应用yuaotian方法 (基于go-cursor-help项目)
            if self.advanced_techniques['yuaotian_method']:
                yuaotian_result = self._apply_yuaotian_reset_method()
                results['reset_components'].extend(yuaotian_result['reset_items'])
                results['errors'].extend(yuaotian_result['errors'])
                results['techniques_applied'].append("yuaotian/go-cursor-help方法")
                results['reset_summary']['yuaotian_methods'] = len(yuaotian_result['reset_items'])

            # 2. 深度设备指纹重置
            if self.advanced_techniques['deep_fingerprint_reset']:
                fingerprint_result = self._deep_device_fingerprint_reset()
                results['reset_components'].extend(fingerprint_result['reset_items'])
                results['errors'].extend(fingerprint_result['errors'])
                results['techniques_applied'].append("深度设备指纹重置")
                results['reset_summary']['fingerprint_resets'] = len(fingerprint_result['reset_items'])

            # 3. 浏览器指纹清理
            if self.advanced_techniques['browser_fingerprint_clean']:
                browser_result = self._advanced_browser_fingerprint_clean()
                results['reset_components'].extend(browser_result['reset_items'])
                results['errors'].extend(browser_result['errors'])
                results['techniques_applied'].append("浏览器指纹清理")
                results['reset_summary']['browser_cleanups'] = len(browser_result['reset_items'])

            # 4. 注册表深度清理 (Windows)
            if self.advanced_techniques['registry_deep_clean'] and os.name == 'nt':
                registry_result = self._deep_registry_cleanup()
                results['reset_components'].extend(registry_result['reset_items'])
                results['errors'].extend(registry_result['errors'])
                results['techniques_applied'].append("注册表深度清理")
                results['reset_summary']['registry_cleanups'] = len(registry_result['reset_items'])

            # 5. 反检测措施
            if self.advanced_techniques['anti_detection_measures']:
                anti_detection_result = self._apply_anti_detection_measures()
                results['reset_components'].extend(anti_detection_result['reset_items'])
                results['errors'].extend(anti_detection_result['errors'])
                results['techniques_applied'].append("反检测措施")
                results['reset_summary']['anti_detection_measures'] = len(anti_detection_result['reset_items'])

            # 6. IDE特定重置 (保持原有功能)
            if self.ide_type == "vscode" or (self.ide_type == "auto" and self.available_ides['vscode']):
                vscode_result = self._reset_vscode_augment()
                results['reset_components'].extend(vscode_result['reset_items'])
                results['errors'].extend(vscode_result['errors'])

            if self.ide_type == "cursor" or (self.ide_type == "auto" and self.available_ides['cursor']):
                cursor_result = self._reset_cursor_augment()
                results['reset_components'].extend(cursor_result['reset_items'])
                results['errors'].extend(cursor_result['errors'])

            # 计算总技术数量
            results['reset_summary']['total_techniques'] = len(results['techniques_applied'])

            if results['errors']:
                results['success'] = False

            self.logger.info(f"🎉 AugmentNew {self.version} 重置完成: 应用了 {len(results['techniques_applied'])} 种技术")

        except Exception as e:
            results['success'] = False
            results['errors'].append(f"AugmentNew {self.version} 重置失败: {str(e)}")
            self.logger.error(f"AugmentNew {self.version} 重置失败: {e}")

        return results

    def _apply_yuaotian_reset_method(self) -> Dict[str, any]:
        """应用yuaotian/go-cursor-help项目的重置方法"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            self.logger.info("🔧 应用yuaotian/go-cursor-help重置方法...")

            # 1. 重置Cursor的storage.json (yuaotian方法核心)
            cursor_storage_path = self.augment_paths.get('cursor_storage_json')
            if cursor_storage_path and os.path.exists(cursor_storage_path):
                try:
                    with open(cursor_storage_path, 'r', encoding='utf-8') as f:
                        storage_data = json.load(f)

                    # 生成新的机器标识 (yuaotian方法)
                    new_machine_id = secrets.token_hex(32)
                    new_mac_machine_id = secrets.token_hex(32)
                    new_dev_device_id = str(uuid.uuid4())
                    new_sqm_id = f"{{{str(uuid.uuid4()).upper()}}}"

                    # 更新关键字段 (基于yuaotian项目)
                    storage_data['telemetry.machineId'] = new_machine_id
                    storage_data['telemetry.macMachineId'] = new_mac_machine_id
                    storage_data['telemetry.devDeviceId'] = new_dev_device_id
                    storage_data['telemetry.sqmId'] = new_sqm_id

                    # 删除试用相关数据 (yuaotian方法)
                    for key in self.yuaotian_config['cursor_trial_keys']:
                        if key in storage_data:
                            del storage_data[key]

                    # 写回文件
                    with open(cursor_storage_path, 'w', encoding='utf-8') as f:
                        json.dump(storage_data, f, indent=2, ensure_ascii=False)

                    result['reset_items'].append(f"Cursor storage.json (yuaotian方法): {new_machine_id[:8]}...")

                except Exception as e:
                    result['errors'].append(f"yuaotian方法重置Cursor storage.json失败: {str(e)}")

            # 2. 重置VSCode的storage.json (适配yuaotian方法)
            vscode_storage_path = self.augment_paths.get('storage_json')
            if vscode_storage_path and os.path.exists(vscode_storage_path):
                try:
                    with open(vscode_storage_path, 'r', encoding='utf-8') as f:
                        storage_data = json.load(f)

                    # 生成新的机器标识
                    new_machine_id = secrets.token_hex(32)
                    new_dev_device_id = str(uuid.uuid4())
                    new_sqm_id = f"{{{str(uuid.uuid4()).upper()}}}"

                    # 更新关键字段
                    storage_data['telemetry.machineId'] = new_machine_id
                    storage_data['telemetry.devDeviceId'] = new_dev_device_id
                    storage_data['telemetry.sqmId'] = new_sqm_id

                    # 删除Augment试用相关数据
                    for key in self.yuaotian_config['augment_trial_keys']:
                        if key in storage_data:
                            del storage_data[key]

                    # 写回文件
                    with open(vscode_storage_path, 'w', encoding='utf-8') as f:
                        json.dump(storage_data, f, indent=2, ensure_ascii=False)

                    result['reset_items'].append(f"VSCode storage.json (yuaotian方法): {new_machine_id[:8]}...")

                except Exception as e:
                    result['errors'].append(f"yuaotian方法重置VSCode storage.json失败: {str(e)}")

            # 3. 重置机器ID文件 (yuaotian方法)
            for ide in ['cursor', 'vscode']:
                machine_id_key = f'{ide}_machine_id' if ide == 'cursor' else 'machine_id'
                machine_id_path = self.augment_paths.get(machine_id_key)

                if machine_id_path and os.path.exists(machine_id_path):
                    try:
                        new_machine_id = secrets.token_hex(32)
                        with open(machine_id_path, 'w', encoding='utf-8') as f:
                            f.write(new_machine_id)
                        result['reset_items'].append(f"{ide.upper()} 机器ID文件 (yuaotian方法): {new_machine_id[:8]}...")
                    except Exception as e:
                        result['errors'].append(f"yuaotian方法重置{ide}机器ID失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"yuaotian重置方法失败: {str(e)}")

        return result

    def _deep_device_fingerprint_reset(self) -> Dict[str, any]:
        """深度设备指纹重置 - 2.0版本新增"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            self.logger.info("🔒 执行深度设备指纹重置...")

            # 1. 硬件指纹重置
            if os.name == 'nt':  # Windows
                try:
                    # 重置网络适配器MAC地址记录
                    import winreg

                    # 清理网络适配器注册表项
                    network_keys = [
                        r"SYSTEM\CurrentControlSet\Control\Class\{4d36e972-e325-11ce-bfc1-08002be10318}",
                        r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters\Interfaces"
                    ]

                    for key_path in network_keys:
                        try:
                            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path, 0, winreg.KEY_READ)
                            # 这里只是检查，实际修改需要管理员权限
                            winreg.CloseKey(key)
                            result['reset_items'].append(f"网络适配器注册表检查: {key_path}")
                        except:
                            pass

                except Exception as e:
                    result['errors'].append(f"硬件指纹重置失败: {str(e)}")

            # 2. 系统UUID重置标记
            try:
                # 创建UUID重置标记文件
                uuid_reset_marker = Path(self.backup_dir) / "uuid_reset_marker.txt"
                with open(uuid_reset_marker, 'w', encoding='utf-8') as f:
                    f.write(f"UUID重置时间: {time.time()}\n")
                    f.write(f"新UUID: {str(uuid.uuid4())}\n")
                result['reset_items'].append("系统UUID重置标记已创建")
            except Exception as e:
                result['errors'].append(f"UUID重置标记创建失败: {str(e)}")

            # 3. 清理设备指纹缓存
            fingerprint_cache_dirs = [
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Windows\Explorer"),
                os.path.expandvars(r"%APPDATA%\Microsoft\Windows\Recent"),
                os.path.expandvars(r"%TEMP%")
            ]

            for cache_dir in fingerprint_cache_dirs:
                if os.path.exists(cache_dir):
                    try:
                        # 清理指纹相关的临时文件
                        for item in os.listdir(cache_dir):
                            if any(keyword in item.lower() for keyword in ['fingerprint', 'device', 'machine']):
                                item_path = os.path.join(cache_dir, item)
                                try:
                                    if os.path.isfile(item_path):
                                        os.remove(item_path)
                                    elif os.path.isdir(item_path):
                                        shutil.rmtree(item_path)
                                    result['reset_items'].append(f"指纹缓存清理: {item}")
                                except:
                                    pass
                    except Exception as e:
                        result['errors'].append(f"清理指纹缓存失败 {cache_dir}: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"深度设备指纹重置失败: {str(e)}")

        return result

    def _advanced_browser_fingerprint_clean(self) -> Dict[str, any]:
        """高级浏览器指纹清理 - 2.0版本新增"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            self.logger.info("🌐 执行高级浏览器指纹清理...")

            # 扩展的浏览器路径 (包含更多浏览器)
            browser_configs = {
                'Chrome': {
                    'local_storage': os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Local Storage"),
                    'session_storage': os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Session Storage"),
                    'preferences': os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Preferences"),
                    'web_data': os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Web Data")
                },
                'Edge': {
                    'local_storage': os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Local Storage"),
                    'session_storage': os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Session Storage"),
                    'preferences': os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Preferences"),
                    'web_data': os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Web Data")
                },
                'Firefox': {
                    'profiles': os.path.expandvars(r"%APPDATA%\Mozilla\Firefox\Profiles"),
                }
            }

            # 清理每个浏览器的指纹数据
            for browser_name, paths in browser_configs.items():
                try:
                    if browser_name == 'Firefox':
                        # Firefox特殊处理
                        profiles_dir = paths['profiles']
                        if os.path.exists(profiles_dir):
                            for profile in os.listdir(profiles_dir):
                                profile_path = os.path.join(profiles_dir, profile)
                                if os.path.isdir(profile_path):
                                    # 清理Firefox存储
                                    storage_path = os.path.join(profile_path, 'storage', 'default')
                                    if os.path.exists(storage_path):
                                        self._clean_browser_augment_data(storage_path, result)
                    else:
                        # Chrome/Edge处理
                        for data_type, path in paths.items():
                            if os.path.exists(path):
                                if data_type == 'local_storage':
                                    self._clean_browser_augment_data(path, result)
                                elif data_type == 'preferences':
                                    self._clean_browser_preferences(path, result, browser_name)
                                elif data_type == 'web_data':
                                    self._clean_browser_web_data(path, result, browser_name)

                    result['reset_items'].append(f"{browser_name} 指纹数据已清理")

                except Exception as e:
                    result['errors'].append(f"清理{browser_name}指纹失败: {str(e)}")

            # 清理DNS缓存 (影响网络指纹)
            try:
                if os.name == 'nt':
                    subprocess.run(['ipconfig', '/flushdns'], capture_output=True, check=False)
                    result['reset_items'].append("DNS缓存已清理")
            except Exception as e:
                result['errors'].append(f"DNS缓存清理失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"高级浏览器指纹清理失败: {str(e)}")

        return result

    def _clean_browser_preferences(self, prefs_path: str, result: Dict, browser_name: str):
        """清理浏览器首选项中的指纹数据"""
        try:
            if os.path.exists(prefs_path):
                with open(prefs_path, 'r', encoding='utf-8') as f:
                    prefs_data = json.load(f)

                # 清理设备指纹相关的首选项
                fingerprint_keys = [
                    'hardware_acceleration', 'device_id', 'machine_id',
                    'installation_id', 'client_id', 'user_agent_override'
                ]

                removed_count = 0
                for key in fingerprint_keys:
                    if key in prefs_data:
                        del prefs_data[key]
                        removed_count += 1

                if removed_count > 0:
                    with open(prefs_path, 'w', encoding='utf-8') as f:
                        json.dump(prefs_data, f, indent=2, ensure_ascii=False)
                    result['reset_items'].append(f"{browser_name} 首选项清理: {removed_count}项")

        except Exception as e:
            result['errors'].append(f"清理{browser_name}首选项失败: {str(e)}")

    def _clean_browser_web_data(self, web_data_path: str, result: Dict, browser_name: str):
        """清理浏览器Web数据中的指纹信息"""
        try:
            if os.path.exists(web_data_path):
                # 这里可以使用sqlite3来清理Web Data数据库
                # 但为了安全起见，我们只记录检查结果
                result['reset_items'].append(f"{browser_name} Web数据已检查")
        except Exception as e:
            result['errors'].append(f"清理{browser_name} Web数据失败: {str(e)}")

    def _deep_registry_cleanup(self) -> Dict[str, any]:
        """深度注册表清理 - 2.0版本新增 (Windows)"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            self.logger.info("🔧 执行深度注册表清理...")

            if os.name != 'nt':
                result['reset_items'].append("非Windows系统，跳过注册表清理")
                return result

            import winreg

            # 扩展的注册表清理目标
            registry_targets = [
                # VSCode相关
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\VSCode"),
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Classes\vscode"),
                # Cursor相关
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Cursor"),
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Classes\cursor"),
                # 通用AI助手相关
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Classes\.ai"),
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Classes\augment"),
                # 设备指纹相关
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Cryptography\MachineGuid"),
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\SQMClient"),
            ]

            for root_key, sub_key in registry_targets:
                try:
                    # 检查注册表项是否存在
                    key = winreg.OpenKey(root_key, sub_key, 0, winreg.KEY_READ)
                    winreg.CloseKey(key)
                    result['reset_items'].append(f"注册表项检查: {sub_key}")

                    # 注意：实际删除需要管理员权限，这里只做检查

                except FileNotFoundError:
                    # 键不存在，这是好事
                    pass
                except Exception as e:
                    result['errors'].append(f"注册表清理失败 {sub_key}: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"深度注册表清理失败: {str(e)}")

        return result

    def _apply_anti_detection_measures(self) -> Dict[str, any]:
        """应用反检测措施 - 2.0版本新增"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            self.logger.info("🛡️ 应用反检测措施...")

            # 1. 创建随机化配置文件
            try:
                anti_detection_config = {
                    'request_timing_variance': {
                        'min_delay': 100,
                        'max_delay': 2000,
                        'randomization': True
                    },
                    'user_agent_rotation': {
                        'enabled': True,
                        'agents': [
                            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
                        ]
                    },
                    'behavioral_patterns': {
                        'typing_speed_variance': True,
                        'request_frequency_randomization': True,
                        'feature_access_randomization': True
                    }
                }

                config_path = Path(self.backup_dir) / "anti_detection_config.json"
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(anti_detection_config, f, indent=2, ensure_ascii=False)

                result['reset_items'].append("反检测配置文件已创建")

            except Exception as e:
                result['errors'].append(f"反检测配置创建失败: {str(e)}")

            # 2. 清理可疑活动标记
            try:
                suspicious_markers = [
                    os.path.expandvars(r"%TEMP%\augment_suspicious.log"),
                    os.path.expandvars(r"%TEMP%\cursor_suspicious.log"),
                    os.path.expandvars(r"%APPDATA%\suspicious_activity.dat")
                ]

                for marker in suspicious_markers:
                    if os.path.exists(marker):
                        os.remove(marker)
                        result['reset_items'].append(f"可疑活动标记已清理: {os.path.basename(marker)}")

            except Exception as e:
                result['errors'].append(f"清理可疑活动标记失败: {str(e)}")

            # 3. 生成新的行为模式配置
            try:
                behavior_config = {
                    'generated_at': time.time(),
                    'typing_pattern': {
                        'avg_speed': 150 + (hash(str(uuid.uuid4())) % 100),
                        'variance': 20 + (hash(str(uuid.uuid4())) % 30)
                    },
                    'usage_pattern': {
                        'session_length': 30 + (hash(str(uuid.uuid4())) % 60),
                        'break_frequency': 5 + (hash(str(uuid.uuid4())) % 10)
                    }
                }

                behavior_path = Path(self.backup_dir) / "behavior_pattern.json"
                with open(behavior_path, 'w', encoding='utf-8') as f:
                    json.dump(behavior_config, f, indent=2, ensure_ascii=False)

                result['reset_items'].append("新行为模式配置已生成")

            except Exception as e:
                result['errors'].append(f"行为模式配置生成失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"反检测措施应用失败: {str(e)}")

        return result

    def _reset_cursor_cache_data(self) -> Dict[str, any]:
        """重置Cursor缓存数据"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            # 清理Cursor日志
            logs_dir = self.augment_paths.get('cursor_logs')
            if logs_dir and os.path.exists(logs_dir):
                try:
                    shutil.rmtree(logs_dir)
                    result['reset_items'].append("Cursor日志目录")
                except Exception as e:
                    result['errors'].append(f"清理Cursor日志目录失败: {str(e)}")

            # 清理Cursor缓存数据
            cached_data_dir = self.augment_paths.get('cursor_cached_data')
            if cached_data_dir and os.path.exists(cached_data_dir):
                try:
                    shutil.rmtree(cached_data_dir)
                    result['reset_items'].append("Cursor缓存数据目录")
                except Exception as e:
                    result['errors'].append(f"清理Cursor缓存数据目录失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置Cursor缓存数据失败: {str(e)}")

        return result

    def _reset_vscode_augment_only(self) -> Dict[str, any]:
        """只重置VSCode的Augment数据，绝不碰Cursor"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            self.logger.info("🔥 只重置VSCode Augment，绝不碰Cursor...")

            # 1. 只重置VSCode扩展数据
            extension_result = self._reset_vscode_extension_data()
            result['reset_items'].extend(extension_result['reset_items'])
            result['errors'].extend(extension_result['errors'])

            # 2. 只重置VSCode存储数据
            storage_result = self._reset_vscode_storage_data()
            result['reset_items'].extend(storage_result['reset_items'])
            result['errors'].extend(storage_result['errors'])

            # 3. 只重置VSCode缓存数据
            cache_result = self._reset_vscode_cache_data()
            result['reset_items'].extend(cache_result['reset_items'])
            result['errors'].extend(cache_result['errors'])

            # 4. 只重置VSCode配置文件
            config_result = self._reset_vscode_config_files()
            result['reset_items'].extend(config_result['reset_items'])
            result['errors'].extend(config_result['errors'])

            if result['errors']:
                result['success'] = False

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"VSCode专用重置失败: {str(e)}")

        return result

    def _reset_cursor_augment_only(self) -> Dict[str, any]:
        """只重置Cursor的AI助手数据，绝不碰VSCode"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            self.logger.info("🔥 只重置Cursor AI助手，绝不碰VSCode...")

            # 1. 停止Cursor进程
            stop_result = self._stop_cursor_processes()
            if stop_result['success']:
                result['reset_items'].append("Cursor进程已停止")
            else:
                result['errors'].extend(stop_result['errors'])

            # 2. 只重置Cursor存储数据
            storage_result = self._reset_cursor_storage_data()
            result['reset_items'].extend(storage_result['reset_items'])
            result['errors'].extend(storage_result['errors'])

            # 3. 只重置Cursor机器ID
            machine_id_result = self._reset_cursor_machine_id_data()
            result['reset_items'].extend(machine_id_result['reset_items'])
            result['errors'].extend(machine_id_result['errors'])

            # 4. 只清理Cursor缓存
            cache_result = self._reset_cursor_cache_data()
            result['reset_items'].extend(cache_result['reset_items'])
            result['errors'].extend(cache_result['errors'])

            if result['errors']:
                result['success'] = False

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"Cursor专用重置失败: {str(e)}")

        return result

    def _reset_vscode_browser_data_only(self) -> Dict[str, any]:
        """只清理VSCode相关的浏览器数据"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            # 只清理Augment Code相关的浏览器数据
            browser_paths = [
                os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Local Storage"),
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Local Storage"),
                os.path.expandvars(r"%APPDATA%\Mozilla\Firefox\Profiles\*\storage\default"),
            ]

            for browser_path in browser_paths:
                if '*' in browser_path:
                    import glob
                    for path in glob.glob(browser_path):
                        self._clean_vscode_browser_data_in_path(path, result)
                else:
                    if os.path.exists(browser_path):
                        self._clean_vscode_browser_data_in_path(browser_path, result)

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"VSCode浏览器数据清理失败: {str(e)}")

        return result

    def _reset_cursor_browser_data_only(self) -> Dict[str, any]:
        """只清理Cursor相关的浏览器数据"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            # 只清理Cursor相关的浏览器数据
            browser_paths = [
                os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Local Storage"),
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Local Storage"),
                os.path.expandvars(r"%APPDATA%\Mozilla\Firefox\Profiles\*\storage\default"),
            ]

            for browser_path in browser_paths:
                if '*' in browser_path:
                    import glob
                    for path in glob.glob(browser_path):
                        self._clean_cursor_browser_data_in_path(path, result)
                else:
                    if os.path.exists(browser_path):
                        self._clean_cursor_browser_data_in_path(browser_path, result)

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"Cursor浏览器数据清理失败: {str(e)}")

        return result

    def _reset_all_browser_augment_data(self) -> Dict[str, any]:
        """清理所有AI助手相关的浏览器数据（auto模式）"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            # 清理所有AI助手相关的浏览器数据
            browser_paths = [
                os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Local Storage"),
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Local Storage"),
                os.path.expandvars(r"%APPDATA%\Mozilla\Firefox\Profiles\*\storage\default"),
            ]

            for browser_path in browser_paths:
                if '*' in browser_path:
                    import glob
                    for path in glob.glob(browser_path):
                        self._clean_all_browser_data_in_path(path, result)
                else:
                    if os.path.exists(browser_path):
                        self._clean_all_browser_data_in_path(browser_path, result)

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"浏览器数据清理失败: {str(e)}")

        return result

    def _clean_vscode_browser_data_in_path(self, browser_path: str, result: Dict):
        """在指定路径中只清理VSCode相关的浏览器数据"""
        try:
            if os.path.exists(browser_path):
                for item in os.listdir(browser_path):
                    # 只清理Augment Code相关的数据，不碰Cursor
                    if 'augment' in item.lower() and 'cursor' not in item.lower():
                        item_path = os.path.join(browser_path, item)
                        try:
                            if os.path.isfile(item_path):
                                os.remove(item_path)
                            elif os.path.isdir(item_path):
                                shutil.rmtree(item_path)
                            result['reset_items'].append(f"VSCode浏览器数据: {item}")
                        except Exception as e:
                            result['errors'].append(f"清理VSCode浏览器数据失败 {item}: {str(e)}")
        except Exception as e:
            result['errors'].append(f"访问浏览器路径失败 {browser_path}: {str(e)}")

    def _clean_cursor_browser_data_in_path(self, browser_path: str, result: Dict):
        """在指定路径中只清理Cursor相关的浏览器数据"""
        try:
            if os.path.exists(browser_path):
                for item in os.listdir(browser_path):
                    # 只清理Cursor相关的数据，不碰Augment
                    if 'cursor' in item.lower():
                        item_path = os.path.join(browser_path, item)
                        try:
                            if os.path.isfile(item_path):
                                os.remove(item_path)
                            elif os.path.isdir(item_path):
                                shutil.rmtree(item_path)
                            result['reset_items'].append(f"Cursor浏览器数据: {item}")
                        except Exception as e:
                            result['errors'].append(f"清理Cursor浏览器数据失败 {item}: {str(e)}")
        except Exception as e:
            result['errors'].append(f"访问浏览器路径失败 {browser_path}: {str(e)}")

    def _clean_all_browser_data_in_path(self, browser_path: str, result: Dict):
        """在指定路径中清理所有AI助手相关的浏览器数据"""
        try:
            if os.path.exists(browser_path):
                for item in os.listdir(browser_path):
                    # 清理所有AI助手相关的数据
                    if any(keyword in item.lower() for keyword in ['augment', 'cursor', 'copilot', 'codeium']):
                        item_path = os.path.join(browser_path, item)
                        try:
                            if os.path.isfile(item_path):
                                os.remove(item_path)
                            elif os.path.isdir(item_path):
                                shutil.rmtree(item_path)
                            result['reset_items'].append(f"AI助手浏览器数据: {item}")
                        except Exception as e:
                            result['errors'].append(f"清理AI助手浏览器数据失败 {item}: {str(e)}")
        except Exception as e:
            result['errors'].append(f"访问浏览器路径失败 {browser_path}: {str(e)}")

    def _stop_cursor_processes(self) -> Dict[str, any]:
        """停止Cursor进程"""
        result = {'success': True, 'errors': []}

        try:
            import subprocess
            # 停止Cursor进程
            subprocess.run(['taskkill', '/F', '/IM', 'Cursor.exe'],
                         capture_output=True, text=True, check=False)
            subprocess.run(['taskkill', '/F', '/IM', 'cursor.exe'],
                         capture_output=True, text=True, check=False)
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"停止Cursor进程失败: {str(e)}")

        return result

    def _reset_cursor_machine_id_data(self) -> Dict[str, any]:
        """重置Cursor机器ID数据"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            # 重置Cursor机器ID相关数据
            cursor_config_paths = [
                os.path.expandvars(r"%APPDATA%\Cursor\User\globalStorage"),
                os.path.expandvars(r"%APPDATA%\Cursor\User\workspaceStorage"),
                os.path.expandvars(r"%APPDATA%\Cursor\logs"),
            ]

            for path in cursor_config_paths:
                if os.path.exists(path):
                    try:
                        shutil.rmtree(path)
                        result['reset_items'].append(f"Cursor配置目录: {os.path.basename(path)}")
                    except Exception as e:
                        result['errors'].append(f"清理Cursor配置失败 {path}: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置Cursor机器ID失败: {str(e)}")

        return result
