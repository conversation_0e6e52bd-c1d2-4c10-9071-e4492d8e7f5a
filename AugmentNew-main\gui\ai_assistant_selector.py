"""
AI助手选择器组件
提供折叠下拉选择功能，支持多选和全选
"""

import customtkinter as ctk
from typing import Dict, List, Callable, Any
import json
from pathlib import Path

class AIAssistantSelector(ctk.CTkFrame):
    """AI助手选择器 - 支持折叠下拉选择"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # AI助手配置
        self.ai_assistants = {
            'augment_code': {
                'name': 'Augment Code',
                'description': 'VSCode AI编程助手',
                'icon': '🤖',
                'enabled': True
            },
            'cursor_ai': {
                'name': 'Cursor AI', 
                'description': '智能编程IDE',
                'icon': '⚡',
                'enabled': True
            },
            'github_copilot': {
                'name': 'GitHub Copilot',
                'description': 'AI代码补全助手',
                'icon': '🐙',
                'enabled': True
            },
            'tabnine': {
                'name': 'Tabnine',
                'description': 'AI代码智能助手',
                'icon': '🧠',
                'enabled': True
            },
            'codeium': {
                'name': 'Codeium',
                'description': '免费AI编程助手',
                'icon': '🚀',
                'enabled': True
            },
            'claude_ai': {
                'name': 'Claude AI',
                'description': 'Anthropic AI助手',
                'icon': '🎭',
                'enabled': True
            },
            'codewhisperer': {
                'name': 'CodeWhisperer',
                'description': 'Amazon AI编程助手',
                'icon': '☁️',
                'enabled': True
            },
            'sourcegraph_cody': {
                'name': 'Sourcegraph Cody',
                'description': '企业级AI助手',
                'icon': '🏢',
                'enabled': True
            }
        }
        
        # 状态变量
        self.is_expanded = False
        self.selected_assistants = set()
        self.selection_callback = None
        
        # 创建界面
        self.setup_ui()
        
        # 加载保存的选择
        self.load_selection()
    
    def setup_ui(self):
        """设置用户界面"""
        self.grid_columnconfigure(0, weight=1)
        
        # 主标题框架
        self.header_frame = ctk.CTkFrame(self)
        self.header_frame.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
        self.header_frame.grid_columnconfigure(1, weight=1)
        
        # 折叠/展开按钮
        self.toggle_btn = ctk.CTkButton(
            self.header_frame,
            text="▶",
            width=30,
            height=30,
            command=self.toggle_expansion,
            font=("Arial", 16)
        )
        self.toggle_btn.grid(row=0, column=0, padx=5, pady=5)
        
        # 标题标签
        self.title_label = ctk.CTkLabel(
            self.header_frame,
            text="🤖 AI助手选择",
            font=("Microsoft YaHei UI", 14, "bold")
        )
        self.title_label.grid(row=0, column=1, sticky="w", padx=10, pady=5)
        
        # 选择状态标签
        self.status_label = ctk.CTkLabel(
            self.header_frame,
            text="已选择: 0/8",
            font=("Microsoft YaHei UI", 10),
            text_color="gray"
        )
        self.status_label.grid(row=0, column=2, padx=10, pady=5)
        
        # 快速操作按钮框架
        self.quick_actions_frame = ctk.CTkFrame(self.header_frame)
        self.quick_actions_frame.grid(row=0, column=3, padx=5, pady=2)
        
        # 全选按钮
        self.select_all_btn = ctk.CTkButton(
            self.quick_actions_frame,
            text="全选",
            width=50,
            height=25,
            command=self.select_all,
            font=("Microsoft YaHei UI", 10)
        )
        self.select_all_btn.grid(row=0, column=0, padx=2)
        
        # 清空按钮
        self.clear_all_btn = ctk.CTkButton(
            self.quick_actions_frame,
            text="清空",
            width=50,
            height=25,
            command=self.clear_all,
            font=("Microsoft YaHei UI", 10)
        )
        self.clear_all_btn.grid(row=0, column=1, padx=2)
        
        # 可折叠的选择区域
        self.selection_frame = ctk.CTkScrollableFrame(self)
        self.selection_frame.grid_columnconfigure(0, weight=1)
        
        # 创建AI助手选择项
        self.create_assistant_items()
        
        # 初始状态为折叠
        self.toggle_expansion()
    
    def create_assistant_items(self):
        """创建AI助手选择项"""
        self.assistant_vars = {}
        self.assistant_frames = {}
        
        row = 0
        for assistant_id, config in self.ai_assistants.items():
            # 创建助手框架
            assistant_frame = ctk.CTkFrame(self.selection_frame)
            assistant_frame.grid(row=row, column=0, sticky="ew", padx=5, pady=2)
            assistant_frame.grid_columnconfigure(1, weight=1)
            
            # 复选框变量
            var = ctk.BooleanVar()
            self.assistant_vars[assistant_id] = var
            self.assistant_frames[assistant_id] = assistant_frame
            
            # 复选框
            checkbox = ctk.CTkCheckBox(
                assistant_frame,
                text="",
                variable=var,
                command=lambda aid=assistant_id: self.on_selection_change(aid),
                width=20
            )
            checkbox.grid(row=0, column=0, padx=5, pady=5)
            
            # 图标标签
            icon_label = ctk.CTkLabel(
                assistant_frame,
                text=config['icon'],
                font=("Arial", 16)
            )
            icon_label.grid(row=0, column=1, padx=5, pady=5)
            
            # 名称标签
            name_label = ctk.CTkLabel(
                assistant_frame,
                text=config['name'],
                font=("Microsoft YaHei UI", 12, "bold")
            )
            name_label.grid(row=0, column=2, sticky="w", padx=5, pady=5)
            
            # 描述标签
            desc_label = ctk.CTkLabel(
                assistant_frame,
                text=config['description'],
                font=("Microsoft YaHei UI", 10),
                text_color="gray"
            )
            desc_label.grid(row=0, column=3, sticky="w", padx=5, pady=5)
            
            # 状态指示器
            status_indicator = ctk.CTkLabel(
                assistant_frame,
                text="●",
                font=("Arial", 12),
                text_color="green" if config['enabled'] else "red"
            )
            status_indicator.grid(row=0, column=4, padx=5, pady=5)
            
            row += 1
    
    def toggle_expansion(self):
        """切换展开/折叠状态"""
        if self.is_expanded:
            # 折叠
            self.selection_frame.grid_remove()
            self.toggle_btn.configure(text="▶")
            self.is_expanded = False
        else:
            # 展开
            self.selection_frame.grid(row=1, column=0, sticky="ew", padx=5, pady=5)
            self.toggle_btn.configure(text="▼")
            self.is_expanded = True
    
    def on_selection_change(self, assistant_id: str):
        """处理选择变化"""
        if self.assistant_vars[assistant_id].get():
            self.selected_assistants.add(assistant_id)
        else:
            self.selected_assistants.discard(assistant_id)
        
        self.update_status()
        
        # 调用回调函数
        if self.selection_callback:
            self.selection_callback(list(self.selected_assistants))
    
    def select_all(self):
        """全选所有AI助手"""
        for assistant_id, var in self.assistant_vars.items():
            if self.ai_assistants[assistant_id]['enabled']:
                var.set(True)
                self.selected_assistants.add(assistant_id)
        
        self.update_status()
        if self.selection_callback:
            self.selection_callback(list(self.selected_assistants))
    
    def clear_all(self):
        """清空所有选择"""
        for var in self.assistant_vars.values():
            var.set(False)
        
        self.selected_assistants.clear()
        self.update_status()
        
        if self.selection_callback:
            self.selection_callback(list(self.selected_assistants))
    
    def update_status(self):
        """更新状态显示"""
        selected_count = len(self.selected_assistants)
        total_count = len(self.ai_assistants)
        
        self.status_label.configure(text=f"已选择: {selected_count}/{total_count}")
        
        # 更新助手框架的视觉反馈
        for assistant_id, frame in self.assistant_frames.items():
            if assistant_id in self.selected_assistants:
                frame.configure(border_color="green", border_width=2)
            else:
                frame.configure(border_color="transparent", border_width=0)
    
    def get_selected_assistants(self) -> List[str]:
        """获取选中的AI助手列表"""
        return list(self.selected_assistants)
    
    def set_selection_callback(self, callback: Callable[[List[str]], None]):
        """设置选择变化回调函数"""
        self.selection_callback = callback
    
    def save_selection(self):
        """保存选择到文件"""
        try:
            config_file = Path("ai_selection.json")
            config_data = {
                'selected_assistants': list(self.selected_assistants),
                'timestamp': str(ctk.datetime.datetime.now())
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"保存AI助手选择失败: {e}")
    
    def load_selection(self):
        """从文件加载选择"""
        try:
            config_file = Path("ai_selection.json")
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                selected = config_data.get('selected_assistants', [])
                
                # 恢复选择状态
                for assistant_id in selected:
                    if assistant_id in self.assistant_vars:
                        self.assistant_vars[assistant_id].set(True)
                        self.selected_assistants.add(assistant_id)
                
                self.update_status()
                
        except Exception as e:
            print(f"加载AI助手选择失败: {e}")
            # 默认全选
            self.select_all()
    
    def get_selection_summary(self) -> str:
        """获取选择摘要"""
        if not self.selected_assistants:
            return "未选择任何AI助手"
        
        if len(self.selected_assistants) == len(self.ai_assistants):
            return "已选择全部AI助手"
        
        selected_names = []
        for assistant_id in self.selected_assistants:
            selected_names.append(self.ai_assistants[assistant_id]['name'])
        
        return f"已选择: {', '.join(selected_names)}"
