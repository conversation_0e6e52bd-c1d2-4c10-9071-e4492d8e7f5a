' AugmentNew 超级启动器 - 激活所有功能
' 最强力、最全面、最安全的启动脚本
' 自动激活所有高级功能，无需任何限制
' 作者: alltobebetter
' 许可: MIT License

Option Explicit

Dim objShell, objFSO, objWMI, scriptPath, pythonPath, mainScript
Dim currentDir, logFile, errorMsg, configFile
Dim adminCheck, systemCheck, featureActivation

' 创建对象
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' 获取脚本所在目录
scriptPath = objFSO.GetParentFolderName(WScript.ScriptFullName)
currentDir = scriptPath

' 主程序文件路径
mainScript = objFSO.BuildPath(currentDir, "gui_main.py")
configFile = objFSO.BuildPath(currentDir, "super_config.json")

' 日志文件路径
logFile = objFSO.BuildPath(currentDir, "超级启动日志.txt")

' 显示超级启动信息
MsgBox "🚀 AugmentNew 超级启动器 🚀" & vbCrLf & vbCrLf & _
       "✨ 激活所有高级功能" & vbCrLf & _
       "🔒 设备指纹清理" & vbCrLf & _
       "🔄 Augment账号重置" & vbCrLf & _
       "🌐 超级浏览器清理" & vbCrLf & _
       "💎 核弹级系统重置" & vbCrLf & _
       "🛡️ 军用级安全保护" & vbCrLf & vbCrLf & _
       "点击确定开始超级启动...", vbInformation, "AugmentNew 超级版"

' 写入启动日志
WriteLog "=== AugmentNew 超级启动器日志 ==="
WriteLog "启动时间: " & Now()
WriteLog "脚本路径: " & scriptPath

' 1. 执行系统检查
WriteLog "开始系统检查..."
systemCheck = PerformSystemCheck()

If Not systemCheck Then
    MsgBox "⚠️ 系统检查发现问题！" & vbCrLf & vbCrLf & _
           "建议以管理员身份运行以获得最佳效果" & vbCrLf & _
           "程序将继续启动，但某些功能可能受限", vbExclamation, "系统检查"
End If

' 2. 检查管理员权限
adminCheck = IsAdmin()
WriteLog "管理员权限检查: " & adminCheck

If adminCheck Then
    WriteLog "检测到管理员权限，激活所有高级功能"
Else
    WriteLog "未检测到管理员权限，部分功能可能受限"
End If

' 3. 检查主程序文件
If Not objFSO.FileExists(mainScript) Then
    MsgBox "❌ 错误：找不到主程序文件！" & vbCrLf & vbCrLf & _
           "请确保 gui_main.py 文件存在于：" & vbCrLf & _
           mainScript, vbCritical, "AugmentNew 启动失败"
    WScript.Quit 1
End If

' 4. 查找Python解释器
WriteLog "查找Python解释器..."
pythonPath = FindPython()

If pythonPath = "" Then
    MsgBox "❌ 错误：未找到Python解释器！" & vbCrLf & vbCrLf & _
           "请确保已安装Python 3.10或更高版本" & vbCrLf & _
           "下载地址：https://www.python.org/downloads/", vbCritical, "Python未安装"
    WScript.Quit 1
End If

WriteLog "Python路径: " & pythonPath

' 5. 创建超级配置文件
WriteLog "创建超级配置文件..."
CreateSuperConfig()

' 6. 激活所有功能
WriteLog "激活所有高级功能..."
featureActivation = ActivateAllFeatures()

If featureActivation Then
    WriteLog "所有功能激活成功"
Else
    WriteLog "功能激活过程中遇到问题，但程序将继续运行"
End If

' 7. 设置环境变量
WriteLog "设置超级环境变量..."
SetSuperEnvironment()

' 8. 启动程序
WriteLog "启动主程序..."
On Error Resume Next
objShell.CurrentDirectory = currentDir

' 使用特殊参数启动超级版本
Dim startupCmd
startupCmd = """" & pythonPath & """ """ & mainScript & """ --super-mode --all-features --no-limits"

objShell.Run startupCmd, 1, False

If Err.Number <> 0 Then
    errorMsg = "🚨 启动失败！" & vbCrLf & vbCrLf & _
               "错误代码: " & Err.Number & vbCrLf & _
               "错误描述: " & Err.Description & vbCrLf & vbCrLf & _
               "请检查Python环境是否正确安装"
    WriteLog "启动失败: " & Err.Description
    MsgBox errorMsg, vbCritical, "AugmentNew 启动失败"
    WScript.Quit 1
Else
    WriteLog "超级版程序启动成功"
    
    ' 显示成功信息
    MsgBox "🎉 AugmentNew 超级版启动成功！🎉" & vbCrLf & vbCrLf & _
           "✅ 所有高级功能已激活" & vbCrLf & _
           "✅ 设备指纹清理已启用" & vbCrLf & _
           "✅ Augment账号重置已启用" & vbCrLf & _
           "✅ 超级浏览器清理已启用" & vbCrLf & _
           "✅ 核弹级系统重置已启用" & vbCrLf & vbCrLf & _
           "程序正在后台运行，请稍等片刻..." & vbCrLf & _
           "享受最强大的清理体验！", vbInformation, "超级版启动成功"
End If

On Error GoTo 0

' 清理对象
Set objShell = Nothing
Set objFSO = Nothing
Set objWMI = Nothing

WScript.Quit 0

' ===== 函数定义 =====

Function IsAdmin()
    ' 检查是否有管理员权限
    On Error Resume Next
    Dim tempFile
    tempFile = objShell.ExpandEnvironmentStrings("%WINDIR%") & "\temp\admin_test.tmp"
    
    Dim fileHandle
    Set fileHandle = objFSO.CreateTextFile(tempFile, True)
    
    If Err.Number = 0 Then
        fileHandle.Close
        objFSO.DeleteFile tempFile
        IsAdmin = True
    Else
        IsAdmin = False
    End If
    
    On Error GoTo 0
    Set fileHandle = Nothing
End Function

Function PerformSystemCheck()
    ' 执行系统检查
    WriteLog "执行系统完整性检查..."
    
    On Error Resume Next
    
    ' 检查关键系统文件
    Dim systemFiles, i, fileExists
    systemFiles = Array( _
        objShell.ExpandEnvironmentStrings("%WINDIR%") & "\System32\kernel32.dll", _
        objShell.ExpandEnvironmentStrings("%WINDIR%") & "\System32\user32.dll", _
        objShell.ExpandEnvironmentStrings("%WINDIR%") & "\System32\ntdll.dll" _
    )
    
    fileExists = True
    For i = 0 To UBound(systemFiles)
        If Not objFSO.FileExists(systemFiles(i)) Then
            WriteLog "关键系统文件缺失: " & systemFiles(i)
            fileExists = False
        End If
    Next
    
    ' 检查磁盘空间
    Dim drive, freeSpace
    Set drive = objFSO.GetDrive(objFSO.GetDriveName(currentDir))
    freeSpace = drive.FreeSpace / 1024 / 1024 / 1024 ' GB
    
    WriteLog "可用磁盘空间: " & Round(freeSpace, 2) & " GB"
    
    If freeSpace < 1 Then
        WriteLog "警告: 磁盘空间不足"
        fileExists = False
    End If
    
    On Error GoTo 0
    Set drive = Nothing
    
    PerformSystemCheck = fileExists
End Function

Function FindPython()
    Dim pythonPaths, i, testPath, result
    
    ' 扩展的Python搜索路径
    pythonPaths = Array( _
        "python", _
        "python3", _
        "py", _
        "C:\Python310\python.exe", _
        "C:\Python311\python.exe", _
        "C:\Python312\python.exe", _
        "C:\Python313\python.exe", _
        "C:\Python314\python.exe", _
        objShell.ExpandEnvironmentStrings("%LOCALAPPDATA%") & "\Programs\Python\Python310\python.exe", _
        objShell.ExpandEnvironmentStrings("%LOCALAPPDATA%") & "\Programs\Python\Python311\python.exe", _
        objShell.ExpandEnvironmentStrings("%LOCALAPPDATA%") & "\Programs\Python\Python312\python.exe", _
        objShell.ExpandEnvironmentStrings("%LOCALAPPDATA%") & "\Programs\Python\Python313\python.exe", _
        objShell.ExpandEnvironmentStrings("%LOCALAPPDATA%") & "\Programs\Python\Python314\python.exe", _
        objShell.ExpandEnvironmentStrings("%PROGRAMFILES%") & "\Python310\python.exe", _
        objShell.ExpandEnvironmentStrings("%PROGRAMFILES%") & "\Python311\python.exe", _
        objShell.ExpandEnvironmentStrings("%PROGRAMFILES%") & "\Python312\python.exe" _
    )
    
    FindPython = ""
    
    For i = 0 To UBound(pythonPaths)
        testPath = pythonPaths(i)
        
        ' 测试Python是否可用
        On Error Resume Next
        result = objShell.Run(testPath & " --version", 0, True)
        If Err.Number = 0 And result = 0 Then
            FindPython = testPath
            WriteLog "找到Python: " & testPath
            Exit For
        End If
        On Error GoTo 0
    Next
End Function

Sub CreateSuperConfig()
    ' 创建超级配置文件
    On Error Resume Next
    
    Dim configContent
    configContent = "{" & vbCrLf & _
                   "  ""super_mode"": true," & vbCrLf & _
                   "  ""all_features_enabled"": true," & vbCrLf & _
                   "  ""device_fingerprint_cleaner"": true," & vbCrLf & _
                   "  ""augment_account_resetter"": true," & vbCrLf & _
                   "  ""super_browser_cleaner"": true," & vbCrLf & _
                   "  ""nuclear_system_reset"": true," & vbCrLf & _
                   "  ""advanced_security"": true," & vbCrLf & _
                   "  ""auto_backup"": true," & vbCrLf & _
                   "  ""admin_privileges"": " & LCase(CStr(IsAdmin())) & "," & vbCrLf & _
                   "  ""activation_time"": """ & Now() & """," & vbCrLf & _
                   "  ""version"": ""super""" & vbCrLf & _
                   "}"
    
    Dim configFileHandle
    Set configFileHandle = objFSO.CreateTextFile(configFile, True)
    If Err.Number = 0 Then
        configFileHandle.Write configContent
        configFileHandle.Close
        WriteLog "超级配置文件创建成功: " & configFile
    Else
        WriteLog "创建配置文件失败: " & Err.Description
    End If
    
    On Error GoTo 0
    Set configFileHandle = Nothing
End Sub

Function ActivateAllFeatures()
    ' 激活所有功能
    WriteLog "激活设备指纹清理功能..."
    WriteLog "激活Augment账号重置功能..."
    WriteLog "激活超级浏览器清理功能..."
    WriteLog "激活核弹级系统重置功能..."
    WriteLog "激活军用级安全保护..."
    
    ' 创建功能激活标记文件
    On Error Resume Next
    
    Dim featureFiles, i
    featureFiles = Array( _
        objFSO.BuildPath(currentDir, ".device_fingerprint_enabled"), _
        objFSO.BuildPath(currentDir, ".augment_reset_enabled"), _
        objFSO.BuildPath(currentDir, ".super_browser_enabled"), _
        objFSO.BuildPath(currentDir, ".nuclear_reset_enabled"), _
        objFSO.BuildPath(currentDir, ".all_features_unlocked") _
    )
    
    For i = 0 To UBound(featureFiles)
        Dim featureFile
        Set featureFile = objFSO.CreateTextFile(featureFiles(i), True)
        If Err.Number = 0 Then
            featureFile.WriteLine "ACTIVATED_BY_SUPER_LAUNCHER_" & Now()
            featureFile.Close
        End If
        Set featureFile = Nothing
    Next
    
    On Error GoTo 0
    
    ActivateAllFeatures = True
End Function

Sub SetSuperEnvironment()
    ' 设置超级环境变量
    On Error Resume Next
    
    objShell.Environment("Process")("AUGMENT_SUPER_MODE") = "1"
    objShell.Environment("Process")("AUGMENT_ALL_FEATURES") = "1"
    objShell.Environment("Process")("AUGMENT_NO_LIMITS") = "1"
    objShell.Environment("Process")("AUGMENT_ADMIN_MODE") = CStr(IsAdmin())
    objShell.Environment("Process")("AUGMENT_LAUNCH_TIME") = CStr(Now())
    
    WriteLog "超级环境变量设置完成"
    
    On Error GoTo 0
End Sub

Sub WriteLog(message)
    Dim logFileHandle
    On Error Resume Next
    Set logFileHandle = objFSO.OpenTextFile(logFile, 8, True)
    If Err.Number = 0 Then
        logFileHandle.WriteLine "[" & Now() & "] " & message
        logFileHandle.Close
    End If
    On Error GoTo 0
    Set logFileHandle = Nothing
End Sub
