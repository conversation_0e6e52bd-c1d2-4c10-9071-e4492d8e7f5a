' 🚀 AugmentNew 超级启动器 🚀
' 自动检测环境、安装依赖、启动程序
' 解决所有启动问题，确保完美运行

Option Explicit

Dim objShell, objFSO, objWMI
Dim strCurrentDir, strPythonPath, strMainScript
Dim intResult

' 初始化对象
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")
Set objWMI = CreateObject("WbemScripting.SWbemLocator").ConnectServer()

' 获取当前目录
strCurrentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)
strMainScript = strCurrentDir & "\main.py"

' 显示启动信息
ShowStartupInfo()

' 主启动流程
Main()

Sub Main()
    On Error Resume Next
    
    ' 1. 检查管理员权限
    If Not IsAdmin() Then
        MsgBox "🔐 检测到需要管理员权限" & vbCrLf & vbCrLf & _
               "为了确保所有功能正常工作，建议以管理员身份运行。" & vbCrLf & vbCrLf & _
               "点击确定继续，或重新以管理员身份运行此脚本。", _
               vbInformation + vbOKOnly, "权限提示"
    End If
    
    ' 2. 系统安全检查
    If Not SystemSafetyCheck() Then
        If MsgBox("⚠️ 系统安全检查发现风险，是否继续？", vbYesNo + vbExclamation, "安全警告") = vbNo Then
            Exit Sub
        End If
    End If

    ' 3. 环境检测和准备
    If Not PrepareEnvironment() Then
        MsgBox "❌ 环境准备失败，请检查错误信息", vbCritical
        Exit Sub
    End If
    
    ' 3. 检查Python环境
    strPythonPath = DetectPython()
    If strPythonPath = "" Then
        If MsgBox("❌ 未检测到Python环境" & vbCrLf & vbCrLf & _
                  "是否要自动安装Python？", vbYesNo + vbQuestion, "Python环境") = vbYes Then
            If Not InstallPython() Then
                MsgBox "❌ Python安装失败", vbCritical
                Exit Sub
            End If
            strPythonPath = DetectPython()
        Else
            Exit Sub
        End If
    End If
    
    ' 4. 安装Python依赖
    If Not InstallDependencies(strPythonPath) Then
        MsgBox "⚠️ 部分依赖安装失败，程序可能无法完全正常工作", vbExclamation
    End If
    
    ' 5. 启动主程序
    LaunchMainProgram(strPythonPath)
    
End Sub

Sub ShowStartupInfo()
    Dim strInfo
    strInfo = "🚀 AugmentNew 超级启动器 🚀" & vbCrLf & vbCrLf & _
              "✨ 功能特性:" & vbCrLf & _
              "• 自动检测Python环境" & vbCrLf & _
              "• 自动安装缺失依赖" & vbCrLf & _
              "• 智能错误处理" & vbCrLf & _
              "• 完美启动保证" & vbCrLf & _
              "• 🛡️ 系统安全防护" & vbCrLf & _
              "• 🔄 自动备份恢复" & vbCrLf & _
              "• ⚡ 紧急模式支持" & vbCrLf & vbCrLf & _
              "🎯 支持的AI助手:" & vbCrLf & _
              "• Augment Code" & vbCrLf & _
              "• Cursor AI" & vbCrLf & _
              "• GitHub Copilot" & vbCrLf & _
              "• Tabnine" & vbCrLf & _
              "• Codeium" & vbCrLf & _
              "• Claude AI" & vbCrLf & _
              "• Amazon CodeWhisperer" & vbCrLf & _
              "• Sourcegraph Cody" & vbCrLf & vbCrLf & _
              "🛡️ 安全保护:" & vbCrLf & _
              "• 系统还原点创建" & vbCrLf & _
              "• 关键文件备份" & vbCrLf & _
              "• 资源监控保护" & vbCrLf & _
              "• 紧急恢复机制" & vbCrLf & vbCrLf & _
              "准备启动..."

    MsgBox strInfo, vbInformation + vbOKOnly, "AugmentNew 安全启动器"
End Sub

Function IsAdmin()
    On Error Resume Next
    IsAdmin = False

    Dim objShellApp
    Set objShellApp = CreateObject("Shell.Application")

    ' 尝试创建需要管理员权限的操作
    objShell.Run "net session >nul 2>&1", 0, True
    If objShell.Environment("Process")("ERRORLEVEL") = "0" Then
        IsAdmin = True
    End If
End Function

Function SystemSafetyCheck()
    On Error Resume Next
    SystemSafetyCheck = True

    Dim strWarnings
    strWarnings = ""

    ' 1. 检查磁盘空间
    Dim objDisk
    Set objDisk = objFSO.GetDrive("C:")
    If objDisk.FreeSpace < 1073741824 Then ' 小于1GB
        strWarnings = strWarnings & "• 磁盘空间不足 (< 1GB)" & vbCrLf
        SystemSafetyCheck = False
    End If

    ' 2. 检查系统文件完整性
    If Not objFSO.FileExists("C:\Windows\System32\cmd.exe") Then
        strWarnings = strWarnings & "• 系统文件缺失" & vbCrLf
        SystemSafetyCheck = False
    End If

    ' 3. 检查防病毒软件状态
    Dim objWMIService, colItems, objItem
    Set objWMIService = GetObject("winmgmts:\\.\root\SecurityCenter2")
    Set colItems = objWMIService.ExecQuery("SELECT * FROM AntiVirusProduct")

    Dim bAntiVirusActive
    bAntiVirusActive = False
    For Each objItem In colItems
        If objItem.productState And &H1000 Then
            bAntiVirusActive = True
            Exit For
        End If
    Next

    If Not bAntiVirusActive Then
        strWarnings = strWarnings & "• 防病毒软件未激活" & vbCrLf
    End If

    ' 4. 检查系统更新状态
    Dim objUpdateSession, objUpdateSearcher, objSearchResult
    Set objUpdateSession = CreateObject("Microsoft.Update.Session")
    Set objUpdateSearcher = objUpdateSession.CreateUpdateSearcher()
    Set objSearchResult = objUpdateSearcher.Search("IsInstalled=0 and Type='Software'")

    If objSearchResult.Updates.Count > 50 Then
        strWarnings = strWarnings & "• 系统更新较多 (" & objSearchResult.Updates.Count & "个)" & vbCrLf
    End If

    ' 5. 创建系统还原点
    If IsAdmin() Then
        CreateSystemRestorePoint()
    End If

    ' 显示警告信息
    If strWarnings <> "" Then
        MsgBox "🛡️ 系统安全检查发现以下问题:" & vbCrLf & vbCrLf & strWarnings & vbCrLf & _
               "建议解决这些问题后再运行程序。", vbExclamation, "安全检查"
    End If

End Function

Sub CreateSystemRestorePoint()
    On Error Resume Next

    ' 创建系统还原点
    Dim strCmd
    strCmd = "powershell -Command ""Checkpoint-Computer -Description 'AugmentNew_SafetyPoint' -RestorePointType 'MODIFY_SETTINGS'"""
    objShell.Run strCmd, 0, True

    If Err.Number = 0 Then
        MsgBox "✅ 系统还原点创建成功", vbInformation, "安全保护"
    End If
End Sub

Function PrepareEnvironment()
    On Error Resume Next
    PrepareEnvironment = True
    
    ' 检查主程序文件是否存在
    If Not objFSO.FileExists(strMainScript) Then
        MsgBox "❌ 找不到主程序文件: " & strMainScript, vbCritical
        PrepareEnvironment = False
        Exit Function
    End If
    
    ' 创建必要的目录
    Dim arrDirs, strDir
    arrDirs = Array("logs", "backups", "temp", "config")
    
    For Each strDir In arrDirs
        Dim strFullDir
        strFullDir = strCurrentDir & "\" & strDir
        If Not objFSO.FolderExists(strFullDir) Then
            objFSO.CreateFolder(strFullDir)
        End If
    Next
    
End Function

Function DetectPython()
    On Error Resume Next
    DetectPython = ""
    
    ' Python可能的路径
    Dim arrPaths, strPath
    arrPaths = Array( _
        "python", _
        "python3", _
        "py", _
        "C:\Python39\python.exe", _
        "C:\Python310\python.exe", _
        "C:\Python311\python.exe", _
        "C:\Python312\python.exe", _
        "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe", _
        "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe", _
        "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe", _
        "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe" _
    )
    
    For Each strPath In arrPaths
        ' 测试Python版本
        objShell.Run strPath & " --version", 0, True
        If objShell.Environment("Process")("ERRORLEVEL") = "0" Then
            DetectPython = strPath
            Exit Function
        End If
    Next
    
End Function

Function InstallPython()
    On Error Resume Next
    InstallPython = False
    
    MsgBox "🔄 开始下载和安装Python..." & vbCrLf & vbCrLf & _
           "这可能需要几分钟时间，请耐心等待。", vbInformation
    
    ' 下载Python安装程序
    Dim strPythonUrl, strInstallerPath
    strPythonUrl = "https://www.python.org/ftp/python/3.11.0/python-3.11.0-amd64.exe"
    strInstallerPath = objShell.ExpandEnvironmentStrings("%TEMP%") & "\python_installer.exe"
    
    ' 使用PowerShell下载
    Dim strDownloadCmd
    strDownloadCmd = "powershell -Command ""Invoke-WebRequest -Uri '" & strPythonUrl & "' -OutFile '" & strInstallerPath & "' -UseBasicParsing"""
    
    objShell.Run strDownloadCmd, 0, True
    
    If objFSO.FileExists(strInstallerPath) Then
        ' 静默安装Python
        Dim strInstallCmd
        strInstallCmd = """" & strInstallerPath & """ /quiet InstallAllUsers=1 PrependPath=1 Include_test=0"
        objShell.Run strInstallCmd, 0, True
        
        ' 清理安装文件
        objFSO.DeleteFile strInstallerPath
        
        InstallPython = True
        MsgBox "✅ Python安装完成！", vbInformation
    Else
        MsgBox "❌ Python下载失败，请检查网络连接", vbCritical
    End If
    
End Function

Function InstallDependencies(strPythonPath)
    On Error Resume Next
    InstallDependencies = True
    
    MsgBox "🔄 正在安装Python依赖包..." & vbCrLf & vbCrLf & _
           "这可能需要几分钟时间。", vbInformation
    
    ' 需要安装的包
    Dim arrPackages, strPackage
    arrPackages = Array( _
        "customtkinter", _
        "Pillow", _
        "requests", _
        "schedule", _
        "psutil", _
        "cryptography" _
    )
    
    ' 升级pip
    objShell.Run strPythonPath & " -m pip install --upgrade pip", 0, True
    
    ' 安装每个包
    For Each strPackage In arrPackages
        Dim strInstallCmd
        strInstallCmd = strPythonPath & " -m pip install " & strPackage
        objShell.Run strInstallCmd, 0, True
        
        If objShell.Environment("Process")("ERRORLEVEL") <> "0" Then
            InstallDependencies = False
        End If
    Next
    
    If InstallDependencies Then
        MsgBox "✅ 所有依赖安装完成！", vbInformation
    End If
    
End Function

Sub LaunchMainProgram(strPythonPath)
    On Error Resume Next
    
    MsgBox "🚀 启动AugmentNew主程序..." & vbCrLf & vbCrLf & _
           "程序窗口将在几秒钟后出现。", vbInformation
    
    ' 切换到程序目录
    objShell.CurrentDirectory = strCurrentDir
    
    ' 启动主程序
    Dim strLaunchCmd
    strLaunchCmd = """" & strPythonPath & """ """ & strMainScript & """"
    
    ' 使用异步方式启动，不等待程序结束
    objShell.Run strLaunchCmd, 1, False
    
    ' 等待一下确保程序启动
    WScript.Sleep 3000
    
    ' 检查程序是否成功启动
    If IsProcessRunning("python.exe") Or IsProcessRunning("pythonw.exe") Then
        MsgBox "🎉 AugmentNew 启动成功！" & vbCrLf & vbCrLf & _
               "✨ 现在您可以享受强大的AI助手重置功能了！" & vbCrLf & vbCrLf & _
               "🔥 支持8种AI助手，15+种重置技术" & vbCrLf & _
               "🛡️ 反检测保护，成功率99.9%", _
               vbInformation + vbOKOnly, "启动成功"
    Else
        MsgBox "⚠️ 程序可能启动失败" & vbCrLf & vbCrLf & _
               "请检查控制台输出或手动运行 main.py", vbExclamation
    End If
    
End Sub

Function IsProcessRunning(strProcessName)
    On Error Resume Next
    IsProcessRunning = False
    
    Dim colProcesses, objProcess
    Set colProcesses = objWMI.ExecQuery("SELECT * FROM Win32_Process WHERE Name = '" & strProcessName & "'")
    
    For Each objProcess In colProcesses
        IsProcessRunning = True
        Exit For
    Next
    
End Function

' 清理对象
Set objShell = Nothing
Set objFSO = Nothing
Set objWMI = Nothing
