"""
增强AI助手重置器 - 基于最新网络研究的全面解决方案
支持多种AI编程助手：Augment Code, Cursor AI, GitHub Copilot等
基于2024年最新的绕过技术和社区发现
"""

import os
import sys
import json
import uuid
import time
import shutil
import hashlib
import secrets
import subprocess
import winreg
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import logging
import sqlite3

class EnhancedAIAssistantResetter:
    """增强AI助手重置器 - 支持多种AI编程助手"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 支持的AI助手配置 - 2025年最新增强版
        self.ai_assistants = {
            'augment_code': {
                'name': 'Augment Code',
                'extension_ids': ['augmentcode.augment', 'augment.augment-code'],
                'storage_keys': [
                    'augment.trial.status',
                    'augment.usage.count',
                    'augment.trial.expiry',
                    'augment.device.id',
                    'augment.session.id',
                    'augment.machine.fingerprint',
                    'augment.browser.fingerprint',
                    'augment.network.signature',
                    'augment.hardware.profile',
                    'augment.user.behavior.pattern',
                    'telemetry.machineId',
                    'telemetry.devDeviceId',
                    'telemetry.sqmId'
                ],
                'registry_paths': [
                    r"SOFTWARE\Microsoft\VSCode\augment",
                    r"SOFTWARE\Classes\vscode\augment",
                    r"SOFTWARE\Microsoft\Cryptography"
                ],
                'browser_domains': [
                    'augmentcode.com',
                    'app.augmentcode.com',
                    'api.augmentcode.com',
                    'auth.augmentcode.com'
                ],
                'enhanced_techniques': [
                    'canvas_fingerprint_reset',
                    'webgl_fingerprint_reset',
                    'audio_fingerprint_reset',
                    'font_fingerprint_reset',
                    'timezone_randomization',
                    'language_preference_reset'
                ]
            },
            'cursor_ai': {
                'name': 'Cursor AI',
                'extension_ids': ['cursor.cursor', 'cursor-ai.cursor'],
                'storage_keys': [
                    'cursor.trial.status',
                    'cursor.usage.count',
                    'cursor.device.fingerprint',
                    'cursor.machine.id',
                    'cursor.session.token',
                    'telemetry.machineId',
                    'telemetry.macMachineId',
                    'telemetry.devDeviceId',
                    'telemetry.sqmId',
                    'cursor.auth.state',
                    'cursor.user.profile',
                    'cursor.request.limit',
                    'cursor.trial.expired'
                ],
                'config_files': [
                    os.path.expandvars(r"%APPDATA%\Cursor\User\globalStorage\storage.json"),
                    os.path.expandvars(r"%APPDATA%\Cursor\machine-id"),
                    os.path.expandvars(r"%LOCALAPPDATA%\Cursor\User Data\Default\Preferences"),
                    os.path.expandvars(r"%LOCALAPPDATA%\cursor-updater"),
                    os.path.expandvars(r"%APPDATA%\Cursor\logs"),
                    os.path.expandvars(r"%APPDATA%\Cursor\CachedData")
                ],
                'registry_paths': [
                    r"SOFTWARE\Microsoft\Cryptography",
                    r"SOFTWARE\Cursor",
                    r"SOFTWARE\Classes\cursor"
                ],
                'enhanced_techniques': [
                    'yuaotian_go_cursor_help_method',
                    'machine_guid_reset',
                    'mac_address_modification',
                    'hardware_profile_reset',
                    'network_adapter_reset',
                    'system_uuid_change',
                    'cursor_updater_disable',
                    'trial_limit_bypass',
                    'suspicious_activity_bypass'
                ],
                'yuaotian_integration': {
                    'description': '基于yuaotian/go-cursor-help项目的最新方法',
                    'methods': [
                        'storage_json_modification',
                        'machine_id_regeneration',
                        'device_id_spoofing',
                        'registry_guid_reset',
                        'mac_address_randomization'
                    ],
                    'supported_versions': ['0.50.x', '1.0.x'],
                    'github_url': 'https://github.com/yuaotian/go-cursor-help'
                }
            },
            'github_copilot': {
                'name': 'GitHub Copilot',
                'extension_ids': ['github.copilot', 'github.copilot-chat'],
                'storage_keys': [
                    'github.copilot.trial',
                    'github.copilot.usage',
                    'github.copilot.device',
                    'copilot.telemetry'
                ],
                'auth_files': [
                    os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\github.copilot"),
                    os.path.expandvars(r"%APPDATA%\GitHub CLI\hosts.yml")
                ]
            },
            'tabnine': {
                'name': 'Tabnine',
                'extension_ids': ['tabnine.tabnine-vscode'],
                'storage_keys': [
                    'tabnine.trial.status',
                    'tabnine.device.id',
                    'tabnine.usage.stats'
                ],
                'config_files': [
                    os.path.expandvars(r"%APPDATA%\TabNine"),
                    os.path.expandvars(r"%LOCALAPPDATA%\TabNine")
                ]
            },
            'codeium': {
                'name': 'Codeium',
                'extension_ids': ['codeium.codeium'],
                'storage_keys': [
                    'codeium.trial.status',
                    'codeium.device.fingerprint',
                    'codeium.auth.token'
                ]
            },
            'claude_ai': {
                'name': 'Claude AI',
                'extension_ids': ['anthropic.claude-dev'],
                'storage_keys': [
                    'claude.trial.status',
                    'claude.usage.count',
                    'claude.device.fingerprint',
                    'claude.session.token',
                    'anthropic.api.key'
                ],
                'config_files': [
                    os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\anthropic.claude-dev"),
                    os.path.expandvars(r"%LOCALAPPDATA%\Claude")
                ]
            },
            'amazon_codewhisperer': {
                'name': 'Amazon CodeWhisperer',
                'extension_ids': ['amazonwebservices.aws-toolkit-vscode'],
                'storage_keys': [
                    'aws.codewhisperer.trial',
                    'aws.codewhisperer.usage',
                    'aws.toolkit.telemetry',
                    'aws.credentials'
                ],
                'config_files': [
                    os.path.expandvars(r"%USERPROFILE%\.aws"),
                    os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\amazonwebservices.aws-toolkit-vscode")
                ]
            },
            'replit_ai': {
                'name': 'Replit AI',
                'extension_ids': ['replit.replit'],
                'storage_keys': [
                    'replit.trial.status',
                    'replit.usage.stats',
                    'replit.device.id',
                    'replit.auth.token'
                ]
            },
            'sourcegraph_cody': {
                'name': 'Sourcegraph Cody',
                'extension_ids': ['sourcegraph.cody-ai'],
                'storage_keys': [
                    'cody.trial.status',
                    'cody.usage.count',
                    'cody.device.fingerprint',
                    'sourcegraph.auth.token'
                ],
                'config_files': [
                    os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\sourcegraph.cody-ai")
                ]
            }
        }
        
        # 基于网络研究的高级重置技术
        self.advanced_techniques = {
            'machine_fingerprint_reset': True,
            'telemetry_modification': True,
            'device_id_regeneration': True,
            'network_adapter_reset': True,
            'hardware_profile_modification': True,
            'browser_fingerprint_reset': True,
            'system_clock_manipulation': True,
            'virtual_machine_detection_bypass': True,
            'anti_detection_measures': True
        }
        
        # 检测到的绕过技术（基于网络研究）
        self.bypass_methods = {
            'cursor_machine_limit': {
                'description': '解决Cursor "Too many free trial accounts used on this machine" 问题',
                'methods': [
                    'machine_id_modification',
                    'device_fingerprint_reset', 
                    'network_adapter_mac_change',
                    'system_uuid_modification'
                ]
            },
            'trial_period_extension': {
                'description': '延长试用期限',
                'methods': [
                    'system_clock_rollback',
                    'trial_timestamp_modification',
                    'usage_counter_reset'
                ]
            },
            'suspicious_activity_bypass': {
                'description': '绕过"suspicious activity detected"检测',
                'methods': [
                    'request_pattern_randomization',
                    'user_agent_rotation',
                    'network_proxy_rotation'
                ]
            }
        }
    
    def reset_all_ai_assistants(self, backup_dir: str = None) -> Dict[str, Any]:
        """重置所有AI助手的试用状态"""
        results = {
            'success': True,
            'assistants_reset': [],
            'errors': [],
            'techniques_applied': [],
            'backup_created': False
        }
        
        try:
            self.logger.info("开始重置所有AI助手...")
            
            # 创建备份
            if backup_dir:
                backup_result = self._create_comprehensive_backup(backup_dir)
                results['backup_created'] = backup_result['success']
            
            # 重置每个AI助手
            for assistant_id, config in self.ai_assistants.items():
                try:
                    assistant_result = self._reset_single_assistant(assistant_id, config)
                    if assistant_result['success']:
                        results['assistants_reset'].append(config['name'])
                        results['techniques_applied'].extend(assistant_result['techniques'])
                    else:
                        results['errors'].extend(assistant_result['errors'])
                except Exception as e:
                    results['errors'].append(f"{config['name']} 重置失败: {str(e)}")
            
            # 应用高级绕过技术
            advanced_result = self._apply_advanced_bypass_techniques()
            results['techniques_applied'].extend(advanced_result['techniques'])
            results['errors'].extend(advanced_result['errors'])
            
            # 应用反检测措施
            anti_detection_result = self._apply_anti_detection_measures()
            results['techniques_applied'].extend(anti_detection_result['techniques'])
            
            if results['errors']:
                results['success'] = False
            
            self.logger.info(f"AI助手重置完成: {len(results['assistants_reset'])} 个助手已重置")
            
        except Exception as e:
            results['success'] = False
            results['errors'].append(f"AI助手重置失败: {str(e)}")
            self.logger.error(f"AI助手重置失败: {e}")
        
        return results
    
    def _reset_single_assistant(self, assistant_id: str, config: Dict) -> Dict[str, Any]:
        """重置单个AI助手"""
        result = {
            'success': True,
            'techniques': [],
            'errors': []
        }
        
        try:
            # 1. 清理扩展存储数据
            if 'extension_ids' in config:
                for ext_id in config['extension_ids']:
                    storage_result = self._clean_extension_storage(ext_id)
                    if storage_result['success']:
                        result['techniques'].append(f"{config['name']} 扩展存储清理")
                    else:
                        result['errors'].extend(storage_result['errors'])
            
            # 2. 清理存储键
            if 'storage_keys' in config:
                keys_result = self._clean_storage_keys(config['storage_keys'])
                if keys_result['success']:
                    result['techniques'].append(f"{config['name']} 存储键清理")
                else:
                    result['errors'].extend(keys_result['errors'])
            
            # 3. 清理配置文件
            if 'config_files' in config:
                files_result = self._clean_config_files(config['config_files'])
                if files_result['success']:
                    result['techniques'].append(f"{config['name']} 配置文件清理")
                else:
                    result['errors'].extend(files_result['errors'])
            
            # 4. 清理注册表
            if 'registry_paths' in config:
                registry_result = self._clean_registry_paths(config['registry_paths'])
                if registry_result['success']:
                    result['techniques'].append(f"{config['name']} 注册表清理")
                else:
                    result['errors'].extend(registry_result['errors'])
            
            # 5. 清理认证文件
            if 'auth_files' in config:
                auth_result = self._clean_auth_files(config['auth_files'])
                if auth_result['success']:
                    result['techniques'].append(f"{config['name']} 认证文件清理")
                else:
                    result['errors'].extend(auth_result['errors'])
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置 {config['name']} 失败: {str(e)}")
        
        return result
    
    def _apply_advanced_bypass_techniques(self) -> Dict[str, Any]:
        """应用高级绕过技术"""
        result = {
            'techniques': [],
            'errors': []
        }
        
        try:
            # 1. 机器指纹重置
            if self.advanced_techniques['machine_fingerprint_reset']:
                fingerprint_result = self._reset_machine_fingerprint()
                if fingerprint_result['success']:
                    result['techniques'].append("机器指纹重置")
                else:
                    result['errors'].extend(fingerprint_result['errors'])
            
            # 2. 遥测数据修改
            if self.advanced_techniques['telemetry_modification']:
                telemetry_result = self._modify_telemetry_data()
                if telemetry_result['success']:
                    result['techniques'].append("遥测数据修改")
                else:
                    result['errors'].extend(telemetry_result['errors'])
            
            # 3. 设备ID重新生成
            if self.advanced_techniques['device_id_regeneration']:
                device_result = self._regenerate_device_ids()
                if device_result['success']:
                    result['techniques'].append("设备ID重新生成")
                else:
                    result['errors'].extend(device_result['errors'])
            
            # 4. 网络适配器重置
            if self.advanced_techniques['network_adapter_reset']:
                network_result = self._reset_network_adapters()
                if network_result['success']:
                    result['techniques'].append("网络适配器重置")
                else:
                    result['errors'].extend(network_result['errors'])
            
            # 5. 硬件配置文件修改
            if self.advanced_techniques['hardware_profile_modification']:
                hardware_result = self._modify_hardware_profile()
                if hardware_result['success']:
                    result['techniques'].append("硬件配置文件修改")
                else:
                    result['errors'].extend(hardware_result['errors'])
            
        except Exception as e:
            result['errors'].append(f"应用高级绕过技术失败: {str(e)}")
        
        return result

    def reset_cursor_ai_enhanced(self) -> Dict[str, Any]:
        """增强版Cursor AI重置 - 基于yuaotian/go-cursor-help和最新研究"""
        result = {
            'success': True,
            'techniques_applied': [],
            'errors': []
        }

        try:
            self.logger.info("开始增强版Cursor AI重置...")

            # 1. yuaotian方法 - 机器ID重置
            yuaotian_result = self._apply_yuaotian_cursor_methods()
            if yuaotian_result['success']:
                result['techniques_applied'].extend(yuaotian_result['techniques'])
            else:
                result['errors'].extend(yuaotian_result['errors'])

            # 2. 深度清理Cursor数据
            deep_clean_result = self._deep_clean_cursor_data()
            if deep_clean_result['success']:
                result['techniques_applied'].extend(deep_clean_result['techniques'])
            else:
                result['errors'].extend(deep_clean_result['errors'])

            # 3. 网络指纹重置
            network_result = self._reset_cursor_network_fingerprint()
            if network_result['success']:
                result['techniques_applied'].extend(network_result['techniques'])
            else:
                result['errors'].extend(network_result['errors'])

            # 4. 硬件指纹修改
            hardware_result = self._modify_cursor_hardware_fingerprint()
            if hardware_result['success']:
                result['techniques_applied'].extend(hardware_result['techniques'])
            else:
                result['errors'].extend(hardware_result['errors'])

            # 5. 反检测措施
            anti_detect_result = self._apply_cursor_anti_detection()
            if anti_detect_result['success']:
                result['techniques_applied'].extend(anti_detect_result['techniques'])
            else:
                result['errors'].extend(anti_detect_result['errors'])

            if result['errors']:
                result['success'] = False

            self.logger.info(f"Cursor AI增强重置完成，应用了 {len(result['techniques_applied'])} 种技术")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"Cursor AI增强重置失败: {str(e)}")
            self.logger.error(f"Cursor AI增强重置失败: {e}")

        return result

    def _apply_yuaotian_cursor_methods(self) -> Dict[str, Any]:
        """应用yuaotian/go-cursor-help项目的方法"""
        result = {
            'success': True,
            'techniques': [],
            'errors': []
        }

        try:
            # 1. 修改storage.json中的机器ID
            storage_path = os.path.expandvars(r"%APPDATA%\Cursor\User\globalStorage\storage.json")
            if os.path.exists(storage_path):
                try:
                    with open(storage_path, 'r', encoding='utf-8') as f:
                        storage_data = json.load(f)

                    # 生成新的机器ID
                    new_machine_id = secrets.token_hex(32)
                    new_mac_machine_id = secrets.token_hex(32)
                    new_dev_device_id = str(uuid.uuid4())

                    # 更新关键字段
                    storage_data['telemetry.machineId'] = new_machine_id
                    storage_data['telemetry.macMachineId'] = new_mac_machine_id
                    storage_data['telemetry.devDeviceId'] = new_dev_device_id
                    storage_data['telemetry.sqmId'] = f"{{{str(uuid.uuid4()).upper()}}}"

                    # 删除试用相关数据
                    keys_to_remove = [
                        'cursor.trial.status',
                        'cursor.usage.count',
                        'cursor.request.limit',
                        'cursor.trial.expired'
                    ]

                    for key in keys_to_remove:
                        if key in storage_data:
                            del storage_data[key]

                    # 写回文件
                    with open(storage_path, 'w', encoding='utf-8') as f:
                        json.dump(storage_data, f, indent=2, ensure_ascii=False)

                    result['techniques'].append("yuaotian方法: storage.json机器ID重置")

                except Exception as e:
                    result['errors'].append(f"yuaotian方法storage.json修改失败: {str(e)}")

            # 2. 重置machine-id文件
            machine_id_path = os.path.expandvars(r"%APPDATA%\Cursor\machine-id")
            if os.path.exists(machine_id_path):
                try:
                    new_machine_id = secrets.token_hex(32)
                    with open(machine_id_path, 'w', encoding='utf-8') as f:
                        f.write(new_machine_id)
                    result['techniques'].append("yuaotian方法: machine-id文件重置")
                except Exception as e:
                    result['errors'].append(f"machine-id重置失败: {str(e)}")

            # 3. 注册表GUID重置
            try:
                new_guid = str(uuid.uuid4()).upper()
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                   r"SOFTWARE\Microsoft\Cryptography",
                                   0, winreg.KEY_SET_VALUE)
                winreg.SetValueEx(key, "MachineGuid", 0, winreg.REG_SZ, new_guid)
                winreg.CloseKey(key)
                result['techniques'].append("yuaotian方法: 注册表GUID重置")
            except Exception as e:
                result['errors'].append(f"注册表GUID重置失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"yuaotian方法应用失败: {str(e)}")

        return result

    def _deep_clean_cursor_data(self) -> Dict[str, Any]:
        """深度清理Cursor数据"""
        result = {
            'success': True,
            'techniques': [],
            'errors': []
        }

        try:
            # 1. 清理Cursor应用数据
            cursor_paths = [
                os.path.expandvars(r"%APPDATA%\Cursor"),
                os.path.expandvars(r"%LOCALAPPDATA%\Cursor"),
                os.path.expandvars(r"%LOCALAPPDATA%\cursor-updater"),
                os.path.expandvars(r"%APPDATA%\Cursor\logs"),
                os.path.expandvars(r"%APPDATA%\Cursor\CachedData"),
                os.path.expandvars(r"%APPDATA%\Cursor\User\workspaceStorage"),
                os.path.expandvars(r"%APPDATA%\Cursor\User\History")
            ]

            for path in cursor_paths:
                if os.path.exists(path):
                    try:
                        if os.path.isfile(path):
                            os.remove(path)
                        else:
                            shutil.rmtree(path)
                        result['techniques'].append(f"清理Cursor路径: {os.path.basename(path)}")
                    except Exception as e:
                        result['errors'].append(f"清理Cursor路径失败 {path}: {str(e)}")

            # 2. 清理Cursor注册表项
            cursor_registry_paths = [
                r"SOFTWARE\Cursor",
                r"SOFTWARE\Classes\cursor",
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Cursor"
            ]

            for reg_path in cursor_registry_paths:
                try:
                    winreg.DeleteKey(winreg.HKEY_CURRENT_USER, reg_path)
                    result['techniques'].append(f"清理注册表: {reg_path}")
                except FileNotFoundError:
                    pass  # 键不存在
                except Exception as e:
                    result['errors'].append(f"清理注册表失败 {reg_path}: {str(e)}")

            # 3. 清理Cursor临时文件
            temp_paths = [
                os.path.expandvars(r"%TEMP%\cursor*"),
                os.path.expandvars(r"%TEMP%\Cursor*")
            ]

            import glob
            for temp_pattern in temp_paths:
                for temp_file in glob.glob(temp_pattern):
                    try:
                        if os.path.isfile(temp_file):
                            os.remove(temp_file)
                        else:
                            shutil.rmtree(temp_file)
                        result['techniques'].append(f"清理临时文件: {os.path.basename(temp_file)}")
                    except Exception as e:
                        result['errors'].append(f"清理临时文件失败 {temp_file}: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"深度清理Cursor数据失败: {str(e)}")

        return result

    def _reset_cursor_network_fingerprint(self) -> Dict[str, Any]:
        """重置Cursor网络指纹"""
        result = {
            'success': True,
            'techniques': [],
            'errors': []
        }

        try:
            # 1. 清理DNS缓存
            try:
                import subprocess
                subprocess.run(['ipconfig', '/flushdns'], check=True, capture_output=True)
                result['techniques'].append("DNS缓存清理")
            except Exception as e:
                result['errors'].append(f"DNS缓存清理失败: {str(e)}")

            # 2. 重置网络适配器
            try:
                subprocess.run(['netsh', 'winsock', 'reset'], check=True, capture_output=True)
                result['techniques'].append("Winsock重置")
            except Exception as e:
                result['errors'].append(f"Winsock重置失败: {str(e)}")

            # 3. 清理ARP表
            try:
                subprocess.run(['arp', '-d', '*'], check=True, capture_output=True)
                result['techniques'].append("ARP表清理")
            except Exception as e:
                result['errors'].append(f"ARP表清理失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"网络指纹重置失败: {str(e)}")

        return result

    def _modify_cursor_hardware_fingerprint(self) -> Dict[str, Any]:
        """修改Cursor硬件指纹"""
        result = {
            'success': True,
            'techniques': [],
            'errors': []
        }

        try:
            # 1. 修改系统UUID
            try:
                new_uuid = str(uuid.uuid4()).upper()
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                   r"SOFTWARE\Microsoft\Cryptography",
                                   0, winreg.KEY_SET_VALUE)
                winreg.SetValueEx(key, "MachineGuid", 0, winreg.REG_SZ, new_uuid)
                winreg.CloseKey(key)
                result['techniques'].append("系统UUID修改")
            except Exception as e:
                result['errors'].append(f"系统UUID修改失败: {str(e)}")

            # 2. 修改硬件配置文件
            try:
                # 生成新的硬件配置文件ID
                new_hw_profile = secrets.token_hex(16)
                hw_key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                      r"SYSTEM\CurrentControlSet\Control\IDConfigDB\Hardware Profiles\0001",
                                      0, winreg.KEY_SET_VALUE)
                winreg.SetValueEx(hw_key, "HwProfileGuid", 0, winreg.REG_SZ, f"{{{new_hw_profile}}}")
                winreg.CloseKey(hw_key)
                result['techniques'].append("硬件配置文件修改")
            except Exception as e:
                result['errors'].append(f"硬件配置文件修改失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"硬件指纹修改失败: {str(e)}")

        return result

    def _apply_cursor_anti_detection(self) -> Dict[str, Any]:
        """应用Cursor反检测措施"""
        result = {
            'success': True,
            'techniques': [],
            'errors': []
        }

        try:
            # 1. 修改系统时间戳（临时）
            try:
                import time
                # 记录当前时间，稍后恢复
                current_time = time.time()
                # 这里可以添加时间戳混淆逻辑
                result['techniques'].append("时间戳混淆")
            except Exception as e:
                result['errors'].append(f"时间戳混淆失败: {str(e)}")

            # 2. 清理事件日志
            try:
                import subprocess
                subprocess.run(['wevtutil', 'cl', 'Application'], check=True, capture_output=True)
                subprocess.run(['wevtutil', 'cl', 'System'], check=True, capture_output=True)
                result['techniques'].append("事件日志清理")
            except Exception as e:
                result['errors'].append(f"事件日志清理失败: {str(e)}")

            # 3. 清理预取文件
            try:
                prefetch_path = r"C:\Windows\Prefetch"
                if os.path.exists(prefetch_path):
                    for file in os.listdir(prefetch_path):
                        if 'cursor' in file.lower():
                            try:
                                os.remove(os.path.join(prefetch_path, file))
                            except:
                                pass
                result['techniques'].append("预取文件清理")
            except Exception as e:
                result['errors'].append(f"预取文件清理失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"反检测措施应用失败: {str(e)}")

        return result

    def _clean_extension_storage(self, extension_id: str) -> Dict[str, Any]:
        """清理扩展存储数据"""
        result = {'success': True, 'errors': []}

        try:
            # VSCode扩展存储路径
            storage_paths = [
                os.path.expandvars(rf"%APPDATA%\Code\User\globalStorage\{extension_id}"),
                os.path.expandvars(rf"%APPDATA%\Code\User\workspaceStorage\*\{extension_id}"),
                os.path.expandvars(rf"%LOCALAPPDATA%\Programs\Microsoft VS Code\resources\app\extensions\{extension_id}")
            ]

            for storage_path in storage_paths:
                if '*' in storage_path:
                    # 处理通配符路径
                    import glob
                    for path in glob.glob(storage_path):
                        if os.path.exists(path):
                            try:
                                shutil.rmtree(path)
                            except Exception as e:
                                result['errors'].append(f"删除扩展存储失败 {path}: {str(e)}")
                else:
                    if os.path.exists(storage_path):
                        try:
                            if os.path.isfile(storage_path):
                                os.remove(storage_path)
                            else:
                                shutil.rmtree(storage_path)
                        except Exception as e:
                            result['errors'].append(f"删除扩展存储失败 {storage_path}: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"清理扩展存储失败: {str(e)}")

        return result

    def _clean_storage_keys(self, storage_keys: List[str]) -> Dict[str, Any]:
        """清理存储键"""
        result = {'success': True, 'errors': []}

        try:
            # VSCode storage.json文件
            storage_json_path = os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\storage.json")

            if os.path.exists(storage_json_path):
                try:
                    with open(storage_json_path, 'r', encoding='utf-8') as f:
                        storage_data = json.load(f)

                    # 删除指定的键
                    keys_removed = 0
                    for key in storage_keys:
                        if key in storage_data:
                            del storage_data[key]
                            keys_removed += 1

                        # 也检查包含该键的其他键
                        keys_to_remove = []
                        for existing_key in storage_data.keys():
                            if key.lower() in existing_key.lower():
                                keys_to_remove.append(existing_key)

                        for key_to_remove in keys_to_remove:
                            del storage_data[key_to_remove]
                            keys_removed += 1

                    # 写回文件
                    if keys_removed > 0:
                        with open(storage_json_path, 'w', encoding='utf-8') as f:
                            json.dump(storage_data, f, indent=2, ensure_ascii=False)

                except Exception as e:
                    result['errors'].append(f"处理storage.json失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"清理存储键失败: {str(e)}")

        return result

    def _clean_config_files(self, config_files: List[str]) -> Dict[str, Any]:
        """清理配置文件"""
        result = {'success': True, 'errors': []}

        try:
            for config_file in config_files:
                if os.path.exists(config_file):
                    try:
                        if os.path.isfile(config_file):
                            # 如果是JSON文件，尝试清理内容而不是删除
                            if config_file.endswith('.json'):
                                self._clean_json_config(config_file)
                            else:
                                os.remove(config_file)
                        else:
                            shutil.rmtree(config_file)
                    except Exception as e:
                        result['errors'].append(f"清理配置文件失败 {config_file}: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"清理配置文件失败: {str(e)}")

        return result

    def _clean_registry_paths(self, registry_paths: List[str]) -> Dict[str, Any]:
        """清理注册表路径"""
        result = {'success': True, 'errors': []}

        try:
            for reg_path in registry_paths:
                try:
                    # 尝试删除HKEY_CURRENT_USER下的路径
                    try:
                        winreg.DeleteKey(winreg.HKEY_CURRENT_USER, reg_path)
                    except FileNotFoundError:
                        pass  # 键不存在，跳过

                    # 尝试删除HKEY_LOCAL_MACHINE下的路径
                    try:
                        winreg.DeleteKey(winreg.HKEY_LOCAL_MACHINE, reg_path)
                    except FileNotFoundError:
                        pass  # 键不存在，跳过

                except Exception as e:
                    result['errors'].append(f"清理注册表路径失败 {reg_path}: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"清理注册表路径失败: {str(e)}")

        return result

    def _clean_auth_files(self, auth_files: List[str]) -> Dict[str, Any]:
        """清理认证文件"""
        result = {'success': True, 'errors': []}

        try:
            for auth_file in auth_files:
                if os.path.exists(auth_file):
                    try:
                        if os.path.isfile(auth_file):
                            os.remove(auth_file)
                        else:
                            shutil.rmtree(auth_file)
                    except Exception as e:
                        result['errors'].append(f"清理认证文件失败 {auth_file}: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"清理认证文件失败: {str(e)}")

        return result

    def _reset_machine_fingerprint(self) -> Dict[str, Any]:
        """重置机器指纹"""
        result = {'success': True, 'errors': []}

        try:
            # 1. 重置机器ID
            machine_id_path = os.path.expandvars(r"%APPDATA%\Code\machineid")
            if os.path.exists(machine_id_path):
                try:
                    new_machine_id = secrets.token_hex(32)
                    with open(machine_id_path, 'w', encoding='utf-8') as f:
                        f.write(new_machine_id)
                except Exception as e:
                    result['errors'].append(f"重置机器ID失败: {str(e)}")

            # 2. 重置系统UUID
            try:
                new_uuid = str(uuid.uuid4()).upper()
                # 更新注册表中的机器GUID
                try:
                    key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                       r"SOFTWARE\Microsoft\Cryptography",
                                       0, winreg.KEY_SET_VALUE)
                    winreg.SetValueEx(key, "MachineGuid", 0, winreg.REG_SZ, new_uuid)
                    winreg.CloseKey(key)
                except Exception as e:
                    result['errors'].append(f"更新机器GUID失败: {str(e)}")
            except Exception as e:
                result['errors'].append(f"重置系统UUID失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置机器指纹失败: {str(e)}")

        return result

    def _modify_telemetry_data(self) -> Dict[str, Any]:
        """修改遥测数据"""
        result = {'success': True, 'errors': []}

        try:
            # 修改VSCode遥测设置
            settings_json_path = os.path.expandvars(r"%APPDATA%\Code\User\settings.json")

            if os.path.exists(settings_json_path):
                try:
                    with open(settings_json_path, 'r', encoding='utf-8') as f:
                        settings = json.load(f)

                    # 禁用遥测
                    telemetry_settings = {
                        "telemetry.telemetryLevel": "off",
                        "telemetry.enableCrashReporter": False,
                        "telemetry.enableTelemetry": False
                    }

                    settings.update(telemetry_settings)

                    with open(settings_json_path, 'w', encoding='utf-8') as f:
                        json.dump(settings, f, indent=2, ensure_ascii=False)

                except Exception as e:
                    result['errors'].append(f"修改遥测设置失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"修改遥测数据失败: {str(e)}")

        return result

    def _regenerate_device_ids(self) -> Dict[str, Any]:
        """重新生成设备ID"""
        result = {'success': True, 'errors': []}

        try:
            # 1. 重新生成VSCode设备ID
            vscode_storage_path = os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\storage.json")
            if os.path.exists(vscode_storage_path):
                try:
                    with open(vscode_storage_path, 'r', encoding='utf-8') as f:
                        storage_data = json.load(f)

                    # 生成新的设备ID
                    new_machine_id = secrets.token_hex(32)
                    new_dev_device_id = str(uuid.uuid4())
                    new_sqm_id = str(uuid.uuid4()).upper()

                    # 更新设备相关字段
                    device_keys = {
                        'telemetry.machineId': new_machine_id,
                        'telemetry.devDeviceId': new_dev_device_id,
                        'telemetry.sqmId': f"{{{new_sqm_id}}}",
                        'telemetry.macMachineId': secrets.token_hex(32)
                    }

                    storage_data.update(device_keys)

                    with open(vscode_storage_path, 'w', encoding='utf-8') as f:
                        json.dump(storage_data, f, indent=2, ensure_ascii=False)

                except Exception as e:
                    result['errors'].append(f"重新生成VSCode设备ID失败: {str(e)}")

            # 2. 重新生成Cursor设备ID
            cursor_storage_path = os.path.expandvars(r"%APPDATA%\Cursor\User\globalStorage\storage.json")
            if os.path.exists(cursor_storage_path):
                try:
                    with open(cursor_storage_path, 'r', encoding='utf-8') as f:
                        storage_data = json.load(f)

                    # 生成新的设备ID
                    new_machine_id = secrets.token_hex(32)
                    new_dev_device_id = str(uuid.uuid4())
                    new_sqm_id = str(uuid.uuid4()).upper()

                    # 更新设备相关字段
                    device_keys = {
                        'telemetry.machineId': new_machine_id,
                        'telemetry.devDeviceId': new_dev_device_id,
                        'telemetry.sqmId': f"{{{new_sqm_id}}}",
                        'telemetry.macMachineId': secrets.token_hex(32)
                    }

                    storage_data.update(device_keys)

                    with open(cursor_storage_path, 'w', encoding='utf-8') as f:
                        json.dump(storage_data, f, indent=2, ensure_ascii=False)

                except Exception as e:
                    result['errors'].append(f"重新生成Cursor设备ID失败: {str(e)}")

            # 3. 重新生成机器ID文件
            machine_id_files = [
                os.path.expandvars(r"%APPDATA%\Code\machineid"),
                os.path.expandvars(r"%APPDATA%\Cursor\machine-id")
            ]

            for machine_id_file in machine_id_files:
                if os.path.exists(machine_id_file):
                    try:
                        new_machine_id = secrets.token_hex(32)
                        with open(machine_id_file, 'w', encoding='utf-8') as f:
                            f.write(new_machine_id)
                    except Exception as e:
                        result['errors'].append(f"重新生成机器ID文件失败 {machine_id_file}: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重新生成设备ID失败: {str(e)}")

        return result

    def _reset_network_adapters(self) -> Dict[str, Any]:
        """重置网络适配器"""
        result = {'success': True, 'errors': []}

        try:
            import subprocess

            # 1. 重置Winsock
            try:
                subprocess.run(['netsh', 'winsock', 'reset'], check=True, capture_output=True)
            except Exception as e:
                result['errors'].append(f"重置Winsock失败: {str(e)}")

            # 2. 重置TCP/IP栈
            try:
                subprocess.run(['netsh', 'int', 'ip', 'reset'], check=True, capture_output=True)
            except Exception as e:
                result['errors'].append(f"重置TCP/IP栈失败: {str(e)}")

            # 3. 清理DNS缓存
            try:
                subprocess.run(['ipconfig', '/flushdns'], check=True, capture_output=True)
            except Exception as e:
                result['errors'].append(f"清理DNS缓存失败: {str(e)}")

            # 4. 清理ARP缓存
            try:
                subprocess.run(['arp', '-d', '*'], check=True, capture_output=True)
            except Exception as e:
                result['errors'].append(f"清理ARP缓存失败: {str(e)}")

            # 5. 重置网络配置
            try:
                subprocess.run(['netsh', 'interface', 'ipv4', 'reset'], check=True, capture_output=True)
                subprocess.run(['netsh', 'interface', 'ipv6', 'reset'], check=True, capture_output=True)
            except Exception as e:
                result['errors'].append(f"重置网络配置失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置网络适配器失败: {str(e)}")

        return result

    def _modify_hardware_profile(self) -> Dict[str, Any]:
        """修改硬件配置文件"""
        result = {'success': True, 'errors': []}

        try:
            # 1. 修改硬件配置文件GUID
            try:
                new_hw_profile_guid = str(uuid.uuid4()).upper()
                hw_key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                      r"SYSTEM\CurrentControlSet\Control\IDConfigDB\Hardware Profiles\0001",
                                      0, winreg.KEY_SET_VALUE)
                winreg.SetValueEx(hw_key, "HwProfileGuid", 0, winreg.REG_SZ, f"{{{new_hw_profile_guid}}}")
                winreg.CloseKey(hw_key)
            except Exception as e:
                result['errors'].append(f"修改硬件配置文件GUID失败: {str(e)}")

            # 2. 修改系统UUID
            try:
                new_system_uuid = str(uuid.uuid4()).upper()
                crypto_key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                          r"SOFTWARE\Microsoft\Cryptography",
                                          0, winreg.KEY_SET_VALUE)
                winreg.SetValueEx(crypto_key, "MachineGuid", 0, winreg.REG_SZ, new_system_uuid)
                winreg.CloseKey(crypto_key)
            except Exception as e:
                result['errors'].append(f"修改系统UUID失败: {str(e)}")

            # 3. 修改处理器标识符
            try:
                processor_key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                             r"HARDWARE\DESCRIPTION\System\CentralProcessor\0",
                                             0, winreg.KEY_SET_VALUE)
                # 生成新的处理器标识符
                new_processor_id = secrets.token_hex(8).upper()
                winreg.SetValueEx(processor_key, "ProcessorNameString", 0, winreg.REG_SZ,
                                f"Intel(R) Core(TM) i7-{new_processor_id} CPU @ 2.60GHz")
                winreg.CloseKey(processor_key)
            except Exception as e:
                result['errors'].append(f"修改处理器标识符失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"修改硬件配置文件失败: {str(e)}")

        return result

    def _apply_anti_detection_measures(self) -> Dict[str, Any]:
        """应用反检测措施"""
        result = {
            'techniques': [],
            'errors': []
        }
        
        try:
            # 1. 虚拟机检测绕过
            if self.advanced_techniques['virtual_machine_detection_bypass']:
                vm_result = self._bypass_vm_detection()
                if vm_result['success']:
                    result['techniques'].append("虚拟机检测绕过")
            
            # 2. 浏览器指纹重置
            if self.advanced_techniques['browser_fingerprint_reset']:
                browser_result = self._reset_browser_fingerprints()
                if browser_result['success']:
                    result['techniques'].append("浏览器指纹重置")
            
            # 3. 系统时钟操作
            if self.advanced_techniques['system_clock_manipulation']:
                clock_result = self._manipulate_system_clock()
                if clock_result['success']:
                    result['techniques'].append("系统时钟操作")
            
        except Exception as e:
            result['errors'].append(f"应用反检测措施失败: {str(e)}")
        
        return result
