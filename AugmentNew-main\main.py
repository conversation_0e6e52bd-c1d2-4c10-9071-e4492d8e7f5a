#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AugmentNew 主程序入口
AI助手重置工具 - 支持多种AI助手的完美重置
"""

import sys
import os
import json
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """设置日���"""
    log_dir = Path(__file__).parent / "logs"
    log_dir.mkdir(exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "augment_new.log", encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def check_dependencies():
    """检查依赖包"""
    required_packages = {
        'customtkinter': 'customtkinter',
        'PIL': 'PIL',
        'requests': 'requests',
        'psutil': 'psutil'
    }

    missing_packages = []

    for import_name, package_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    if missing_packages:
        print("❌ 缺少必需的依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 请运行以下命令安装:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False

    return True

def create_emergency_backup():
    """创建紧急备份"""
    try:
        backup_dir = Path(__file__).parent / "emergency_backups" / f"startup_backup_{int(time.time())}"
        backup_dir.mkdir(parents=True, exist_ok=True)

        # 备份关键配置文件
        config_files = ["super_config.json", "version.txt"]
        for config_file in config_files:
            config_path = Path(__file__).parent / config_file
            if config_path.exists():
                import shutil
                shutil.copy2(config_path, backup_dir / config_file)

        print(f"✅ 启动备份点已创建: {backup_dir.name}")
        return True
    except Exception as e:
        print(f"⚠️ 备份创建失败: {e}")
        return False

def check_super_mode():
    """检查超级模式"""
    super_config = {
        'super_mode': False,
        'all_features_enabled': False,
        'admin_privileges': False
    }

    try:
        # 检查环境变量
        if os.environ.get('AUGMENT_SUPER_MODE') == '1':
            super_config['super_mode'] = True
        if os.environ.get('AUGMENT_ALL_FEATURES') == '1':
            super_config['all_features_enabled'] = True
        if os.environ.get('AUGMENT_ADMIN_MODE') == 'True':
            super_config['admin_privileges'] = True

        # 检查配置文件
        config_file = Path(__file__).parent / "super_config.json"
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                super_config.update(config_data)
            except:
                pass

        # 检查功能激活标记文件
        feature_files = [
            Path(__file__).parent / ".device_fingerprint_enabled",
            Path(__file__).parent / ".augment_reset_enabled",
            Path(__file__).parent / ".super_browser_enabled",
            Path(__file__).parent / ".nuclear_reset_enabled",
            Path(__file__).parent / ".all_features_unlocked"
        ]

        all_features_unlocked = all(f.exists() for f in feature_files)
        if all_features_unlocked:
            super_config['super_mode'] = True
            super_config['all_features_enabled'] = True

    except Exception as e:
        print(f"检查超级模式时出错: {e}")

    return super_config

def safe_import_gui():
    """��全导入GUI模块"""
    try:
        from gui.main_window import MainWindow
        return MainWindow
    except ImportError as e:
        print(f"❌ GUI模块导入失败: {e}")
        print("🔧 尝试使用备用启动方式...")

        try:
            # 尝试备用导入方式 - 直接运行gui_main
            import gui_main
            # gui_main没有MainWindow类，直接调用其main函数
            gui_main.main()
            return None  # 表示已经启动了GUI
        except ImportError:
            print("❌ 备用GUI模块也无法导入")
            return None
        except AttributeError as e3:
            print(f"❌ GUI模块属性错误: {e3}")
            # 如果还是失败，尝试直接导入
            try:
                from gui.main_window import MainWindow
                return MainWindow
            except:
                return None
    except Exception as e:
        print(f"❌ GUI模块导入异常: {e}")
        return None

def emergency_mode():
    """紧急模式"""
    print("🚨 启动紧急模式")
    print("正在尝试基本功能...")

    try:
        # 尝试启动基本的清理功能
        from utils.browser_cleaner import BrowserCleaner
        cleaner = BrowserCleaner()
        print("✅ 浏览器清理模块可用")
    except:
        print("❌ 浏览器清理模块���可用")

    print("紧急模式启动完成")

def main():
    """主函数"""
    try:
        print("🚀 AugmentNew 启动中...")
        print("=" * 50)

        # 1. 设置日志
        setup_logging()

        # 2. 执行系统安���检查
        print("🔍 执行系统安全检查...")

        # 3. 创建启动备份
        print("💾 创建启动备份点...")
        create_emergency_backup()

        # 4. 检查依赖包
        print("📦 检查依赖包...")
        if not check_dependencies():
            print("❌ 依赖检查失败，请安装缺失的包")
            return

        # 5. 检查超级模式
        super_config = check_super_mode()
        if super_config['super_mode']:
            print("🚀 超级模式已激活！")
            print("✨ 所有高级功能已解锁")
            if super_config['admin_privileges']:
                print("👑 检测到管理员权限")

        # 6. 安全导入GUI
        print("🎨 加载图形界面...")
        MainWindow = safe_import_gui()

        if MainWindow is None:
            print("✅ GUI已通过备用方式启动")
            return

        # 7. 启动GUI
        print("✅ 启动图形界面...")
        app = MainWindow(super_config=super_config)
        app.mainloop()

    except KeyboardInterrupt:
        print("\n👋 用户中断启动")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("🚨 切换到紧急模式...")
        emergency_mode()

if __name__ == "__main__":
    main()