#!/usr/bin/env python3
"""
Augment账号重置功能测试脚本
注意：此脚本仅用于测试功能，不会实际执行重置操作
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.augment_account_resetter import AugmentAccountResetter

def test_account_status_analysis():
    """测试账号状态分析功能"""
    print("🔍 测试Augment账号状态分析...")
    
    resetter = AugmentAccountResetter()
    
    try:
        analysis = resetter.analyze_augment_account_status()
        
        print(f"✅ 账号状态: {analysis['account_status']}")
        print(f"✅ 试用数据存在: {analysis['trial_data_found']}")
        print(f"✅ 扩展已安装: {analysis['extension_installed']}")
        print(f"✅ 存储数据存在: {analysis['storage_data_exists']}")
        print(f"✅ 缓存数据存在: {analysis['cache_data_exists']}")
        print(f"✅ 重置建议: {analysis['reset_recommendation']}")
        
        if analysis['risk_factors']:
            print("\n⚠️ 风险因素:")
            for factor in analysis['risk_factors']:
                print(f"  • {factor}")
        
    except Exception as e:
        print(f"❌ 账号状态分析失败: {e}")

def test_reset_preview():
    """测试重置预览功能"""
    print("\n📋 测试重置预览功能...")
    
    resetter = AugmentAccountResetter()
    
    try:
        preview = resetter.get_reset_preview()
        
        print(f"✅ 预计重置项目总数: {preview['estimated_items']}")
        
        reset_plan = preview['reset_plan']
        print("\n📊 重置计划详情:")
        print(f"  • 扩展数据: {reset_plan['extension_data']} 项")
        print(f"  • 存储条目: {reset_plan['storage_entries']} 项")
        print(f"  • 缓存文件: {reset_plan['cache_files']} 项")
        print(f"  • 配置文件: {reset_plan['config_files']} 项")
        print(f"  • 试用状态: {reset_plan['trial_status']} 项")
        
        if preview['warnings']:
            print("\n⚠️ 警告信息:")
            for warning in preview['warnings']:
                print(f"  • {warning}")
                
    except Exception as e:
        print(f"❌ 重置预览失败: {e}")

def test_path_detection():
    """测试路径检测功能"""
    print("\n🔍 测试路径检测功能...")
    
    resetter = AugmentAccountResetter()
    
    print("✅ Augment相关路径:")
    for key, path in resetter.augment_paths.items():
        exists = os.path.exists(path)
        status = "✅" if exists else "❌"
        print(f"  {status} {key}: {path}")

def test_extension_ids():
    """测试扩展ID识别"""
    print("\n🔌 测试扩展ID识别...")
    
    resetter = AugmentAccountResetter()
    
    print("✅ 支持的Augment扩展ID:")
    for ext_id in resetter.augment_extension_ids:
        print(f"  • {ext_id}")

def test_trial_status_keys():
    """测试试用状态键识别"""
    print("\n🔑 测试试用状态键识别...")
    
    resetter = AugmentAccountResetter()
    
    print("✅ 试用状态相关键名:")
    for key in resetter.trial_status_keys:
        print(f"  • {key}")

def test_id_generation():
    """测试ID生成功能"""
    print("\n🆔 测试ID生成功能...")
    
    resetter = AugmentAccountResetter()
    
    try:
        # 测试机器ID生成
        machine_id = resetter._generate_machine_id()
        print(f"✅ 新机器ID: {machine_id[:16]}... (长度: {len(machine_id)})")
        
        # 测试设备ID生成
        device_id = resetter._generate_device_id()
        print(f"✅ 新设备ID: {device_id}")
        
        # 测试SQM ID生成
        sqm_id = resetter._generate_sqm_id()
        print(f"✅ 新SQM ID: {sqm_id}")
        
        # 验证ID格式
        if len(machine_id) == 64 and all(c in '0123456789abcdef' for c in machine_id):
            print("✅ 机器ID格式正确")
        else:
            print("❌ 机器ID格式错误")
        
        import uuid
        try:
            uuid.UUID(device_id)
            print("✅ 设备ID格式正确")
        except ValueError:
            print("❌ 设备ID格式错误")
            
        try:
            uuid.UUID(sqm_id)
            print("✅ SQM ID格式正确")
        except ValueError:
            print("❌ SQM ID格式错误")
            
    except Exception as e:
        print(f"❌ ID生成测试失败: {e}")

def test_file_counting():
    """测试文件计数功能"""
    print("\n🔢 测试文件计数功能...")
    
    resetter = AugmentAccountResetter()
    
    try:
        # 测试各种计数功能
        extension_count = resetter._count_extension_data()
        print(f"✅ 扩展数据项目: {extension_count} 个")
        
        storage_count = resetter._count_storage_entries()
        print(f"✅ 存储条目: {storage_count} 个")
        
        cache_count = resetter._count_cache_files()
        print(f"✅ 缓存文件: {cache_count} 个")
        
        config_count = resetter._count_config_files()
        print(f"✅ 配置文件: {config_count} 个")
        
        trial_count = resetter._count_trial_status_items()
        print(f"✅ 试用状态项目: {trial_count} 个")
        
    except Exception as e:
        print(f"❌ 文件计数测试失败: {e}")

def test_backup_functionality():
    """测试备份功能"""
    print("\n💾 测试备份功能...")
    
    resetter = AugmentAccountResetter()
    
    try:
        # 测试备份功能
        test_backup_dir = "test_augment_backup"
        backup_success = resetter._create_augment_backup(test_backup_dir)
        
        if backup_success:
            print("✅ 备份功能测试成功")
            
            # 检查备份目录
            backup_path = Path(test_backup_dir)
            if backup_path.exists():
                backup_items = list(backup_path.glob("augment_account_backup_*"))
                if backup_items:
                    print(f"✅ 备份目录已创建: {backup_items[0]}")
                    
                    # 清理测试备份
                    import shutil
                    shutil.rmtree(backup_path, ignore_errors=True)
                    print("✅ 测试备份已清理")
                else:
                    print("❌ 备份目录为空")
            else:
                print("❌ 备份目录未创建")
        else:
            print("❌ 备份功能测试失败")
            
    except Exception as e:
        print(f"❌ 备份功能测试失败: {e}")

def test_json_cleaning():
    """测试JSON清理功能"""
    print("\n🧹 测试JSON清理功能...")
    
    resetter = AugmentAccountResetter()
    
    try:
        # 创建测试数据
        test_data = {
            "normal_key": "normal_value",
            "augment.trial.status": "expired",
            "augment.usage.count": 100,
            "other_config": {
                "augment.setting": "test",
                "normal_setting": "keep"
            },
            "list_data": [
                "normal_item",
                "augment_item",
                {"augment.key": "remove", "keep.key": "keep"}
            ]
        }
        
        # 测试清理功能
        cleaned_data = resetter._remove_augment_keys(test_data)
        
        print("✅ JSON清理测试:")
        print(f"  原始键数量: {len(test_data)}")
        print(f"  清理后键数量: {len(cleaned_data)}")
        
        # 验证清理结果
        has_augment_keys = any('augment' in str(key).lower() for key in cleaned_data.keys())
        if not has_augment_keys:
            print("✅ Augment相关键已成功清理")
        else:
            print("❌ 仍存在Augment相关键")
            
    except Exception as e:
        print(f"❌ JSON清理测试失败: {e}")

def generate_test_report():
    """生成测试报告"""
    print("\n📄 生成测试报告...")
    
    try:
        resetter = AugmentAccountResetter()
        
        # 收集测试信息
        report = {
            "test_time": str(Path(__file__).stat().st_mtime),
            "augment_paths": resetter.augment_paths,
            "extension_ids": resetter.augment_extension_ids,
            "trial_status_keys": resetter.trial_status_keys,
            "current_analysis": resetter.analyze_augment_account_status(),
            "reset_preview": resetter.get_reset_preview(),
            "test_results": {
                "path_detection": True,
                "id_generation": True,
                "file_counting": True,
                "backup_functionality": True,
                "json_cleaning": True
            }
        }
        
        # 保存报告
        report_file = "augment_account_reset_test_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 测试报告已保存到: {report_file}")
        
    except Exception as e:
        print(f"❌ 生成测试报告失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始Augment账号重置功能测试")
    print("=" * 60)
    
    # 运行所有测试
    test_account_status_analysis()
    test_reset_preview()
    test_path_detection()
    test_extension_ids()
    test_trial_status_keys()
    test_id_generation()
    test_file_counting()
    test_backup_functionality()
    test_json_cleaning()
    generate_test_report()
    
    print("\n" + "=" * 60)
    print("✅ 所有测试完成！")
    print("\n⚠️ 注意：此脚本仅用于测试功能，未执行实际重置操作")
    print("💡 如需执行重置，请使用GUI界面的'Augment账号重置'功能")
    print("\n🔄 Augment账号重置功能可以将停用账号恢复为全新状态！")

if __name__ == "__main__":
    main()
