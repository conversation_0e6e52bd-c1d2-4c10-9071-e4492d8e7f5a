' AugmentNew 免费版启动脚本
' 完全免费，无需激活码！
' 作者: alltobebetter
' 许可: MIT License

Option Explicit

Dim objShell, objFSO, scriptPath, pythonPath, mainScript
Dim currentDir, logFile, errorMsg

' 创建对象
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' 获取脚本所在目录
scriptPath = objFSO.GetParentFolderName(WScript.ScriptFullName)
currentDir = scriptPath

' 主程序文件路径
mainScript = objFSO.BuildPath(currentDir, "gui_main.py")

' 日志文件路径
logFile = objFSO.BuildPath(currentDir, "启动日志.txt")

' 显示启动信息
MsgBox "AugmentNew 免费版启动中..." & vbCrLf & vbCrLf & _
       "✅ 完全免费，无需激活码" & vbCrLf & _
       "✅ 开源透明，拒绝收费" & vbCrLf & _
       "✅ 安全可靠，自动备份" & vbCrLf & vbCrLf & _
       "点击确定开始启动程序", vbInformation, "AugmentNew 免费版"

' 检查主程序文件是否存在
If Not objFSO.FileExists(mainScript) Then
    MsgBox "错误：找不到主程序文件！" & vbCrLf & vbCrLf & _
           "请确保 gui_main.py 文件存在于：" & vbCrLf & _
           mainScript, vbCritical, "AugmentNew 启动失败"
    WScript.Quit 1
End If

' 尝试查找Python解释器
pythonPath = FindPython()

If pythonPath = "" Then
    MsgBox "错误：未找到Python解释器！" & vbCrLf & vbCrLf & _
           "请确保已安装Python 3.10或更高版本" & vbCrLf & _
           "下载地址：https://www.python.org/downloads/", vbCritical, "Python未安装"
    WScript.Quit 1
End If

' 写入启动日志
WriteLog "=== AugmentNew 免费版启动日志 ==="
WriteLog "启动时间: " & Now()
WriteLog "Python路径: " & pythonPath
WriteLog "主程序路径: " & mainScript
WriteLog "工作目录: " & currentDir

' 启动程序
On Error Resume Next
objShell.CurrentDirectory = currentDir
objShell.Run """" & pythonPath & """ """ & mainScript & """", 1, False

If Err.Number <> 0 Then
    errorMsg = "启动失败！" & vbCrLf & vbCrLf & _
               "错误代码: " & Err.Number & vbCrLf & _
               "错误描述: " & Err.Description & vbCrLf & vbCrLf & _
               "请检查Python环境是否正确安装"
    WriteLog "启动失败: " & Err.Description
    MsgBox errorMsg, vbCritical, "AugmentNew 启动失败"
    WScript.Quit 1
Else
    WriteLog "程序启动成功"
    MsgBox "AugmentNew 免费版启动成功！" & vbCrLf & vbCrLf & _
           "程序正在后台运行，请稍等片刻..." & vbCrLf & _
           "如有问题请查看启动日志.txt", vbInformation, "启动成功"
End If

On Error GoTo 0

' 清理对象
Set objShell = Nothing
Set objFSO = Nothing

WScript.Quit 0

' ===== 函数定义 =====

Function FindPython()
    Dim pythonPaths, i, testPath
    
    ' 常见的Python安装路径
    pythonPaths = Array( _
        "python", _
        "python3", _
        "py", _
        "C:\Python310\python.exe", _
        "C:\Python311\python.exe", _
        "C:\Python312\python.exe", _
        "C:\Python313\python.exe", _
        "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe", _
        "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe", _
        "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe", _
        "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" _
    )
    
    FindPython = ""
    
    For i = 0 To UBound(pythonPaths)
        testPath = pythonPaths(i)
        
        ' 测试Python是否可用
        On Error Resume Next
        objShell.Run testPath & " --version", 0, True
        If Err.Number = 0 Then
            FindPython = testPath
            Exit For
        End If
        On Error GoTo 0
    Next
End Function

Sub WriteLog(message)
    Dim logFileHandle
    On Error Resume Next
    Set logFileHandle = objFSO.OpenTextFile(logFile, 8, True)
    If Err.Number = 0 Then
        logFileHandle.WriteLine message
        logFileHandle.Close
    End If
    On Error GoTo 0
    Set logFileHandle = Nothing
End Sub
