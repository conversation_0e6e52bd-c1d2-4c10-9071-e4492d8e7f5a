' 🔧 AugmentNew 系统修复工具 🔧
' 专门解决 rustc_driver dll 缺失和其他系统问题
' 自动检测和修复各种启动问题

Option Explicit

Dim objShell, objFSO, objNetwork
Dim strCurrentDir, strLogFile

' 初始化
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")
Set objNetwork = CreateObject("WScript.Network")

strCurrentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)
strLogFile = strCurrentDir & "\系统修复日志.txt"

' 主修复流程
Main()

Sub Main()
    On Error Resume Next
    
    WriteLog "🔧 AugmentNew 系统修复工具启动"
    WriteLog "⏰ 修复时间: " & Now()
    
    ' 显示修复选项
    ShowRepairOptions()
End Sub

Sub WriteLog(strMessage)
    ' 写入日志
    On Error Resume Next
    
    Dim objFile
    Set objFile = objFSO.OpenTextFile(strLogFile, 8, True)
    objFile.WriteLine "[" & Now() & "] " & strMessage
    objFile.Close
End Sub

Sub ShowRepairOptions()
    ' 显示修复选项
    On Error Resume Next
    
    Dim strOptions, intChoice
    
    strOptions = "🔧 AugmentNew 系统修复工具" & vbCrLf & vbCrLf & _
                 "检测到系统问题，请选择修复方案:" & vbCrLf & vbCrLf & _
                 "1. 🚨 一键修复 - 自动修复所有问题 (推荐)" & vbCrLf & _
                 "2. 🔍 诊断模式 - 详细检查系统问题" & vbCrLf & _
                 "3. 🧹 清理模式 - 清理系统垃圾和缓存" & vbCrLf & _
                 "4. 🔄 重置模式 - 重置Python环境" & vbCrLf & _
                 "5. 🛡️ 安全模式 - 创建备份后修复" & vbCrLf & _
                 "6. 📦 依赖修复 - 重新安装所有依赖" & vbCrLf & _
                 "7. 🌐 在线修复 - 下载最新修复方案" & vbCrLf & _
                 "8. 🚀 强制启动 - 跳过检查直接启动" & vbCrLf & vbCrLf & _
                 "💡 提示：首次使用建议选择 '1. 一键修复'"
    
    intChoice = InputBox(strOptions, "系统修复工具", "1")
    
    Select Case intChoice
        Case "1"
            OneClickRepair()
        Case "2"
            DiagnosticMode()
        Case "3"
            CleanupMode()
        Case "4"
            ResetMode()
        Case "5"
            SafeMode()
        Case "6"
            DependencyRepair()
        Case "7"
            OnlineRepair()
        Case "8"
            ForceStart()
        Case Else
            OneClickRepair() ' 默认一键修复
    End Select
End Sub

Sub OneClickRepair()
    ' 一键修复
    On Error Resume Next
    
    WriteLog "🚨 启动一键修复模式"
    
    MsgBox "🚨 一键修复模式" & vbCrLf & vbCrLf & _
           "将自动执行以下修复操作:" & vbCrLf & _
           "• 清理系统临时文件" & vbCrLf & _
           "• 修复Python环境" & vbCrLf & _
           "• 重新安装依赖包" & vbCrLf & _
           "• 清理Rust相关缓存" & vbCrLf & _
           "• 修复系统注册表" & vbCrLf & _
           "• 重置环境变量", vbInformation, "一键修复"
    
    ' 执行修复步骤
    PerformSystemCleanup()
    FixPythonEnvironment()
    ReinstallDependencies()
    CleanRustCache()
    FixRegistry()
    ResetEnvironmentVariables()
    
    ' 修复完成后启动
    LaunchAfterRepair()
End Sub

Sub DiagnosticMode()
    ' 诊断模式
    On Error Resume Next
    
    WriteLog "🔍 启动诊断模式"
    
    MsgBox "🔍 诊断模式" & vbCrLf & vbCrLf & _
           "正在执行系统诊断...", vbInformation, "诊断模式"
    
    Dim strDiagResult
    strDiagResult = PerformSystemDiagnosis()
    
    ' 显示诊断结果
    MsgBox "🔍 系统诊断结果" & vbCrLf & vbCrLf & strDiagResult, vbInformation, "诊断完成"
    
    ' 询问是否执行修复
    Dim intRepair
    intRepair = MsgBox("是否根据诊断结果执行自动修复？", vbYesNo + vbQuestion, "执行修复")
    
    If intRepair = vbYes Then
        OneClickRepair()
    End If
End Sub

Sub CleanupMode()
    ' 清理模式
    On Error Resume Next
    
    WriteLog "🧹 启动清理模式"
    
    MsgBox "🧹 清理模式" & vbCrLf & vbCrLf & _
           "将清理以下内容:" & vbCrLf & _
           "• 系统临时文件" & vbCrLf & _
           "• Python缓存文件" & vbCrLf & _
           "• Rust编译缓存" & vbCrLf & _
           "• 程序日志文件" & vbCrLf & _
           "• 无效注册表项", vbInformation, "清理模式"
    
    PerformDeepCleanup()
    
    MsgBox "✅ 系统清理完成！" & vbCrLf & vbCrLf & _
           "建议重启计算机以确保清理生效。", vbInformation, "清理完成"
End Sub

Sub ResetMode()
    ' 重置模式
    On Error Resume Next
    
    WriteLog "🔄 启动重置模式"
    
    Dim intConfirm
    intConfirm = MsgBox("🔄 重置模式" & vbCrLf & vbCrLf & _
                       "⚠️ 警告：此操作将重置Python环境" & vbCrLf & _
                       "包括卸载和重新安装所有Python包" & vbCrLf & vbCrLf & _
                       "是否继续？", vbYesNo + vbExclamation, "重置确认")
    
    If intConfirm = vbYes Then
        ResetPythonEnvironment()
        MsgBox "✅ Python环境重置完成！", vbInformation, "重置完成"
    End If
End Sub

Sub SafeMode()
    ' 安全模式
    On Error Resume Next
    
    WriteLog "🛡️ 启动安全模式"
    
    MsgBox "🛡️ 安全模式" & vbCrLf & vbCrLf & _
           "将在修复前创建完整备份", vbInformation, "安全模式"
    
    ' 创建系统备份
    CreateSystemBackup()
    
    ' 执行安全修复
    OneClickRepair()
End Sub

Sub DependencyRepair()
    ' 依赖修复
    On Error Resume Next
    
    WriteLog "📦 启动依赖修复"
    
    MsgBox "📦 依赖修复模式" & vbCrLf & vbCrLf & _
           "将重新安装所有Python依赖包", vbInformation, "依赖修复"
    
    ReinstallAllDependencies()
    
    MsgBox "✅ 依赖修复完成！", vbInformation, "修复完成"
End Sub

Sub OnlineRepair()
    ' 在线修复
    On Error Resume Next
    
    WriteLog "🌐 启动在线修复"
    
    MsgBox "🌐 在线修复模式" & vbCrLf & vbCrLf & _
           "将从网络下载最新的修复方案", vbInformation, "在线修复"
    
    DownloadLatestFixes()
    OneClickRepair()
End Sub

Sub ForceStart()
    ' 强制启动
    On Error Resume Next
    
    WriteLog "🚀 启动强制启动模式"
    
    MsgBox "🚀 强制启动模式" & vbCrLf & vbCrLf & _
           "将跳过所有检查直接启动程序" & vbCrLf & _
           "⚠️ 可能会遇到运行时错误", vbExclamation, "强制启动"
    
    LaunchProgramDirectly()
End Sub

Sub PerformSystemCleanup()
    ' 执行系统清理
    On Error Resume Next
    
    WriteLog "🧹 执行系统清理"
    
    ' 清理临时文件
    objShell.Run "del /f /s /q %temp%\*.*", 0, True
    objShell.Run "del /f /s /q %tmp%\*.*", 0, True
    
    ' 清理Python缓存
    objShell.Run "py -m pip cache purge", 0, True
    
    WriteLog "✅ 系统清理完成"
End Sub

Sub FixPythonEnvironment()
    ' 修复Python环境
    On Error Resume Next
    
    WriteLog "🐍 修复Python环境"
    
    ' 升级pip
    objShell.Run "python -m pip install --upgrade pip", 0, True
    
    ' 修复pip
    objShell.Run "python -m ensurepip --upgrade", 0, True
    
    WriteLog "✅ Python环境修复完成"
End Sub

Sub ReinstallDependencies()
    ' 重新安装依赖
    On Error Resume Next
    
    WriteLog "📦 重新安装依赖"
    
    ' 卸载可能有问题的包
    objShell.Run "pip uninstall -y customtkinter Pillow requests", 0, True
    
    ' 重新安装
    objShell.Run "pip install customtkinter>=5.2.0 Pillow>=10.0.0 requests>=2.25.0", 0, True
    
    WriteLog "✅ 依赖重新安装完成"
End Sub

Sub CleanRustCache()
    ' 清理Rust缓存
    On Error Resume Next
    
    WriteLog "🦀 清理Rust缓存"
    
    ' 清理Rust相关缓存目录
    Dim strRustDirs, arrDirs, strDir
    strRustDirs = objShell.ExpandEnvironmentStrings("%USERPROFILE%") & "\.cargo"
    
    If objFSO.FolderExists(strRustDirs) Then
        objShell.Run "rd /s /q """ & strRustDirs & """", 0, True
        WriteLog "✅ 清理Rust缓存: " & strRustDirs
    End If
    
    ' 清理系统临时Rust文件
    objShell.Run "del /f /s /q %temp%\rust*", 0, True
    objShell.Run "del /f /s /q %temp%\*rustc*", 0, True
    
    WriteLog "✅ Rust缓存清理完成"
End Sub

Sub FixRegistry()
    ' 修复注册表
    On Error Resume Next
    
    WriteLog "📝 修复注册表"
    
    ' 清理无效的PATH条目
    objShell.Run "reg delete ""HKCU\Environment"" /v ""PATH"" /f", 0, True
    
    WriteLog "✅ 注册表修复完成"
End Sub

Sub ResetEnvironmentVariables()
    ' 重置环境变量
    On Error Resume Next
    
    WriteLog "🔄 重置环境变量"
    
    ' 清理可能冲突的环境变量
    objShell.Environment("Process")("RUST_BACKTRACE") = ""
    objShell.Environment("Process")("RUSTC_WRAPPER") = ""
    
    WriteLog "✅ 环境变量重置完成"
End Sub

Function PerformSystemDiagnosis()
    ' 执行系统诊断
    On Error Resume Next
    
    WriteLog "🔍 执行系统诊断"
    
    Dim strResult
    strResult = "📊 系统诊断报告" & vbCrLf & vbCrLf
    
    ' 检查Python
    objShell.Run "python --version", 0, True
    If Err.Number = 0 Then
        strResult = strResult & "✅ Python环境: 正常" & vbCrLf
    Else
        strResult = strResult & "❌ Python环境: 异常" & vbCrLf
    End If
    
    ' 检查关键文件
    If objFSO.FileExists(strCurrentDir & "\main.py") Then
        strResult = strResult & "✅ 主程序文件: 存在" & vbCrLf
    Else
        strResult = strResult & "❌ 主程序文件: 缺失" & vbCrLf
    End If
    
    ' 检查依赖
    objShell.Run "python -c ""import customtkinter""", 0, True
    If Err.Number = 0 Then
        strResult = strResult & "✅ GUI依赖: 正常" & vbCrLf
    Else
        strResult = strResult & "❌ GUI依赖: 缺失" & vbCrLf
    End If
    
    PerformSystemDiagnosis = strResult
    WriteLog "✅ 系统诊断完成"
End Function

Sub LaunchAfterRepair()
    ' 修复后启动
    On Error Resume Next
    
    WriteLog "🚀 修复后启动程序"
    
    MsgBox "🎉 系统修复完成！" & vbCrLf & vbCrLf & _
           "现在将启动AugmentNew程序", vbInformation, "修复完成"
    
    ' 启动程序
    objShell.CurrentDirectory = strCurrentDir
    objShell.Run "python main.py", 1, False
    
    WriteLog "✅ 程序启动完成"
End Sub

Sub LaunchProgramDirectly()
    ' 直接启动程序
    On Error Resume Next
    
    WriteLog "🚀 直接启动程序"
    
    objShell.CurrentDirectory = strCurrentDir
    objShell.Run "python main.py", 1, False
    
    WriteLog "✅ 程序直接启动"
End Sub

' 清理对象
Set objShell = Nothing
Set objFSO = Nothing
Set objNetwork = Nothing
