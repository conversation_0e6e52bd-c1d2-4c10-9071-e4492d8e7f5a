"""
测试IDE检测功能
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.augment_account_resetter import AugmentAccount<PERSON><PERSON>tter

def test_ide_detection():
    """测试IDE检测功能"""
    print("🔍 测试IDE检测功能...")
    
    try:
        # 测试自动检测
        resetter = AugmentAccountResetter("auto")
        available_ides = resetter.get_detected_ides()
        
        print(f"检测到的IDE: {available_ides}")
        
        # 测试重置目标信息
        target_info = resetter.get_reset_target_info()
        print(f"重置目标信息: {target_info}")
        
        # 测试不同的IDE类型
        for ide_type in ['vscode', 'cursor', 'auto']:
            print(f"\n--- 测试 {ide_type} 模式 ---")
            try:
                test_resetter = AugmentAccountResetter(ide_type)
                test_info = test_resetter.get_reset_target_info()
                print(f"IDE类型: {test_info['ide_type']}")
                print(f"可用IDE: {test_info['available_ides']}")
                print(f"警告: {test_info['warnings']}")
                print(f"目标路径数量: {len(test_info['target_paths'])}")
            except Exception as e:
                print(f"错误: {e}")
        
        print("\n✅ IDE检测功能测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ide_detection()
