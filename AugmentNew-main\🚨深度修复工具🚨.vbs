' 🚨 AugmentNew 深度修复工具 🚨
' 专门解决 rustc_driver dll 缺失等顽固系统问题
' 彻底清理和重建Python环境

Option Explicit

Dim objShell, objFSO, objWMI
Dim strCurrentDir, strLogFile

' 初始化
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")
strCurrentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)
strLogFile = strCurrentDir & "\深度修复日志.txt"

' 主修复流程
Main()

Sub Main()
    On Error Resume Next
    
    WriteLog "🚨 AugmentNew 深度修复工具启动"
    
    ' 显示警告信息
    Dim intConfirm
    intConfirm = MsgBox("🚨 深度修复工具" & vbCrLf & vbCrLf & _
                       "此工具将执行以下操作:" & vbCrLf & _
                       "• 彻底清理Rust相关组件" & vbCrLf & _
                       "• 重置Python环境" & vbCrLf & _
                       "• 清理系统注册表" & vbCrLf & _
                       "• 重建程序环境" & vbCrLf & vbCrLf & _
                       "⚠️ 此操作可能需要5-10分钟" & vbCrLf & _
                       "是否继续？", vbYesNo + vbExclamation, "深度修复确认")
    
    If intConfirm = vbYes Then
        PerformDeepRepair()
    Else
        MsgBox "用户取消操作", vbInformation
    End If
End Sub

Sub WriteLog(strMessage)
    ' 写入日志
    On Error Resume Next
    
    Dim objFile
    Set objFile = objFSO.OpenTextFile(strLogFile, 8, True)
    objFile.WriteLine "[" & Now() & "] " & strMessage
    objFile.Close
End Sub

Sub PerformDeepRepair()
    ' 执行深度修复
    On Error Resume Next
    
    WriteLog "开始深度修复流程"
    
    ' 显示进度
    MsgBox "🚨 开始深度修复" & vbCrLf & vbCrLf & _
           "正在执行修复操作，请耐心等待..." & vbCrLf & _
           "修复过程中请勿关闭此窗口", vbInformation, "深度修复进行中"
    
    ' 1. 彻底清理Rust组件
    CleanRustComponents()
    
    ' 2. 清理Python环境
    CleanPythonEnvironment()
    
    ' 3. 清理系统注册表
    CleanSystemRegistry()
    
    ' 4. 清理环境变量
    CleanEnvironmentVariables()
    
    ' 5. 重建Python环境
    RebuildPythonEnvironment()
    
    ' 6. 修复程序文件
    FixProgramFiles()
    
    ' 7. 验证修复结果
    VerifyRepair()
    
    WriteLog "深度修复流程完成"
End Sub

Sub CleanRustComponents()
    ' 彻底清理Rust组件
    On Error Resume Next
    
    WriteLog "清理Rust组件"
    
    ' 清理用户目录下的Rust相关文件
    Dim strUserProfile
    strUserProfile = objShell.ExpandEnvironmentStrings("%USERPROFILE%")
    
    ' 清理.cargo目录
    objShell.Run "powershell -Command ""if (Test-Path '$env:USERPROFILE\.cargo') { Remove-Item -Path '$env:USERPROFILE\.cargo' -Recurse -Force }""", 0, True
    
    ' 清理.rustup目录
    objShell.Run "powershell -Command ""if (Test-Path '$env:USERPROFILE\.rustup') { Remove-Item -Path '$env:USERPROFILE\.rustup' -Recurse -Force }""", 0, True
    
    ' 清理临时目录中的Rust文件
    objShell.Run "powershell -Command ""Get-ChildItem -Path '$env:TEMP' -Filter '*rust*' -Recurse | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue""", 0, True
    objShell.Run "powershell -Command ""Get-ChildItem -Path '$env:TMP' -Filter '*rust*' -Recurse | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue""", 0, True
    
    ' 清理系统临时目录
    objShell.Run "powershell -Command ""Get-ChildItem -Path 'C:\Windows\Temp' -Filter '*rust*' -Recurse | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue""", 0, True
    
    WriteLog "Rust组件清理完成"
End Sub

Sub CleanPythonEnvironment()
    ' 清理Python环境
    On Error Resume Next
    
    WriteLog "清理Python环境"
    
    ' 清理pip缓存
    objShell.Run "python -m pip cache purge", 0, True
    objShell.Run "py -m pip cache purge", 0, True
    
    ' 清理Python临时文件
    objShell.Run "powershell -Command ""Get-ChildItem -Path '$env:TEMP' -Filter '*python*' -Recurse | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue""", 0, True
    objShell.Run "powershell -Command ""Get-ChildItem -Path '$env:TEMP' -Filter '*.pyc' -Recurse | Remove-Item -Force -ErrorAction SilentlyContinue""", 0, True
    objShell.Run "powershell -Command ""Get-ChildItem -Path '$env:TEMP' -Filter '__pycache__' -Recurse | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue""", 0, True
    
    ' 清理用户Python缓存
    Dim strAppData
    strAppData = objShell.ExpandEnvironmentStrings("%APPDATA%")
    objShell.Run "powershell -Command ""if (Test-Path '$env:APPDATA\pip') { Remove-Item -Path '$env:APPDATA\pip' -Recurse -Force }""", 0, True
    
    WriteLog "Python环境清理完成"
End Sub

Sub CleanSystemRegistry()
    ' 清理系统注册表
    On Error Resume Next
    
    WriteLog "清理系统注册表"
    
    ' 清理可能的Rust相关注册表项
    objShell.Run "reg delete ""HKCU\Software\Rust"" /f", 0, True
    objShell.Run "reg delete ""HKLM\Software\Rust"" /f", 0, True
    
    ' 清理PATH中的无效条目
    objShell.Run "powershell -Command ""$path = [Environment]::GetEnvironmentVariable('PATH', 'User'); $newPath = ($path -split ';' | Where-Object { $_ -notlike '*rust*' -and $_ -notlike '*cargo*' }) -join ';'; [Environment]::SetEnvironmentVariable('PATH', $newPath, 'User')""", 0, True
    
    WriteLog "系统注册表清理完成"
End Sub

Sub CleanEnvironmentVariables()
    ' 清理环境变量
    On Error Resume Next
    
    WriteLog "清理环境变量"
    
    ' 清理Rust相关环境变量
    objShell.Environment("User")("CARGO_HOME") = ""
    objShell.Environment("User")("RUSTUP_HOME") = ""
    objShell.Environment("User")("RUST_BACKTRACE") = ""
    objShell.Environment("User")("RUSTC_WRAPPER") = ""
    
    ' 清理进程环境变量
    objShell.Environment("Process")("CARGO_HOME") = ""
    objShell.Environment("Process")("RUSTUP_HOME") = ""
    objShell.Environment("Process")("RUST_BACKTRACE") = ""
    objShell.Environment("Process")("RUSTC_WRAPPER") = ""
    
    WriteLog "环境变量清理完成"
End Sub

Sub RebuildPythonEnvironment()
    ' 重建Python环境
    On Error Resume Next
    
    WriteLog "重建Python环境"
    
    ' 检测Python
    Dim strPythonPath
    strPythonPath = DetectPython()
    
    If strPythonPath = "" Then
        WriteLog "错误：未找到Python"
        MsgBox "❌ 未找到Python环境！" & vbCrLf & vbCrLf & _
               "请先安装Python 3.8或更高版本", vbCritical, "Python未安装"
        Exit Sub
    End If
    
    WriteLog "找到Python: " & strPythonPath
    
    ' 升级pip
    objShell.Run strPythonPath & " -m pip install --upgrade pip", 0, True
    
    ' 修复pip
    objShell.Run strPythonPath & " -m ensurepip --upgrade", 0, True
    
    ' 卸载可能有问题的包
    objShell.Run strPythonPath & " -m pip uninstall -y customtkinter Pillow requests cryptography", 0, True
    
    ' 重新安装核心依赖
    objShell.Run strPythonPath & " -m pip install --no-cache-dir customtkinter>=5.2.0", 0, True
    objShell.Run strPythonPath & " -m pip install --no-cache-dir Pillow>=10.0.0", 0, True
    objShell.Run strPythonPath & " -m pip install --no-cache-dir requests>=2.25.0", 0, True
    
    ' 安装其他依赖
    objShell.Run strPythonPath & " -m pip install --no-cache-dir jaraco.text jaraco.functools jaraco.context more-itertools zipp importlib-metadata", 0, True
    
    WriteLog "Python环境重建完成"
End Sub

Function DetectPython()
    ' 检测Python
    On Error Resume Next
    DetectPython = ""
    
    Dim arrPaths, strPath
    arrPaths = Array("python", "python3", "py")
    
    For Each strPath In arrPaths
        objShell.Run strPath & " --version", 0, True
        If Err.Number = 0 Then
            DetectPython = strPath
            Exit Function
        End If
        Err.Clear
    Next
End Function

Sub FixProgramFiles()
    ' 修复程序文件
    On Error Resume Next
    
    WriteLog "修复程序文件"
    
    ' 修复main.py
    FixMainPy()
    
    ' 创建必要目录
    CreateNecessaryDirectories()
    
    ' 创建初始化文件
    CreateInitFiles()
    
    WriteLog "程序文件修复完成"
End Sub

Sub FixMainPy()
    ' 修复main.py文件
    On Error Resume Next
    
    Dim strMainPy
    strMainPy = strCurrentDir & "\main.py"
    
    If objFSO.FileExists(strMainPy) Then
        Dim objFile, strContent
        Set objFile = objFSO.OpenTextFile(strMainPy, 1)
        strContent = objFile.ReadAll
        objFile.Close
        
        ' 修复依赖检查
        strContent = Replace(strContent, "'Pillow'", "'PIL'")
        strContent = Replace(strContent, "app.run()", "app.mainloop()")
        
        ' 写回文件
        Set objFile = objFSO.CreateTextFile(strMainPy, True)
        objFile.Write strContent
        objFile.Close
        
        WriteLog "main.py修复完成"
    End If
End Sub

Sub CreateNecessaryDirectories()
    ' 创建必要目录
    On Error Resume Next
    
    Dim arrDirs, strDir
    arrDirs = Array("logs", "backups", "temp", "emergency_backups")
    
    For Each strDir In arrDirs
        If Not objFSO.FolderExists(strCurrentDir & "\" & strDir) Then
            objFSO.CreateFolder(strCurrentDir & "\" & strDir)
            WriteLog "创建目录: " & strDir
        End If
    Next
End Sub

Sub CreateInitFiles()
    ' 创建初始化文件
    On Error Resume Next
    
    Dim arrInitFiles, strInitFile
    arrInitFiles = Array("gui\__init__.py", "utils\__init__.py", "augutils\__init__.py")
    
    For Each strInitFile In arrInitFiles
        If Not objFSO.FileExists(strCurrentDir & "\" & strInitFile) Then
            Dim objInitFile
            Set objInitFile = objFSO.CreateTextFile(strCurrentDir & "\" & strInitFile, True)
            objInitFile.WriteLine "# -*- coding: utf-8 -*-"
            objInitFile.Close
            WriteLog "创建文件: " & strInitFile
        End If
    Next
End Sub

Sub VerifyRepair()
    ' 验证修复结果
    On Error Resume Next
    
    WriteLog "验证修复结果"
    
    ' 测试Python导入
    Dim strTestResult
    objShell.Run "python -c ""import customtkinter, PIL, requests; print('所有依赖导入成功')""", 0, True
    
    If Err.Number = 0 Then
        strTestResult = "✅ 依赖测试通过"
        WriteLog "依赖测试通过"
    Else
        strTestResult = "❌ 依赖测试失败"
        WriteLog "依赖测试失败"
    End If
    
    ' 显示修复结果
    MsgBox "🎉 深度修复完成！" & vbCrLf & vbCrLf & _
           "修复结果:" & vbCrLf & _
           "✅ Rust组件已清理" & vbCrLf & _
           "✅ Python环境已重建" & vbCrLf & _
           "✅ 系统注册表已清理" & vbCrLf & _
           "✅ 程序文件已修复" & vbCrLf & _
           strTestResult & vbCrLf & vbCrLf & _
           "现在可以尝试启动程序了！" & vbCrLf & vbCrLf & _
           "建议重启计算机以确保所有更改生效", _
           vbInformation, "深度修复完成"
    
    WriteLog "深度修复流程全部完成"
    
    ' 询问是否立即启动
    Dim intLaunch
    intLaunch = MsgBox("是否立即尝试启动AugmentNew？", vbYesNo + vbQuestion, "启动程序")
    
    If intLaunch = vbYes Then
        LaunchProgram()
    End If
End Sub

Sub LaunchProgram()
    ' 启动程序
    On Error Resume Next
    
    WriteLog "尝试启动程序"
    
    objShell.CurrentDirectory = strCurrentDir
    objShell.Run "python main.py", 1, False
    
    WriteLog "程序启动命令已执行"
End Sub

' 清理对象
Set objShell = Nothing
Set objFSO = Nothing
