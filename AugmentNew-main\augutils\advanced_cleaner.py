"""
高级清理工具 - 解决账号注册限制问题
专门针对频繁注册被限制的情况
"""

import os
import sys
import json
import time
import random
import shutil
import winreg
import subprocess
from pathlib import Path
from typing import Dict, List, Optional
from utils.device_codes import generate_machine_id, generate_device_id


class AdvancedCleaner:
    """高级清理器 - 解决注册限制问题"""
    
    def __init__(self):
        self.backup_dir = Path("advanced_backups")
        self.backup_dir.mkdir(exist_ok=True)
        
    def deep_system_reset(self) -> Dict:
        """深度系统重置 - 解决注册限制"""
        result = {
            'success': False,
            'operations': [],
            'errors': [],
            'backup_path': None
        }
        
        try:
            # 创建备份
            backup_path = self._create_system_backup()
            result['backup_path'] = str(backup_path)
            
            # 1. 清理VS Code相关注册表
            self._clean_vscode_registry()
            result['operations'].append('清理VS Code注册表')
            
            # 2. 清理系统临时文件
            self._clean_system_temp()
            result['operations'].append('清理系统临时文件')
            
            # 3. 重置网络配置
            self._reset_network_config()
            result['operations'].append('重置网络配置')
            
            # 4. 清理浏览器数据
            self._clean_browser_data()
            result['operations'].append('清理浏览器数据')
            
            # 5. 修改系统标识
            self._modify_system_identifiers()
            result['operations'].append('修改系统标识')
            
            # 6. 清理应用数据
            self._clean_application_data()
            result['operations'].append('清理应用数据')
            
            result['success'] = True
            
        except Exception as e:
            result['errors'].append(str(e))
            
        return result
    
    def _create_system_backup(self) -> Path:
        """创建系统备份"""
        timestamp = int(time.time())
        backup_path = self.backup_dir / f"system_backup_{timestamp}"
        backup_path.mkdir(exist_ok=True)
        
        # 备份重要文件
        important_paths = [
            os.path.expandvars(r"%APPDATA%\Code"),
            os.path.expandvars(r"%LOCALAPPDATA%\Programs\Microsoft VS Code"),
            os.path.expandvars(r"%USERPROFILE%\.vscode")
        ]
        
        for path in important_paths:
            if os.path.exists(path):
                dest = backup_path / Path(path).name
                try:
                    shutil.copytree(path, dest, ignore_errors=True)
                except:
                    pass
                    
        return backup_path
    
    def _clean_vscode_registry(self):
        """清理VS Code相关注册表"""
        if sys.platform != "win32":
            return
            
        try:
            # 清理HKEY_CURRENT_USER下的VS Code相关项
            registry_paths = [
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
                r"SOFTWARE\Classes\Applications",
                r"SOFTWARE\Classes\vscode",
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\ApplicationAssociationToasts"
            ]
            
            for reg_path in registry_paths:
                try:
                    key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, reg_path, 0, winreg.KEY_ALL_ACCESS)
                    self._clean_registry_key(key, "vscode")
                    self._clean_registry_key(key, "code")
                    winreg.CloseKey(key)
                except:
                    pass
                    
        except Exception:
            pass
    
    def _clean_registry_key(self, parent_key, search_term):
        """清理包含特定关键词的注册表项"""
        try:
            i = 0
            while True:
                try:
                    subkey_name = winreg.EnumKey(parent_key, i)
                    if search_term.lower() in subkey_name.lower():
                        winreg.DeleteKey(parent_key, subkey_name)
                    else:
                        i += 1
                except OSError:
                    break
        except:
            pass
    
    def _clean_system_temp(self):
        """清理系统临时文件"""
        temp_dirs = [
            os.environ.get('TEMP', ''),
            os.environ.get('TMP', ''),
            os.path.expandvars(r"%LOCALAPPDATA%\Temp"),
            os.path.expandvars(r"%WINDIR%\Temp")
        ]
        
        for temp_dir in temp_dirs:
            if temp_dir and os.path.exists(temp_dir):
                try:
                    for item in os.listdir(temp_dir):
                        item_path = os.path.join(temp_dir, item)
                        try:
                            if os.path.isfile(item_path):
                                os.remove(item_path)
                            elif os.path.isdir(item_path):
                                shutil.rmtree(item_path, ignore_errors=True)
                        except:
                            pass
                except:
                    pass
    
    def _reset_network_config(self):
        """重置网络配置"""
        if sys.platform != "win32":
            return
            
        try:
            # 清理DNS缓存
            subprocess.run(["ipconfig", "/flushdns"], 
                         capture_output=True, check=False)
            
            # 重置网络适配器
            subprocess.run(["netsh", "winsock", "reset"], 
                         capture_output=True, check=False)
            
            # 重置TCP/IP栈
            subprocess.run(["netsh", "int", "ip", "reset"], 
                         capture_output=True, check=False)
                         
        except:
            pass
    
    def _clean_browser_data(self):
        """清理浏览器数据"""
        browser_paths = {
            'Chrome': os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data"),
            'Edge': os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data"),
            'Firefox': os.path.expandvars(r"%APPDATA%\Mozilla\Firefox\Profiles")
        }
        
        for browser, path in browser_paths.items():
            if os.path.exists(path):
                try:
                    # 清理特定文件
                    files_to_clean = [
                        "Cookies", "History", "Login Data", 
                        "Web Data", "Preferences", "Local State"
                    ]
                    
                    for root, dirs, files in os.walk(path):
                        for file in files:
                            if any(clean_file in file for clean_file in files_to_clean):
                                try:
                                    os.remove(os.path.join(root, file))
                                except:
                                    pass
                except:
                    pass
    
    def _modify_system_identifiers(self):
        """修改系统标识符"""
        try:
            # 生成新的设备ID
            new_machine_id = generate_machine_id()
            new_device_id = generate_device_id()
            
            # 修改VS Code相关的ID文件
            id_files = [
                os.path.expandvars(r"%APPDATA%\Code\machineid"),
                os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\storage.json")
            ]
            
            for id_file in id_files:
                if os.path.exists(id_file):
                    try:
                        if id_file.endswith('machineid'):
                            with open(id_file, 'w') as f:
                                f.write(new_machine_id)
                        elif id_file.endswith('storage.json'):
                            self._update_storage_json(id_file, new_device_id)
                    except:
                        pass
                        
        except:
            pass
    
    def _update_storage_json(self, file_path: str, new_device_id: str):
        """更新storage.json文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 更新设备相关的ID
            if 'telemetry.machineId' in data:
                data['telemetry.machineId'] = new_device_id
            if 'telemetry.devDeviceId' in data:
                data['telemetry.devDeviceId'] = new_device_id
                
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2)
                
        except:
            pass
    
    def _clean_application_data(self):
        """清理应用程序数据"""
        app_data_paths = [
            os.path.expandvars(r"%APPDATA%\Code"),
            os.path.expandvars(r"%LOCALAPPDATA%\Programs\Microsoft VS Code\User"),
            os.path.expandvars(r"%USERPROFILE%\.vscode"),
            os.path.expandvars(r"%USERPROFILE%\.vscode-insiders")
        ]
        
        for app_path in app_data_paths:
            if os.path.exists(app_path):
                try:
                    # 清理特定文件和文件夹
                    items_to_clean = [
                        "logs", "CachedData", "User/workspaceStorage",
                        "User/History", "User/globalStorage"
                    ]
                    
                    for item in items_to_clean:
                        item_path = os.path.join(app_path, item)
                        if os.path.exists(item_path):
                            try:
                                if os.path.isfile(item_path):
                                    os.remove(item_path)
                                else:
                                    shutil.rmtree(item_path, ignore_errors=True)
                            except:
                                pass
                except:
                    pass
    
    def generate_registration_strategy(self) -> Dict:
        """生成注册策略建议"""
        strategies = {
            'timing': {
                'wait_time': random.randint(3600, 7200),  # 1-2小时
                'best_hours': [9, 10, 14, 15, 20, 21],  # 最佳注册时间
                'avoid_hours': [0, 1, 2, 3, 4, 5, 6]    # 避免的时间
            },
            'environment': {
                'use_incognito': True,
                'clear_cookies': True,
                'use_vpn': True,
                'change_user_agent': True
            },
            'email': {
                'use_temp_email': True,
                'email_providers': [
                    '10minutemail.com',
                    'guerrillamail.com',
                    'tempmail.org'
                ]
            },
            'tips': [
                '使用不同的浏览器或无痕模式',
                '每次注册间隔至少1小时',
                '使用临时邮箱服务',
                '避免在同一IP下频繁注册',
                '模拟真实用户行为，不要过快操作'
            ]
        }
        
        return strategies


def create_registration_helper():
    """创建注册助手"""
    cleaner = AdvancedCleaner()
    
    print("🚀 AugmentCode 注册助手")
    print("=" * 50)
    
    # 执行深度清理
    print("正在执行深度系统清理...")
    result = cleaner.deep_system_reset()
    
    if result['success']:
        print("✅ 系统清理完成！")
        print(f"📦 备份位置: {result['backup_path']}")
        print("🔧 执行的操作:")
        for op in result['operations']:
            print(f"  - {op}")
    else:
        print("❌ 清理过程中出现错误:")
        for error in result['errors']:
            print(f"  - {error}")
    
    # 生成注册策略
    print("\n📋 注册策略建议:")
    strategy = cleaner.generate_registration_strategy()
    
    print(f"⏰ 建议等待时间: {strategy['timing']['wait_time']} 秒")
    print(f"🕐 最佳注册时间: {strategy['timing']['best_hours']}")
    print("💡 注册技巧:")
    for tip in strategy['tips']:
        print(f"  - {tip}")


if __name__ == "__main__":
    create_registration_helper()
