"""
数据恢复工具
提供备份文件的恢复功能
"""

import os
import json
import shutil
import zipfile
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime


class DataRecovery:
    """数据恢复类"""
    
    def __init__(self):
        self.backup_extensions = ['.bak', '.backup', '.zip']
        
    def scan_backup_files(self, directory: str = ".") -> List[Dict]:
        """扫描备份文件"""
        backup_files = []
        
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    
                    # 检查是否是备份文件
                    if any(file.endswith(ext) for ext in self.backup_extensions):
                        if any(keyword in file.lower() for keyword in 
                               ['backup', 'bak', 'storage', 'machine', 'workspace']):
                            
                            backup_info = self._analyze_backup_file(file_path)
                            if backup_info:
                                backup_files.append(backup_info)
                                
        except Exception as e:
            print(f"扫描备份文件时出错: {e}")
            
        return sorted(backup_files, key=lambda x: x['timestamp'], reverse=True)
    
    def _analyze_backup_file(self, file_path: str) -> Optional[Dict]:
        """分析备份文件信息"""
        try:
            stat = os.stat(file_path)
            file_name = os.path.basename(file_path)
            
            # 尝试从文件名提取时间戳
            timestamp = stat.st_mtime
            try:
                # 查找文件名中的时间戳
                import re
                timestamp_match = re.search(r'(\d{10})', file_name)
                if timestamp_match:
                    timestamp = int(timestamp_match.group(1))
            except:
                pass
            
            # 确定备份类型
            backup_type = "未知"
            if "storage" in file_name.lower():
                backup_type = "存储文件"
            elif "machine" in file_name.lower():
                backup_type = "机器ID"
            elif "workspace" in file_name.lower():
                backup_type = "工作区"
            elif "database" in file_name.lower() or "db" in file_name.lower():
                backup_type = "数据库"
            
            return {
                'path': file_path,
                'name': file_name,
                'type': backup_type,
                'size': stat.st_size,
                'timestamp': timestamp,
                'date': datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S'),
                'readable_size': self._format_size(stat.st_size)
            }
            
        except Exception:
            return None
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    def restore_from_backup(self, backup_path: str, target_path: str = None) -> Dict:
        """从备份恢复数据"""
        result = {
            'success': False,
            'message': '',
            'restored_files': [],
            'errors': []
        }
        
        try:
            if not os.path.exists(backup_path):
                result['message'] = f"备份文件不存在: {backup_path}"
                return result
            
            # 确定目标路径
            if not target_path:
                target_path = self._determine_target_path(backup_path)
            
            if not target_path:
                result['message'] = "无法确定恢复目标路径"
                return result
            
            # 创建目标目录
            os.makedirs(os.path.dirname(target_path), exist_ok=True)
            
            # 根据文件类型进行恢复
            if backup_path.endswith('.zip'):
                restored_files = self._restore_from_zip(backup_path, target_path)
            else:
                restored_files = self._restore_single_file(backup_path, target_path)
            
            result['restored_files'] = restored_files
            result['success'] = True
            result['message'] = f"成功恢复 {len(restored_files)} 个文件"
            
        except Exception as e:
            result['message'] = f"恢复失败: {str(e)}"
            result['errors'].append(str(e))
            
        return result
    
    def _determine_target_path(self, backup_path: str) -> Optional[str]:
        """确定恢复目标路径"""
        file_name = os.path.basename(backup_path).lower()
        
        # 根据备份文件名确定目标路径
        if "storage" in file_name:
            from utils.paths import get_storage_path
            return get_storage_path()
        elif "machine" in file_name:
            from utils.paths import get_machine_id_path
            return get_machine_id_path()
        elif "workspace" in file_name:
            from utils.paths import get_workspace_storage_path
            return get_workspace_storage_path()
        elif "database" in file_name or "db" in file_name:
            from utils.paths import get_db_path
            return get_db_path()
        
        return None
    
    def _restore_from_zip(self, zip_path: str, target_dir: str) -> List[str]:
        """从ZIP文件恢复"""
        restored_files = []
        
        with zipfile.ZipFile(zip_path, 'r') as zip_file:
            for file_info in zip_file.filelist:
                if not file_info.is_dir():
                    # 提取文件
                    zip_file.extract(file_info, target_dir)
                    restored_path = os.path.join(target_dir, file_info.filename)
                    restored_files.append(restored_path)
        
        return restored_files
    
    def _restore_single_file(self, backup_path: str, target_path: str) -> List[str]:
        """恢复单个文件"""
        shutil.copy2(backup_path, target_path)
        return [target_path]
    
    def create_restore_point(self, description: str = "") -> Dict:
        """创建系统还原点"""
        result = {
            'success': False,
            'restore_point_path': '',
            'message': ''
        }
        
        try:
            timestamp = int(datetime.now().timestamp())
            restore_point_dir = Path(f"restore_points/restore_point_{timestamp}")
            restore_point_dir.mkdir(parents=True, exist_ok=True)
            
            # 备份关键文件
            from utils.paths import (get_storage_path, get_machine_id_path, 
                                   get_db_path, get_workspace_storage_path)
            
            critical_files = [
                ('storage.json', get_storage_path()),
                ('machineid', get_machine_id_path()),
                ('state.vscdb', get_db_path()),
                ('workspace_storage', get_workspace_storage_path())
            ]
            
            backed_up_files = []
            for name, path in critical_files:
                if os.path.exists(path):
                    try:
                        dest_path = restore_point_dir / name
                        if os.path.isfile(path):
                            shutil.copy2(path, dest_path)
                        else:
                            shutil.copytree(path, dest_path, ignore_errors=True)
                        backed_up_files.append(name)
                    except Exception as e:
                        print(f"备份 {name} 失败: {e}")
            
            # 创建还原点信息文件
            restore_info = {
                'timestamp': timestamp,
                'date': datetime.now().isoformat(),
                'description': description or f"自动还原点 {timestamp}",
                'backed_up_files': backed_up_files
            }
            
            info_file = restore_point_dir / 'restore_info.json'
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(restore_info, f, indent=2, ensure_ascii=False)
            
            result['success'] = True
            result['restore_point_path'] = str(restore_point_dir)
            result['message'] = f"还原点创建成功，备份了 {len(backed_up_files)} 个文件"
            
        except Exception as e:
            result['message'] = f"创建还原点失败: {str(e)}"
            
        return result
    
    def list_restore_points(self) -> List[Dict]:
        """列出所有还原点"""
        restore_points = []
        restore_points_dir = Path("restore_points")
        
        if not restore_points_dir.exists():
            return restore_points
        
        try:
            for point_dir in restore_points_dir.iterdir():
                if point_dir.is_dir():
                    info_file = point_dir / 'restore_info.json'
                    if info_file.exists():
                        try:
                            with open(info_file, 'r', encoding='utf-8') as f:
                                info = json.load(f)
                                info['path'] = str(point_dir)
                                restore_points.append(info)
                        except:
                            pass
        except:
            pass
        
        return sorted(restore_points, key=lambda x: x['timestamp'], reverse=True)
    
    def restore_from_point(self, restore_point_path: str) -> Dict:
        """从还原点恢复"""
        result = {
            'success': False,
            'message': '',
            'restored_files': []
        }
        
        try:
            point_dir = Path(restore_point_path)
            info_file = point_dir / 'restore_info.json'
            
            if not info_file.exists():
                result['message'] = "还原点信息文件不存在"
                return result
            
            with open(info_file, 'r', encoding='utf-8') as f:
                info = json.load(f)
            
            # 恢复文件
            from utils.paths import (get_storage_path, get_machine_id_path, 
                                   get_db_path, get_workspace_storage_path)
            
            file_mappings = {
                'storage.json': get_storage_path(),
                'machineid': get_machine_id_path(),
                'state.vscdb': get_db_path(),
                'workspace_storage': get_workspace_storage_path()
            }
            
            restored_files = []
            for file_name in info['backed_up_files']:
                source_path = point_dir / file_name
                target_path = file_mappings.get(file_name)
                
                if source_path.exists() and target_path:
                    try:
                        # 确保目标目录存在
                        os.makedirs(os.path.dirname(target_path), exist_ok=True)
                        
                        if source_path.is_file():
                            shutil.copy2(source_path, target_path)
                        else:
                            if os.path.exists(target_path):
                                shutil.rmtree(target_path)
                            shutil.copytree(source_path, target_path)
                        
                        restored_files.append(target_path)
                    except Exception as e:
                        print(f"恢复 {file_name} 失败: {e}")
            
            result['success'] = True
            result['restored_files'] = restored_files
            result['message'] = f"成功从还原点恢复 {len(restored_files)} 个文件"
            
        except Exception as e:
            result['message'] = f"从还原点恢复失败: {str(e)}"
            
        return result


if __name__ == "__main__":
    # 测试数据恢复功能
    recovery = DataRecovery()
    
    print("🔍 扫描备份文件...")
    backups = recovery.scan_backup_files()
    
    if backups:
        print(f"找到 {len(backups)} 个备份文件:")
        for backup in backups[:5]:  # 显示前5个
            print(f"  - {backup['name']} ({backup['type']}) - {backup['date']} - {backup['readable_size']}")
    else:
        print("未找到备份文件")
    
    print("\n📋 还原点列表:")
    restore_points = recovery.list_restore_points()
    if restore_points:
        for point in restore_points[:3]:  # 显示前3个
            print(f"  - {point['description']} - {point['date']}")
    else:
        print("未找到还原点")
