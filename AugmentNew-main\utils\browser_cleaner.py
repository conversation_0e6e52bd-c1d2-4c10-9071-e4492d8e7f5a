#!/usr/bin/env python3
"""
多浏览器清理模块
支持Chrome、Edge、Firefox等主流浏览器的深度清理
"""

import os
import shutil
import json
import sqlite3
import winreg
from pathlib import Path
from typing import List, Dict, Optional
import logging

class BrowserCleaner:
    """多浏览器清理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.user_profile = os.environ.get('USERPROFILE', '')
        self.appdata_local = os.path.join(self.user_profile, 'AppData', 'Local')
        self.appdata_roaming = os.path.join(self.user_profile, 'AppData', 'Roaming')
        
    def get_browser_paths(self) -> Dict[str, Dict[str, str]]:
        """获取各浏览器的路径配置"""
        return {
            'chrome': {
                'name': 'Google Chrome',
                'user_data': os.path.join(self.appdata_local, 'Google', 'Chrome', 'User Data'),
                'cache': os.path.join(self.appdata_local, 'Google', 'Chrome', 'User Data', 'Default', 'Cache'),
                'cookies': os.path.join(self.appdata_local, 'Google', 'Chrome', 'User Data', 'Default', 'Cookies'),
                'local_storage': os.path.join(self.appdata_local, 'Google', 'Chrome', 'User Data', 'Default', 'Local Storage'),
                'session_storage': os.path.join(self.appdata_local, 'Google', 'Chrome', 'User Data', 'Default', 'Session Storage'),
                'preferences': os.path.join(self.appdata_local, 'Google', 'Chrome', 'User Data', 'Default', 'Preferences'),
                'web_data': os.path.join(self.appdata_local, 'Google', 'Chrome', 'User Data', 'Default', 'Web Data'),
            },
            'edge': {
                'name': 'Microsoft Edge',
                'user_data': os.path.join(self.appdata_local, 'Microsoft', 'Edge', 'User Data'),
                'cache': os.path.join(self.appdata_local, 'Microsoft', 'Edge', 'User Data', 'Default', 'Cache'),
                'cookies': os.path.join(self.appdata_local, 'Microsoft', 'Edge', 'User Data', 'Default', 'Cookies'),
                'local_storage': os.path.join(self.appdata_local, 'Microsoft', 'Edge', 'User Data', 'Default', 'Local Storage'),
                'session_storage': os.path.join(self.appdata_local, 'Microsoft', 'Edge', 'User Data', 'Default', 'Session Storage'),
                'preferences': os.path.join(self.appdata_local, 'Microsoft', 'Edge', 'User Data', 'Default', 'Preferences'),
                'web_data': os.path.join(self.appdata_local, 'Microsoft', 'Edge', 'User Data', 'Default', 'Web Data'),
            },
            'firefox': {
                'name': 'Mozilla Firefox',
                'profiles': os.path.join(self.appdata_roaming, 'Mozilla', 'Firefox', 'Profiles'),
                'cache': os.path.join(self.appdata_local, 'Mozilla', 'Firefox', 'Profiles'),
            },
            'opera': {
                'name': 'Opera',
                'user_data': os.path.join(self.appdata_roaming, 'Opera Software', 'Opera Stable'),
                'cache': os.path.join(self.appdata_local, 'Opera Software', 'Opera Stable', 'Cache'),
            },
            'brave': {
                'name': 'Brave Browser',
                'user_data': os.path.join(self.appdata_local, 'BraveSoftware', 'Brave-Browser', 'User Data'),
                'cache': os.path.join(self.appdata_local, 'BraveSoftware', 'Brave-Browser', 'User Data', 'Default', 'Cache'),
            }
        }
    
    def detect_installed_browsers(self) -> List[str]:
        """检测已安装的浏览器"""
        installed = []
        browser_paths = self.get_browser_paths()
        
        for browser_key, paths in browser_paths.items():
            if browser_key == 'firefox':
                if os.path.exists(paths['profiles']):
                    installed.append(browser_key)
            else:
                if os.path.exists(paths['user_data']):
                    installed.append(browser_key)
        
        return installed
    
    def backup_browser_data(self, browser: str, backup_dir: str) -> bool:
        """备份浏览器数据"""
        try:
            browser_paths = self.get_browser_paths()
            if browser not in browser_paths:
                return False
            
            browser_backup_dir = os.path.join(backup_dir, f"{browser}_backup")
            os.makedirs(browser_backup_dir, exist_ok=True)
            
            paths = browser_paths[browser]
            
            # 备份主要数据目录
            if 'user_data' in paths and os.path.exists(paths['user_data']):
                backup_path = os.path.join(browser_backup_dir, 'user_data')
                shutil.copytree(paths['user_data'], backup_path, ignore_errors=True)
            
            # 备份Firefox配置文件
            if browser == 'firefox' and os.path.exists(paths['profiles']):
                backup_path = os.path.join(browser_backup_dir, 'profiles')
                shutil.copytree(paths['profiles'], backup_path, ignore_errors=True)
            
            self.logger.info(f"已备份 {browser} 数据到 {browser_backup_dir}")
            return True
            
        except Exception as e:
            self.logger.error(f"备份 {browser} 数据失败: {e}")
            return False
    
    def clear_browser_augment_data(self, browser: str) -> Dict[str, any]:
        """清理指定浏览器的Augment相关数据"""
        result = {
            'success': False,
            'browser': browser,
            'cleared_items': [],
            'errors': []
        }
        
        try:
            browser_paths = self.get_browser_paths()
            if browser not in browser_paths:
                result['errors'].append(f"不支持的浏览器: {browser}")
                return result
            
            paths = browser_paths[browser]
            
            # 清理Local Storage中的Augment数据
            if 'local_storage' in paths:
                self._clear_local_storage_augment(paths['local_storage'], result)
            
            # 清理Cookies中的Augment相关数据
            if 'cookies' in paths:
                self._clear_cookies_augment(paths['cookies'], result)
            
            # 清理缓存（选择性清理）
            if 'cache' in paths:
                self._clear_cache_selective(paths['cache'], result)
            
            # 清理Session Storage（选择性清理）
            if 'session_storage' in paths:
                self._clear_session_storage_augment(paths['session_storage'], result)
            
            # 清理Web Data (表单数据等)
            if 'web_data' in paths:
                self._clear_web_data_augment(paths['web_data'], result)
            
            # 修改Preferences文件
            if 'preferences' in paths:
                self._modify_preferences_augment(paths['preferences'], result)
            
            # Firefox特殊处理
            if browser == 'firefox':
                self._clear_firefox_augment_data(paths, result)
            
            result['success'] = len(result['errors']) == 0
            
        except Exception as e:
            result['errors'].append(f"清理 {browser} 失败: {str(e)}")
            self.logger.error(f"清理 {browser} 失败: {e}")
        
        return result
    
    def clear_all_browsers_augment_data(self, backup_dir: str = None) -> Dict[str, any]:
        """清理所有浏览器的Augment数据"""
        results = {
            'success': True,
            'browsers_processed': [],
            'total_cleared': 0,
            'errors': []
        }
        
        installed_browsers = self.detect_installed_browsers()
        
        for browser in installed_browsers:
            try:
                # 备份数据
                if backup_dir:
                    self.backup_browser_data(browser, backup_dir)
                
                # 清理数据
                browser_result = self.clear_browser_augment_data(browser)
                results['browsers_processed'].append(browser_result)
                
                if browser_result['success']:
                    results['total_cleared'] += len(browser_result['cleared_items'])
                else:
                    results['success'] = False
                    results['errors'].extend(browser_result['errors'])
                    
            except Exception as e:
                results['success'] = False
                results['errors'].append(f"处理 {browser} 时出错: {str(e)}")
        
        return results

    def _get_augment_domains(self) -> List[str]:
        """获取Augment相关的精确域名列表"""
        return [
            'augmentcode.com',
            'augment.dev',
            'app.augmentcode.com',
            'api.augmentcode.com',
            'auth.augmentcode.com',
            'dashboard.augmentcode.com',
            'docs.augmentcode.com',
            'support.augmentcode.com'
        ]

    def _get_augment_patterns(self) -> List[str]:
        """获取Augment相关的匹配模式"""
        return [
            'augmentcode',
            'augment-code',
            'augment_code',
            'vscode-augment',
            'augment-extension',
            'augment-vscode',
            'augment-ai',
            'augment-assistant'
        ]

    def _get_github_augment_patterns(self) -> List[str]:
        """获取GitHub上Augment相关的匹配模式"""
        return [
            'github.com_augmentcode',
            'github.com/augmentcode',
            'augmentcode.github.io'
        ]

    def _clear_local_storage_augment(self, local_storage_path: str, result: Dict) -> None:
        """精确清理Local Storage中的Augment相关数据"""
        try:
            if not os.path.exists(local_storage_path):
                return

            augment_patterns = self._get_augment_patterns()
            augment_domains = self._get_augment_domains()
            github_patterns = self._get_github_augment_patterns()

            cleared_count = 0

            for file_name in os.listdir(local_storage_path):
                file_path = os.path.join(local_storage_path, file_name)
                should_delete = False

                # 检查文件名是否包含Augment相关关键词
                for pattern in augment_patterns:
                    if pattern.lower() in file_name.lower():
                        should_delete = True
                        self.logger.debug(f"匹配到模式 '{pattern}': {file_name}")
                        break

                # 检查是否包含特定域名
                if not should_delete:
                    for domain in augment_domains:
                        if domain.lower().replace('.', '_') in file_name.lower():
                            should_delete = True
                            self.logger.debug(f"匹配到域名 '{domain}': {file_name}")
                            break

                # 检查GitHub相关模式
                if not should_delete:
                    for pattern in github_patterns:
                        if pattern.lower() in file_name.lower():
                            should_delete = True
                            self.logger.debug(f"匹配到GitHub模式 '{pattern}': {file_name}")
                            break

                if should_delete:
                    try:
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                            result['cleared_items'].append(f"Local Storage: {file_name}")
                            cleared_count += 1
                        elif os.path.isdir(file_path):
                            shutil.rmtree(file_path)
                            result['cleared_items'].append(f"Local Storage Dir: {file_name}")
                            cleared_count += 1
                    except Exception as e:
                        result['errors'].append(f"删除Local Storage文件失败 {file_name}: {e}")

            if cleared_count > 0:
                self.logger.info(f"Local Storage: 清理了 {cleared_count} 个Augment相关项目")

        except Exception as e:
            result['errors'].append(f"清理Local Storage失败: {e}")
            self.logger.error(f"清理Local Storage失败: {e}")

    def _clear_cookies_augment(self, cookies_path: str, result: Dict) -> None:
        """精确清理Cookies中的Augment相关数据"""
        try:
            if not os.path.exists(cookies_path):
                return

            # 连接到Cookies数据库
            conn = sqlite3.connect(cookies_path)
            cursor = conn.cursor()

            # 构建精确的域名匹配列表
            augment_domains = []
            for domain in self._get_augment_domains():
                augment_domains.append(f'%{domain}%')
                augment_domains.append(f'%.{domain}%')  # 包含子域名

            # 添加GitHub相关的精确匹配
            github_patterns = [
                '%github.com/augmentcode%',
                '%augmentcode.github.io%'
            ]

            deleted_count = 0
            deleted_details = []

            # 删除Augment域名相关的cookies
            for domain in augment_domains:
                cursor.execute("SELECT host_key, name FROM cookies WHERE host_key LIKE ?", (domain,))
                cookies_to_delete = cursor.fetchall()

                if cookies_to_delete:
                    cursor.execute("DELETE FROM cookies WHERE host_key LIKE ?", (domain,))
                    count = cursor.rowcount
                    if count > 0:
                        deleted_count += count
                        deleted_details.append(f"域名 {domain.strip('%')}: {count} 个")

            # 删除GitHub相关的cookies
            for pattern in github_patterns:
                cursor.execute("SELECT host_key, name FROM cookies WHERE host_key LIKE ?", (pattern,))
                cookies_to_delete = cursor.fetchall()

                if cookies_to_delete:
                    cursor.execute("DELETE FROM cookies WHERE host_key LIKE ?", (pattern,))
                    count = cursor.rowcount
                    if count > 0:
                        deleted_count += count
                        deleted_details.append(f"GitHub模式 {pattern.strip('%')}: {count} 个")

            conn.commit()
            conn.close()

            if deleted_count > 0:
                result['cleared_items'].append(f"Cookies: 删除了 {deleted_count} 个相关cookie")
                for detail in deleted_details:
                    result['cleared_items'].append(f"  - {detail}")
                self.logger.info(f"Cookies: 删除了 {deleted_count} 个Augment相关cookie")

        except Exception as e:
            result['errors'].append(f"清理Cookies失败: {e}")
            self.logger.error(f"清理Cookies失败: {e}")

    def _clear_cache_selective(self, cache_path: str, result: Dict) -> None:
        """选择性清理浏览器缓存中的Augment相关数据"""
        try:
            if not os.path.exists(cache_path):
                return

            augment_patterns = self._get_augment_patterns()
            augment_domains = self._get_augment_domains()

            cleared_count = 0

            # 遍历缓存目录，只删除Augment相关的缓存文件
            for root, dirs, files in os.walk(cache_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    should_delete = False

                    # 检查文件名或路径是否包含Augment相关内容
                    file_lower = file.lower()
                    path_lower = file_path.lower()

                    for pattern in augment_patterns:
                        if pattern.lower() in file_lower or pattern.lower() in path_lower:
                            should_delete = True
                            break

                    if not should_delete:
                        for domain in augment_domains:
                            domain_pattern = domain.replace('.', '_')
                            if domain_pattern.lower() in file_lower or domain_pattern.lower() in path_lower:
                                should_delete = True
                                break

                    if should_delete:
                        try:
                            os.remove(file_path)
                            cleared_count += 1
                        except:
                            pass

            if cleared_count > 0:
                result['cleared_items'].append(f"Cache: 清理了 {cleared_count} 个Augment相关缓存文件")
                self.logger.info(f"Cache: 清理了 {cleared_count} 个Augment相关缓存文件")
            else:
                result['cleared_items'].append("Cache: 未找到Augment相关缓存文件")

        except Exception as e:
            result['errors'].append(f"清理缓存失败: {e}")
            self.logger.error(f"清理缓存失败: {e}")

    def _clear_session_storage_augment(self, session_storage_path: str, result: Dict) -> None:
        """选择性清理Session Storage中的Augment相关数据"""
        try:
            if not os.path.exists(session_storage_path):
                return

            augment_patterns = self._get_augment_patterns()
            augment_domains = self._get_augment_domains()

            cleared_count = 0

            # 只删除Augment相关的session storage文件
            for file_name in os.listdir(session_storage_path):
                file_path = os.path.join(session_storage_path, file_name)
                should_delete = False

                # 检查文件名是否包含Augment相关关键词
                for pattern in augment_patterns:
                    if pattern.lower() in file_name.lower():
                        should_delete = True
                        break

                if not should_delete:
                    for domain in augment_domains:
                        domain_pattern = domain.replace('.', '_')
                        if domain_pattern.lower() in file_name.lower():
                            should_delete = True
                            break

                if should_delete:
                    try:
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                            cleared_count += 1
                        elif os.path.isdir(file_path):
                            shutil.rmtree(file_path)
                            cleared_count += 1
                    except:
                        pass

            if cleared_count > 0:
                result['cleared_items'].append(f"Session Storage: 清理了 {cleared_count} 个Augment相关项目")
                self.logger.info(f"Session Storage: 清理了 {cleared_count} 个Augment相关项目")
            else:
                result['cleared_items'].append("Session Storage: 未找到Augment相关数据")

        except Exception as e:
            result['errors'].append(f"清理Session Storage失败: {e}")
            self.logger.error(f"清理Session Storage失败: {e}")

    def _clear_web_data_augment(self, web_data_path: str, result: Dict) -> None:
        """清理Web Data中的Augment相关数据"""
        try:
            if not os.path.exists(web_data_path):
                return

            conn = sqlite3.connect(web_data_path)
            cursor = conn.cursor()

            # 清理自动填充数据
            try:
                cursor.execute("DELETE FROM autofill WHERE name LIKE '%augment%'")
                cursor.execute("DELETE FROM autofill_profiles WHERE company_name LIKE '%augment%'")
                result['cleared_items'].append("Web Data: 已清理自动填充数据")
            except:
                pass

            conn.commit()
            conn.close()

        except Exception as e:
            result['errors'].append(f"清理Web Data失败: {e}")

    def _modify_preferences_augment(self, preferences_path: str, result: Dict) -> None:
        """修改Preferences文件，移除Augment相关配置"""
        try:
            if not os.path.exists(preferences_path):
                return

            with open(preferences_path, 'r', encoding='utf-8') as f:
                prefs = json.load(f)

            # 移除扩展相关配置
            extensions_to_remove = []
            if 'extensions' in prefs:
                if 'settings' in prefs['extensions']:
                    for ext_id, ext_data in prefs['extensions']['settings'].items():
                        if 'augment' in str(ext_data).lower():
                            extensions_to_remove.append(ext_id)

            for ext_id in extensions_to_remove:
                if ext_id in prefs['extensions']['settings']:
                    del prefs['extensions']['settings'][ext_id]
                    result['cleared_items'].append(f"Preferences: 移除扩展 {ext_id}")

            # 保存修改后的preferences
            with open(preferences_path, 'w', encoding='utf-8') as f:
                json.dump(prefs, f, indent=2)

        except Exception as e:
            result['errors'].append(f"修改Preferences失败: {e}")

    def _clear_firefox_augment_data(self, paths: Dict, result: Dict) -> None:
        """清理Firefox的Augment相关数据"""
        try:
            profiles_dir = paths.get('profiles')
            if not profiles_dir or not os.path.exists(profiles_dir):
                return

            # 遍历所有Firefox配置文件
            for profile_name in os.listdir(profiles_dir):
                profile_path = os.path.join(profiles_dir, profile_name)
                if not os.path.isdir(profile_path):
                    continue

                # 清理places.sqlite (历史记录和书签)
                places_db = os.path.join(profile_path, 'places.sqlite')
                if os.path.exists(places_db):
                    self._clear_firefox_places_augment(places_db, result)

                # 清理cookies.sqlite
                cookies_db = os.path.join(profile_path, 'cookies.sqlite')
                if os.path.exists(cookies_db):
                    self._clear_firefox_cookies_augment(cookies_db, result)

                # 清理webappsstore.sqlite (localStorage)
                webapps_db = os.path.join(profile_path, 'webappsstore.sqlite')
                if os.path.exists(webapps_db):
                    self._clear_firefox_webapps_augment(webapps_db, result)

        except Exception as e:
            result['errors'].append(f"清理Firefox数据失败: {e}")

    def _clear_firefox_places_augment(self, places_db_path: str, result: Dict) -> None:
        """精确清理Firefox places数据库中的Augment相关记录"""
        try:
            conn = sqlite3.connect(places_db_path)
            cursor = conn.cursor()

            deleted_history = 0
            deleted_bookmarks = 0

            # 删除Augment相关的历史记录
            for domain in self._get_augment_domains():
                cursor.execute("DELETE FROM moz_places WHERE url LIKE ?", (f'%{domain}%',))
                deleted_history += cursor.rowcount

            # 删除GitHub Augment相关的历史记录
            for pattern in self._get_github_augment_patterns():
                url_pattern = pattern.replace('_', '/').replace('github.com/', 'github.com/')
                cursor.execute("DELETE FROM moz_places WHERE url LIKE ?", (f'%{url_pattern}%',))
                deleted_history += cursor.rowcount

            # 删除相关书签
            for pattern in self._get_augment_patterns():
                cursor.execute("DELETE FROM moz_bookmarks WHERE title LIKE ?", (f'%{pattern}%',))
                deleted_bookmarks += cursor.rowcount

            conn.commit()
            conn.close()

            if deleted_history > 0 or deleted_bookmarks > 0:
                result['cleared_items'].append(f"Firefox: 清理了 {deleted_history} 条历史记录, {deleted_bookmarks} 个书签")
                self.logger.info(f"Firefox places: 清理了 {deleted_history} 条历史记录, {deleted_bookmarks} 个书签")

        except Exception as e:
            result['errors'].append(f"清理Firefox places失败: {e}")
            self.logger.error(f"清理Firefox places失败: {e}")

    def _clear_firefox_cookies_augment(self, cookies_db_path: str, result: Dict) -> None:
        """精确清理Firefox cookies数据库"""
        try:
            conn = sqlite3.connect(cookies_db_path)
            cursor = conn.cursor()

            deleted_count = 0

            # 删除Augment域名相关的cookies
            for domain in self._get_augment_domains():
                cursor.execute("DELETE FROM moz_cookies WHERE host LIKE ?", (f'%{domain}%',))
                deleted_count += cursor.rowcount
                cursor.execute("DELETE FROM moz_cookies WHERE host LIKE ?", (f'%.{domain}%',))
                deleted_count += cursor.rowcount

            # 删除GitHub Augment相关的cookies
            for pattern in self._get_github_augment_patterns():
                host_pattern = pattern.replace('_', '.').replace('github.com/', 'github.com')
                cursor.execute("DELETE FROM moz_cookies WHERE host LIKE ?", (f'%{host_pattern}%',))
                deleted_count += cursor.rowcount

            conn.commit()
            conn.close()

            if deleted_count > 0:
                result['cleared_items'].append(f"Firefox: 清理了 {deleted_count} 个cookies")
                self.logger.info(f"Firefox cookies: 清理了 {deleted_count} 个cookies")

        except Exception as e:
            result['errors'].append(f"清理Firefox cookies失败: {e}")
            self.logger.error(f"清理Firefox cookies失败: {e}")

    def _clear_firefox_webapps_augment(self, webapps_db_path: str, result: Dict) -> None:
        """精确清理Firefox localStorage数据"""
        try:
            conn = sqlite3.connect(webapps_db_path)
            cursor = conn.cursor()

            deleted_count = 0

            # 删除Augment域名相关的localStorage
            for domain in self._get_augment_domains():
                cursor.execute("DELETE FROM webappsstore2 WHERE originKey LIKE ?", (f'%{domain}%',))
                deleted_count += cursor.rowcount

            # 删除GitHub Augment相关的localStorage
            for pattern in self._get_github_augment_patterns():
                origin_pattern = pattern.replace('_', '.').replace('github.com/', 'github.com')
                cursor.execute("DELETE FROM webappsstore2 WHERE originKey LIKE ?", (f'%{origin_pattern}%',))
                deleted_count += cursor.rowcount

            conn.commit()
            conn.close()

            if deleted_count > 0:
                result['cleared_items'].append(f"Firefox: 清理了 {deleted_count} 个localStorage项目")
                self.logger.info(f"Firefox localStorage: 清理了 {deleted_count} 个项目")

        except Exception as e:
            result['errors'].append(f"清理Firefox localStorage失败: {e}")
            self.logger.error(f"清理Firefox localStorage失败: {e}")

    def get_cleanup_preview(self) -> Dict[str, any]:
        """获取清理预览信息，显示将要清理的内容"""
        preview = {
            'browsers_detected': [],
            'cleanup_items': {},
            'total_estimated_items': 0,
            'warnings': []
        }

        try:
            installed_browsers = self.detect_installed_browsers()
            preview['browsers_detected'] = installed_browsers

            for browser in installed_browsers:
                browser_paths = self.get_browser_paths()
                if browser not in browser_paths:
                    continue

                paths = browser_paths[browser]
                browser_items = {
                    'local_storage': 0,
                    'cookies': 0,
                    'cache_files': 0,
                    'session_storage': 0,
                    'history': 0,
                    'bookmarks': 0
                }

                # 预估Local Storage项目数量
                if 'local_storage' in paths and os.path.exists(paths['local_storage']):
                    browser_items['local_storage'] = self._count_augment_files(
                        paths['local_storage'], 'local_storage'
                    )

                # 预估Cookies数量
                if 'cookies' in paths and os.path.exists(paths['cookies']):
                    browser_items['cookies'] = self._count_augment_cookies(paths['cookies'])

                # 预估缓存文件数量
                if 'cache' in paths and os.path.exists(paths['cache']):
                    browser_items['cache_files'] = self._count_augment_cache_files(paths['cache'])

                # 预估Session Storage项目数量
                if 'session_storage' in paths and os.path.exists(paths['session_storage']):
                    browser_items['session_storage'] = self._count_augment_files(
                        paths['session_storage'], 'session_storage'
                    )

                # Firefox特殊处理
                if browser == 'firefox':
                    firefox_items = self._count_firefox_augment_items(paths)
                    browser_items.update(firefox_items)

                preview['cleanup_items'][browser] = browser_items
                preview['total_estimated_items'] += sum(browser_items.values())

            # 添加警告信息
            if preview['total_estimated_items'] == 0:
                preview['warnings'].append("未检测到Augment相关数据，可能已经清理过或未使用过Augment")

        except Exception as e:
            preview['warnings'].append(f"获取预览信息时出错: {str(e)}")
            self.logger.error(f"获取清理预览失败: {e}")

        return preview

    def _count_augment_files(self, directory_path: str, file_type: str) -> int:
        """计算目录中Augment相关文件的数量"""
        try:
            if not os.path.exists(directory_path):
                return 0

            count = 0
            augment_patterns = self._get_augment_patterns()
            augment_domains = self._get_augment_domains()

            for file_name in os.listdir(directory_path):
                should_count = False

                for pattern in augment_patterns:
                    if pattern.lower() in file_name.lower():
                        should_count = True
                        break

                if not should_count:
                    for domain in augment_domains:
                        domain_pattern = domain.replace('.', '_')
                        if domain_pattern.lower() in file_name.lower():
                            should_count = True
                            break

                if should_count:
                    count += 1

            return count

        except Exception as e:
            self.logger.error(f"计算{file_type}文件数量失败: {e}")
            return 0

    def _count_augment_cookies(self, cookies_path: str) -> int:
        """计算Augment相关cookies的数量"""
        try:
            if not os.path.exists(cookies_path):
                return 0

            conn = sqlite3.connect(cookies_path)
            cursor = conn.cursor()

            total_count = 0

            # 计算Augment域名相关的cookies
            for domain in self._get_augment_domains():
                cursor.execute("SELECT COUNT(*) FROM cookies WHERE host_key LIKE ?", (f'%{domain}%',))
                count = cursor.fetchone()[0]
                total_count += count

            # 计算GitHub相关的cookies
            for pattern in self._get_github_augment_patterns():
                host_pattern = pattern.replace('_', '.')
                cursor.execute("SELECT COUNT(*) FROM cookies WHERE host_key LIKE ?", (f'%{host_pattern}%',))
                count = cursor.fetchone()[0]
                total_count += count

            conn.close()
            return total_count

        except Exception as e:
            self.logger.error(f"计算cookies数量失败: {e}")
            return 0

    def _count_augment_cache_files(self, cache_path: str) -> int:
        """计算Augment相关缓存文件的数量"""
        try:
            if not os.path.exists(cache_path):
                return 0

            count = 0
            augment_patterns = self._get_augment_patterns()
            augment_domains = self._get_augment_domains()

            # 限制搜索深度以提高性能
            max_depth = 3
            current_depth = 0

            for root, dirs, files in os.walk(cache_path):
                current_depth = root.replace(cache_path, '').count(os.sep)
                if current_depth >= max_depth:
                    dirs[:] = []  # 不再深入子目录
                    continue

                for file in files:
                    file_lower = file.lower()
                    path_lower = root.lower()

                    should_count = False

                    for pattern in augment_patterns:
                        if pattern.lower() in file_lower or pattern.lower() in path_lower:
                            should_count = True
                            break

                    if not should_count:
                        for domain in augment_domains:
                            domain_pattern = domain.replace('.', '_')
                            if domain_pattern.lower() in file_lower or domain_pattern.lower() in path_lower:
                                should_count = True
                                break

                    if should_count:
                        count += 1

            return count

        except Exception as e:
            self.logger.error(f"计算缓存文件数量失败: {e}")
            return 0

    def _count_firefox_augment_items(self, paths: Dict) -> Dict[str, int]:
        """计算Firefox中Augment相关项目的数量"""
        firefox_counts = {
            'history': 0,
            'bookmarks': 0,
            'cookies': 0,
            'local_storage': 0
        }

        try:
            profiles_dir = paths.get('profiles')
            if not profiles_dir or not os.path.exists(profiles_dir):
                return firefox_counts

            for profile_name in os.listdir(profiles_dir):
                profile_path = os.path.join(profiles_dir, profile_name)
                if not os.path.isdir(profile_path):
                    continue

                # 计算places.sqlite中的项目
                places_db = os.path.join(profile_path, 'places.sqlite')
                if os.path.exists(places_db):
                    try:
                        conn = sqlite3.connect(places_db)
                        cursor = conn.cursor()

                        # 计算历史记录
                        for domain in self._get_augment_domains():
                            cursor.execute("SELECT COUNT(*) FROM moz_places WHERE url LIKE ?", (f'%{domain}%',))
                            firefox_counts['history'] += cursor.fetchone()[0]

                        # 计算书签
                        for pattern in self._get_augment_patterns():
                            cursor.execute("SELECT COUNT(*) FROM moz_bookmarks WHERE title LIKE ?", (f'%{pattern}%',))
                            firefox_counts['bookmarks'] += cursor.fetchone()[0]

                        conn.close()
                    except:
                        pass

                # 计算cookies.sqlite中的项目
                cookies_db = os.path.join(profile_path, 'cookies.sqlite')
                if os.path.exists(cookies_db):
                    try:
                        conn = sqlite3.connect(cookies_db)
                        cursor = conn.cursor()

                        for domain in self._get_augment_domains():
                            cursor.execute("SELECT COUNT(*) FROM moz_cookies WHERE host LIKE ?", (f'%{domain}%',))
                            firefox_counts['cookies'] += cursor.fetchone()[0]

                        conn.close()
                    except:
                        pass

                # 计算webappsstore.sqlite中的项目
                webapps_db = os.path.join(profile_path, 'webappsstore.sqlite')
                if os.path.exists(webapps_db):
                    try:
                        conn = sqlite3.connect(webapps_db)
                        cursor = conn.cursor()

                        for domain in self._get_augment_domains():
                            cursor.execute("SELECT COUNT(*) FROM webappsstore2 WHERE originKey LIKE ?", (f'%{domain}%',))
                            firefox_counts['local_storage'] += cursor.fetchone()[0]

                        conn.close()
                    except:
                        pass

        except Exception as e:
            self.logger.error(f"计算Firefox项目数量失败: {e}")

        return firefox_counts

    def enhanced_clear_all_browsers(self, backup_dir: str = None, progress_callback=None) -> Dict[str, any]:
        """增强的一键清理所有浏览器Augment数据，支持进度回调"""
        results = {
            'success': True,
            'browsers_processed': [],
            'total_cleared': 0,
            'errors': [],
            'summary': {
                'local_storage': 0,
                'cookies': 0,
                'cache_files': 0,
                'session_storage': 0,
                'history': 0,
                'bookmarks': 0
            },
            'backup_created': False
        }

        try:
            installed_browsers = self.detect_installed_browsers()
            total_browsers = len(installed_browsers)

            if progress_callback:
                progress_callback(0, f"检测到 {total_browsers} 个浏览器")

            for i, browser in enumerate(installed_browsers):
                try:
                    if progress_callback:
                        progress_callback(
                            int((i / total_browsers) * 100),
                            f"正在处理 {browser}..."
                        )

                    # 备份数据
                    if backup_dir:
                        backup_success = self.backup_browser_data(browser, backup_dir)
                        if backup_success:
                            results['backup_created'] = True

                    # 清理数据
                    browser_result = self.clear_browser_augment_data(browser)
                    results['browsers_processed'].append(browser_result)

                    if browser_result['success']:
                        # 统计清理的项目
                        for item in browser_result['cleared_items']:
                            if 'Local Storage' in item:
                                results['summary']['local_storage'] += 1
                            elif 'Cookies' in item:
                                results['summary']['cookies'] += 1
                            elif 'Cache' in item:
                                results['summary']['cache_files'] += 1
                            elif 'Session Storage' in item:
                                results['summary']['session_storage'] += 1
                            elif 'Firefox' in item and '历史记录' in item:
                                results['summary']['history'] += 1
                            elif 'Firefox' in item and '书签' in item:
                                results['summary']['bookmarks'] += 1

                        results['total_cleared'] += len(browser_result['cleared_items'])
                    else:
                        results['success'] = False
                        results['errors'].extend(browser_result['errors'])

                except Exception as e:
                    results['success'] = False
                    results['errors'].append(f"处理 {browser} 时出错: {str(e)}")
                    self.logger.error(f"处理 {browser} 时出错: {e}")

            if progress_callback:
                progress_callback(100, "清理完成")

            # 记录清理结果
            self.logger.info(f"清理完成: 处理了 {len(results['browsers_processed'])} 个浏览器, "
                           f"清理了 {results['total_cleared']} 个项目")

        except Exception as e:
            results['success'] = False
            results['errors'].append(f"清理过程出错: {str(e)}")
            self.logger.error(f"清理过程出错: {e}")

        return results
