#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能检测系统 - 自动发现浏览器和编辑器的实际安装位置
"""

import os
import json
import winreg
import subprocess
from pathlib import Path

class SmartDetector:
    """智能检测器"""
    
    def __init__(self):
        self.detected_paths = {
            'browsers': {},
            'editors': {},
            'augment_data': []
        }
    
    def detect_all(self):
        """检测所有相关软件和数据"""
        print("🔍 开始智能检测系统...")
        
        # 1. 检测浏览器
        self._detect_browsers()
        
        # 2. 检测编辑器
        self._detect_editors()
        
        # 3. 检测Augment数据
        self._detect_augment_data()
        
        return self.detected_paths
    
    def _detect_browsers(self):
        """检测浏览器安装位置"""
        print("🌐 检测浏览器...")
        
        # Chrome检测
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe"),
            os.path.expandvars(r"%PROGRAMFILES%\Google\Chrome\Application\chrome.exe"),
        ]
        
        for path in chrome_paths:
            if os.path.exists(path):
                user_data = self._find_chrome_user_data(path)
                if user_data:
                    self.detected_paths['browsers']['chrome'] = {
                        'exe': path,
                        'user_data': user_data,
                        'profiles': self._find_chrome_profiles(user_data)
                    }
                    print(f"✅ Chrome: {path}")
                    print(f"   数据目录: {user_data}")
                break
        
        # Edge检测
        edge_paths = [
            r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
            r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
            os.path.expandvars(r"%PROGRAMFILES%\Microsoft\Edge\Application\msedge.exe"),
        ]
        
        for path in edge_paths:
            if os.path.exists(path):
                user_data = self._find_edge_user_data()
                if user_data:
                    self.detected_paths['browsers']['edge'] = {
                        'exe': path,
                        'user_data': user_data,
                        'profiles': self._find_edge_profiles(user_data)
                    }
                    print(f"✅ Edge: {path}")
                    print(f"   数据目录: {user_data}")
                break
        
        # Firefox检测
        firefox_paths = [
            r"C:\Program Files\Mozilla Firefox\firefox.exe",
            r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe",
        ]
        
        for path in firefox_paths:
            if os.path.exists(path):
                profiles_dir = self._find_firefox_profiles()
                if profiles_dir:
                    self.detected_paths['browsers']['firefox'] = {
                        'exe': path,
                        'profiles_dir': profiles_dir,
                        'profiles': self._find_firefox_profile_list(profiles_dir)
                    }
                    print(f"✅ Firefox: {path}")
                    print(f"   配置目录: {profiles_dir}")
                break
    
    def _detect_editors(self):
        """检测编辑器安装位置"""
        print("💻 检测编辑器...")
        
        # VSCode检测
        vscode_paths = [
            r"C:\Program Files\Microsoft VS Code\Code.exe",
            r"C:\Program Files (x86)\Microsoft VS Code\Code.exe",
            os.path.expandvars(r"%LOCALAPPDATA%\Programs\Microsoft VS Code\Code.exe"),
            os.path.expandvars(r"%PROGRAMFILES%\Microsoft VS Code\Code.exe"),
        ]
        
        for path in vscode_paths:
            if os.path.exists(path):
                user_data = self._find_vscode_user_data()
                if user_data:
                    self.detected_paths['editors']['vscode'] = {
                        'exe': path,
                        'user_data': user_data,
                        'extensions': os.path.join(user_data, 'extensions'),
                        'global_storage': os.path.join(user_data, 'User', 'globalStorage')
                    }
                    print(f"✅ VSCode: {path}")
                    print(f"   数据目录: {user_data}")
                break
        
        # Cursor检测
        cursor_paths = [
            os.path.expandvars(r"%LOCALAPPDATA%\Programs\cursor\Cursor.exe"),
            r"C:\Program Files\Cursor\Cursor.exe",
            r"C:\Program Files (x86)\Cursor\Cursor.exe",
        ]
        
        for path in cursor_paths:
            if os.path.exists(path):
                user_data = self._find_cursor_user_data()
                if user_data:
                    self.detected_paths['editors']['cursor'] = {
                        'exe': path,
                        'user_data': user_data,
                        'global_storage': os.path.join(user_data, 'User', 'globalStorage')
                    }
                    print(f"✅ Cursor: {path}")
                    print(f"   数据目录: {user_data}")
                break
    
    def _detect_augment_data(self):
        """检测Augment相关数据"""
        print("🎯 检测Augment数据...")
        
        augment_data = []
        
        # 检测浏览器中的Augment数据
        for browser, info in self.detected_paths['browsers'].items():
            if browser == 'chrome' or browser == 'edge':
                for profile_name, profile_path in info.get('profiles', {}).items():
                    # 检查Cookies
                    cookies_db = os.path.join(profile_path, 'Cookies')
                    if os.path.exists(cookies_db):
                        if self._check_cookies_for_augment(cookies_db):
                            augment_data.append({
                                'type': 'cookies',
                                'browser': browser,
                                'profile': profile_name,
                                'path': cookies_db
                            })
                    
                    # 检查Local Storage
                    ls_path = os.path.join(profile_path, 'Local Storage', 'leveldb')
                    if os.path.exists(ls_path):
                        if self._check_leveldb_for_augment(ls_path):
                            augment_data.append({
                                'type': 'local_storage',
                                'browser': browser,
                                'profile': profile_name,
                                'path': ls_path
                            })
                    
                    # 检查IndexedDB
                    idb_path = os.path.join(profile_path, 'IndexedDB')
                    if os.path.exists(idb_path):
                        augment_dbs = self._check_indexeddb_for_augment(idb_path)
                        for db in augment_dbs:
                            augment_data.append({
                                'type': 'indexeddb',
                                'browser': browser,
                                'profile': profile_name,
                                'path': db
                            })
            
            elif browser == 'firefox':
                for profile_name, profile_path in info.get('profiles', {}).items():
                    # 检查Firefox cookies
                    cookies_db = os.path.join(profile_path, 'cookies.sqlite')
                    if os.path.exists(cookies_db):
                        if self._check_cookies_for_augment(cookies_db, is_firefox=True):
                            augment_data.append({
                                'type': 'firefox_cookies',
                                'browser': 'firefox',
                                'profile': profile_name,
                                'path': cookies_db
                            })
        
        # 检测编辑器中的Augment数据
        for editor, info in self.detected_paths['editors'].items():
            if 'global_storage' in info:
                storage_json = os.path.join(info['global_storage'], 'storage.json')
                if os.path.exists(storage_json):
                    if self._check_storage_json_for_augment(storage_json):
                        augment_data.append({
                            'type': 'editor_storage',
                            'editor': editor,
                            'path': storage_json
                        })
                
                # 检查扩展数据
                for item in os.listdir(info['global_storage']):
                    if 'augment' in item.lower():
                        augment_data.append({
                            'type': 'extension_data',
                            'editor': editor,
                            'path': os.path.join(info['global_storage'], item)
                        })
        
        self.detected_paths['augment_data'] = augment_data
        
        if augment_data:
            print(f"🎯 发现 {len(augment_data)} 个Augment数据位置:")
            for data in augment_data:
                print(f"   - {data['type']}: {data['path']}")
        else:
            print("ℹ️ 未发现Augment相关数据")
    
    def _find_chrome_user_data(self, chrome_exe):
        """查找Chrome用户数据目录"""
        possible_paths = [
            os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data"),
            os.path.expandvars(r"%APPDATA%\Google\Chrome\User Data"),
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        return None
    
    def _find_chrome_profiles(self, user_data_dir):
        """查找Chrome配置文件"""
        profiles = {}
        if not os.path.exists(user_data_dir):
            return profiles
        
        # 默认配置文件
        default_path = os.path.join(user_data_dir, 'Default')
        if os.path.exists(default_path):
            profiles['Default'] = default_path
        
        # 其他配置文件
        for item in os.listdir(user_data_dir):
            if item.startswith('Profile '):
                profile_path = os.path.join(user_data_dir, item)
                if os.path.isdir(profile_path):
                    profiles[item] = profile_path
        
        return profiles
    
    def _find_edge_user_data(self):
        """查找Edge用户数据目录"""
        possible_paths = [
            os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data"),
            os.path.expandvars(r"%APPDATA%\Microsoft\Edge\User Data"),
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        return None
    
    def _find_edge_profiles(self, user_data_dir):
        """查找Edge配置文件"""
        return self._find_chrome_profiles(user_data_dir)  # Edge使用相同的结构
    
    def _find_firefox_profiles(self):
        """查找Firefox配置文件目录"""
        possible_paths = [
            os.path.expandvars(r"%APPDATA%\Mozilla\Firefox\Profiles"),
            os.path.expandvars(r"%LOCALAPPDATA%\Mozilla\Firefox\Profiles"),
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        return None
    
    def _find_firefox_profile_list(self, profiles_dir):
        """获取Firefox配置文件列表"""
        profiles = {}
        if not os.path.exists(profiles_dir):
            return profiles
        
        for item in os.listdir(profiles_dir):
            profile_path = os.path.join(profiles_dir, item)
            if os.path.isdir(profile_path):
                profiles[item] = profile_path
        
        return profiles
    
    def _find_vscode_user_data(self):
        """查找VSCode用户数据目录"""
        possible_paths = [
            os.path.expandvars(r"%APPDATA%\Code"),
            os.path.expandvars(r"%LOCALAPPDATA%\Programs\Microsoft VS Code\User"),
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        return None
    
    def _find_cursor_user_data(self):
        """查找Cursor用户数据目录"""
        possible_paths = [
            os.path.expandvars(r"%APPDATA%\Cursor"),
            os.path.expandvars(r"%LOCALAPPDATA%\Programs\cursor\User"),
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        return None
    
    def _check_cookies_for_augment(self, db_path, is_firefox=False):
        """检查Cookies数据库是否包含Augment数据"""
        try:
            import sqlite3
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            if is_firefox:
                cursor.execute("SELECT COUNT(*) FROM moz_cookies WHERE host LIKE '%augment%'")
            else:
                cursor.execute("SELECT COUNT(*) FROM cookies WHERE host_key LIKE '%augment%'")
            
            count = cursor.fetchone()[0]
            conn.close()
            return count > 0
        except:
            return False
    
    def _check_leveldb_for_augment(self, leveldb_path):
        """检查LevelDB是否包含Augment数据"""
        try:
            for file in os.listdir(leveldb_path):
                if file.endswith(('.log', '.ldb')):
                    file_path = os.path.join(leveldb_path, file)
                    try:
                        with open(file_path, 'rb') as f:
                            content = f.read()
                        if b'augment' in content.lower():
                            return True
                    except:
                        continue
            return False
        except:
            return False
    
    def _check_indexeddb_for_augment(self, idb_path):
        """检查IndexedDB是否包含Augment数据"""
        augment_dbs = []
        try:
            for item in os.listdir(idb_path):
                if 'augment' in item.lower():
                    augment_dbs.append(os.path.join(idb_path, item))
            return augment_dbs
        except:
            return []
    
    def _check_storage_json_for_augment(self, storage_path):
        """检查storage.json是否包含Augment数据"""
        try:
            with open(storage_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return 'augment' in content.lower()
        except:
            return False

def main():
    """主函数"""
    detector = SmartDetector()
    results = detector.detect_all()
    
    print("\n" + "="*60)
    print("📊 检测结果汇总")
    print("="*60)
    
    print(f"\n🌐 浏览器: {len(results['browsers'])} 个")
    for browser, info in results['browsers'].items():
        print(f"  ✅ {browser.upper()}: {info['exe']}")
    
    print(f"\n💻 编辑器: {len(results['editors'])} 个")
    for editor, info in results['editors'].items():
        print(f"  ✅ {editor.upper()}: {info['exe']}")
    
    print(f"\n🎯 Augment数据: {len(results['augment_data'])} 个位置")
    for data in results['augment_data']:
        print(f"  📍 {data['type']}: {data['path']}")
    
    return results

if __name__ == "__main__":
    main()
