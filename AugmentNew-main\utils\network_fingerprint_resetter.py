"""
网络指纹重置器
基于最新网络研究，重置网络相关的设备指纹
解决基于网络特征的设备识别问题
"""

import os
import sys
import json
import uuid
import time
import subprocess
import winreg
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

class NetworkFingerprintResetter:
    """网络指纹重置器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 网络指纹组件
        self.network_components = {
            'mac_addresses': {
                'description': 'MAC地址重置',
                'adapters': ['Ethernet', 'Wi-Fi', 'Bluetooth']
            },
            'dns_cache': {
                'description': 'DNS缓存清理',
                'commands': [
                    ['ipconfig', '/flushdns'],
                    ['ipconfig', '/registerdns']
                ]
            },
            'network_profiles': {
                'description': '网络配置文件重置',
                'paths': [
                    r"%PROGRAMDATA%\Microsoft\Wlansvc\Profiles",
                    r"%LOCALAPPDATA%\Microsoft\Windows\INetCache"
                ]
            },
            'proxy_settings': {
                'description': '代理设置重置',
                'registry_keys': [
                    r"SOFTWARE\Microsoft\Windows\CurrentVersion\Internet Settings"
                ]
            }
        }
    
    def reset_network_fingerprint(self) -> Dict[str, Any]:
        """重置网络指纹"""
        results = {
            'success': True,
            'reset_components': [],
            'errors': []
        }
        
        try:
            self.logger.info("开始重置网络指纹...")
            
            # 1. 重置网络适配器
            adapter_result = self._reset_network_adapters()
            if adapter_result['success']:
                results['reset_components'].extend(adapter_result['reset_items'])
            else:
                results['errors'].extend(adapter_result['errors'])
            
            # 2. 清理DNS缓存
            dns_result = self._clear_dns_cache()
            if dns_result['success']:
                results['reset_components'].extend(dns_result['reset_items'])
            else:
                results['errors'].extend(dns_result['errors'])
            
            # 3. 重置网络配置文件
            profile_result = self._reset_network_profiles()
            if profile_result['success']:
                results['reset_components'].extend(profile_result['reset_items'])
            else:
                results['errors'].extend(profile_result['errors'])
            
            # 4. 重置代理设置
            proxy_result = self._reset_proxy_settings()
            if proxy_result['success']:
                results['reset_components'].extend(proxy_result['reset_items'])
            else:
                results['errors'].extend(proxy_result['errors'])
            
            if results['errors']:
                results['success'] = False
            
            self.logger.info(f"网络指纹重置完成: 处理了 {len(results['reset_components'])} 个组件")
            
        except Exception as e:
            results['success'] = False
            results['errors'].append(f"网络指纹重置失败: {str(e)}")
            self.logger.error(f"网络指纹重置失败: {e}")
        
        return results
    
    def _reset_network_adapters(self) -> Dict[str, Any]:
        """重置网络适配器"""
        result = {'success': True, 'reset_items': [], 'errors': []}
        
        try:
            # 重置网络适配器
            reset_commands = [
                ['netsh', 'winsock', 'reset'],
                ['netsh', 'int', 'ip', 'reset'],
                ['netsh', 'int', 'ipv4', 'reset'],
                ['netsh', 'int', 'ipv6', 'reset']
            ]
            
            for cmd in reset_commands:
                try:
                    process = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                    if process.returncode == 0:
                        result['reset_items'].append(f"网络命令: {' '.join(cmd)}")
                    else:
                        result['errors'].append(f"网络命令失败: {' '.join(cmd)}")
                except Exception as e:
                    result['errors'].append(f"执行网络命令失败 {' '.join(cmd)}: {str(e)}")
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置网络适配器失败: {str(e)}")
        
        return result
    
    def _clear_dns_cache(self) -> Dict[str, Any]:
        """清理DNS缓存"""
        result = {'success': True, 'reset_items': [], 'errors': []}
        
        try:
            dns_commands = [
                ['ipconfig', '/flushdns'],
                ['ipconfig', '/registerdns'],
                ['ipconfig', '/release'],
                ['ipconfig', '/renew']
            ]
            
            for cmd in dns_commands:
                try:
                    process = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                    if process.returncode == 0:
                        result['reset_items'].append(f"DNS命令: {' '.join(cmd)}")
                    else:
                        result['errors'].append(f"DNS命令失败: {' '.join(cmd)}")
                except Exception as e:
                    result['errors'].append(f"执行DNS命令失败 {' '.join(cmd)}: {str(e)}")
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"清理DNS缓存失败: {str(e)}")
        
        return result
    
    def _reset_network_profiles(self) -> Dict[str, Any]:
        """重置网络配置文件"""
        result = {'success': True, 'reset_items': [], 'errors': []}
        
        try:
            profile_paths = [
                os.path.expandvars(r"%PROGRAMDATA%\Microsoft\Wlansvc\Profiles"),
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Windows\INetCache"),
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Windows\INetCookies")
            ]
            
            for profile_path in profile_paths:
                if os.path.exists(profile_path):
                    try:
                        import shutil
                        shutil.rmtree(profile_path)
                        result['reset_items'].append(f"网络配置文件: {os.path.basename(profile_path)}")
                    except Exception as e:
                        result['errors'].append(f"删除网络配置文件失败 {profile_path}: {str(e)}")
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置网络配置文件失败: {str(e)}")
        
        return result
    
    def _reset_proxy_settings(self) -> Dict[str, Any]:
        """重置代理设置"""
        result = {'success': True, 'reset_items': [], 'errors': []}
        
        try:
            # 重置Internet设置
            try:
                key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                                   r"SOFTWARE\Microsoft\Windows\CurrentVersion\Internet Settings",
                                   0, winreg.KEY_SET_VALUE)
                
                # 禁用代理
                winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 0)
                winreg.SetValueEx(key, "ProxyServer", 0, winreg.REG_SZ, "")
                winreg.SetValueEx(key, "ProxyOverride", 0, winreg.REG_SZ, "")
                
                winreg.CloseKey(key)
                result['reset_items'].append("代理设置已重置")
                
            except Exception as e:
                result['errors'].append(f"重置代理设置失败: {str(e)}")
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置代理设置失败: {str(e)}")
        
        return result
