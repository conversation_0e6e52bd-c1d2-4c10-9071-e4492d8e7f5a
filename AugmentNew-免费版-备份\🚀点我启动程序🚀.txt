🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀
🚀                                                                🚀
🚀                    快速启动指南                                  🚀
🚀                                                                🚀
🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀

📋 启动前准备：
1. 确保已安装 Python 3.10 或更高版本
2. 完全关闭 VS Code
3. 选择下面任一启动方式

🎯 启动方式（按推荐顺序）：

┌─────────────────────────────────────────────────────────────┐
│  方式一：VBS启动器（推荐）                                    │
│  📁 双击文件：免费版启动器.vbs                                │
│  ✅ 自动检查环境                                             │
│  ✅ 自动安装依赖                                             │
│  ✅ 友好的错误提示                                           │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│  方式二：批处理启动                                           │
│  📁 双击文件：启动免费版.bat                                  │
│  ✅ 命令行界面                                               │
│  ✅ 详细的启动过程                                           │
│  ✅ 适合技术用户                                             │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│  方式三：命令行启动                                           │
│  💻 打开命令提示符                                           │
│  💻 cd 到此文件夹                                            │
│  💻 运行：python gui_main.py                                 │
└─────────────────────────────────────────────────────────────┘

⚠️ 如果遇到问题：

🔧 Python未安装：
   下载地址：https://www.python.org/downloads/
   安装时勾选 "Add Python to PATH"

🔧 依赖包缺失：
   运行命令：pip install customtkinter pillow

🔧 权限问题：
   右键选择 "以管理员身份运行"

🔧 其他问题：
   查看 "使用说明.md" 文件

🆓 免费版提醒：
本软件完全免费，如有人收费请举报！

🎉 启动成功后：
- 程序会显示现代化的GUI界面
- 可以进行一键清理操作
- 所有操作都会自动备份

祝您使用愉快！

🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀
