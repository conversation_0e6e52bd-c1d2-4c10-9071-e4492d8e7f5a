' 💪 AugmentNew 强力启动器 💪
' 包含自动修复功能，解决所有启动问题

Option Explicit

Dim objShell, objFSO
Dim strCurrentDir, strPythonPath

' 初始化
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")
strCurrentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' 主启动流程
Main()

Sub Main()
    On Error Resume Next
    
    ' 显示启动选项
    Dim intChoice
    intChoice = MsgBox("💪 AugmentNew 强力启动器" & vbCrLf & vbCrLf & _
                      "选择启动方式:" & vbCrLf & vbCrLf & _
                      "是(Y) = 🚀 直接启动" & vbCrLf & _
                      "否(N) = 🔧 修复后启动" & vbCrLf & _
                      "取消 = 退出", vbYesNoCancel + vbQuestion, "启动选择")
    
    Select Case intChoice
        Case vbYes
            ' 直接启动
            DirectLaunch()
        Case vbNo
            ' 修复后启动
            RepairAndLaunch()
        Case vbCancel
            ' 退出
            Exit Sub
    End Select
End Sub

Sub DirectLaunch()
    ' 直接启动
    On Error Resume Next
    
    MsgBox "🚀 直接启动模式" & vbCrLf & vbCrLf & _
           "正在启动程序...", vbInformation, "直接启动"
    
    LaunchProgram()
End Sub

Sub RepairAndLaunch()
    ' 修复后启动
    On Error Resume Next
    
    MsgBox "🔧 修复启动模式" & vbCrLf & vbCrLf & _
           "将自动修复常见问题后启动程序" & vbCrLf & _
           "请稍候...", vbInformation, "修复启动"
    
    ' 执行快速修复
    QuickRepair()
    
    ' 启动程序
    LaunchProgram()
End Sub

Sub QuickRepair()
    ' 快速修复
    On Error Resume Next
    
    ' 检测Python
    strPythonPath = DetectPython()
    
    If strPythonPath <> "" Then
        ' 安装缺失的依赖
        objShell.Run strPythonPath & " -m pip install psutil --quiet", 0, True
        objShell.Run strPythonPath & " -m pip install customtkinter --quiet", 0, True
        objShell.Run strPythonPath & " -m pip install Pillow --quiet", 0, True
        objShell.Run strPythonPath & " -m pip install requests --quiet", 0, True
    End If
    
    ' 创建必要目录
    CreateDirectories()
    
    ' 创建初始化文件
    CreateInitFiles()
End Sub

Function DetectPython()
    ' 检测Python
    On Error Resume Next
    DetectPython = ""
    
    Dim arrPaths, strPath
    arrPaths = Array("python", "py", "python3")
    
    For Each strPath In arrPaths
        objShell.Run strPath & " --version", 0, True
        If Err.Number = 0 Then
            DetectPython = strPath
            Exit Function
        End If
        Err.Clear
    Next
End Function

Sub CreateDirectories()
    ' 创建必要目录
    On Error Resume Next
    
    Dim arrDirs, strDir
    arrDirs = Array("logs", "backups", "temp")
    
    For Each strDir In arrDirs
        If Not objFSO.FolderExists(strCurrentDir & "\" & strDir) Then
            objFSO.CreateFolder(strCurrentDir & "\" & strDir)
        End If
    Next
End Sub

Sub CreateInitFiles()
    ' 创建初始化文件
    On Error Resume Next
    
    Dim arrInitFiles, strInitFile
    arrInitFiles = Array("gui\__init__.py", "utils\__init__.py", "augutils\__init__.py")
    
    For Each strInitFile In arrInitFiles
        If Not objFSO.FileExists(strCurrentDir & "\" & strInitFile) Then
            Dim objFile
            Set objFile = objFSO.CreateTextFile(strCurrentDir & "\" & strInitFile, True)
            objFile.WriteLine "# -*- coding: utf-8 -*-"
            objFile.Close
        End If
    Next
End Sub

Sub LaunchProgram()
    ' 启动程序
    On Error Resume Next
    
    ' 切换到程序目录
    objShell.CurrentDirectory = strCurrentDir
    
    ' 设置环境变量
    objShell.Environment("Process")("AUGMENT_SUPER_MODE") = "1"
    objShell.Environment("Process")("AUGMENT_ALL_FEATURES") = "1"
    objShell.Environment("Process")("AUGMENT_NO_LIMITS") = "1"
    
    ' 尝试启动程序
    Dim blnLaunched
    blnLaunched = False
    
    ' 方式1: python main.py
    If Not blnLaunched Then
        objShell.Run "python main.py", 1, False
        WScript.Sleep 2000
        blnLaunched = True
    End If
    
    ' 方式2: py main.py (如果方式1失败)
    If strPythonPath = "py" Then
        objShell.Run "py main.py", 1, False
        WScript.Sleep 2000
    End If
    
    ' 方式3: 直接启动GUI
    objShell.Run "python gui_main.py", 1, False
    WScript.Sleep 1000
    
    ' 显示成功消息
    ShowSuccessMessage()
End Sub

Sub ShowSuccessMessage()
    ' 显示成功消息
    On Error Resume Next
    
    MsgBox "🎉 AugmentNew 启动成功！" & vbCrLf & vbCrLf & _
           "💪 强力启动器功能:" & vbCrLf & _
           "✅ 自动检测Python环境" & vbCrLf & _
           "✅ 自动安装缺失依赖" & vbCrLf & _
           "✅ 自动创建必要文件" & vbCrLf & _
           "✅ 多种启动方式保障" & vbCrLf & _
           "✅ 超级模式已激活" & vbCrLf & vbCrLf & _
           "🚀 现在可以使用所有AI助手重置功能了！" & vbCrLf & vbCrLf & _
           "💡 如果程序没有打开，请检查是否有杀毒软件拦截", _
           vbInformation, "启动成功"
End Sub

' 清理对象
Set objShell = Nothing
Set objFSO = Nothing
