"""
Cursor AI 专用重置器
基于GitHub项目 yuaotian/go-cursor-help 和最新网络研究
解决 "Too many free trial accounts used on this machine" 问题
"""

import os
import sys
import json
import uuid
import time
import shutil
import hashlib
import secrets
import subprocess
import winreg
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

class CursorAIResetter:
    """Cursor AI 专用重置器 - 基于最新网络研究"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Cursor AI 相关路径
        self.cursor_paths = {
            'user_data': os.path.expandvars(r"%APPDATA%\Cursor\User Data"),
            'global_storage': os.path.expandvars(r"%APPDATA%\Cursor\User\globalStorage"),
            'machine_id': os.path.expandvars(r"%APPDATA%\Cursor\machineid"),
            'storage_json': os.path.expandvars(r"%APPDATA%\Cursor\User\globalStorage\storage.json"),
            'preferences': os.path.expandvars(r"%LOCALAPPDATA%\Cursor\User Data\Default\Preferences"),
            'local_state': os.path.expandvars(r"%LOCALAPPDATA%\Cursor\User Data\Local State"),
            'logs': os.path.expandvars(r"%APPDATA%\Cursor\logs"),
            'cached_data': os.path.expandvars(r"%APPDATA%\Cursor\CachedData"),
            'extensions': os.path.expandvars(r"%APPDATA%\Cursor\extensions"),
            'workspace_storage': os.path.expandvars(r"%APPDATA%\Cursor\User\workspaceStorage")
        }
        
        # 基于网络研究的Cursor检测机制
        self.cursor_detection_methods = {
            'machine_fingerprint': [
                'machine_id',
                'device_uuid', 
                'hardware_hash',
                'network_adapter_mac',
                'disk_serial_number',
                'cpu_id',
                'motherboard_serial'
            ],
            'usage_tracking': [
                'trial_start_date',
                'usage_count',
                'request_count',
                'session_duration',
                'feature_usage_stats'
            ],
            'network_fingerprint': [
                'ip_address',
                'user_agent',
                'request_headers',
                'connection_timing',
                'dns_resolution'
            ],
            'behavioral_patterns': [
                'typing_patterns',
                'request_frequency',
                'usage_time_patterns',
                'feature_access_patterns'
            ]
        }
        
        # 基于 yuaotian/go-cursor-help 项目的重置策略
        self.reset_strategies = {
            'machine_id_reset': {
                'description': '重置机器标识符',
                'files': [
                    'machineid',
                    'storage.json',
                    'Local State',
                    'Preferences'
                ],
                'registry_keys': [
                    r"SOFTWARE\Cursor",
                    r"SOFTWARE\Classes\cursor"
                ]
            },
            'device_fingerprint_reset': {
                'description': '重置设备指纹',
                'methods': [
                    'hardware_info_modification',
                    'network_adapter_reset',
                    'system_uuid_change'
                ]
            },
            'usage_data_cleanup': {
                'description': '清理使用数据',
                'targets': [
                    'trial_status',
                    'usage_statistics',
                    'session_data',
                    'cache_data'
                ]
            },
            'anti_detection': {
                'description': '反检测措施',
                'techniques': [
                    'request_pattern_randomization',
                    'timing_variation',
                    'user_agent_rotation'
                ]
            }
        }
    
    def reset_cursor_trial(self, backup_dir: str = None) -> Dict[str, Any]:
        """重置Cursor AI试用状态"""
        results = {
            'success': True,
            'reset_components': [],
            'errors': [],
            'techniques_applied': [],
            'backup_created': False
        }
        
        try:
            self.logger.info("开始重置Cursor AI试用状态...")
            
            # 1. 创建备份
            if backup_dir:
                backup_result = self._create_cursor_backup(backup_dir)
                results['backup_created'] = backup_result['success']
                if not backup_result['success']:
                    results['errors'].extend(backup_result['errors'])
            
            # 2. 停止Cursor进程
            stop_result = self._stop_cursor_processes()
            if stop_result['success']:
                results['techniques_applied'].append("Cursor进程已停止")
            else:
                results['errors'].extend(stop_result['errors'])
            
            # 3. 重置机器ID
            machine_id_result = self._reset_cursor_machine_id()
            if machine_id_result['success']:
                results['reset_components'].extend(machine_id_result['reset_items'])
                results['techniques_applied'].append("机器ID重置")
            else:
                results['errors'].extend(machine_id_result['errors'])
            
            # 4. 清理存储数据
            storage_result = self._clean_cursor_storage()
            if storage_result['success']:
                results['reset_components'].extend(storage_result['reset_items'])
                results['techniques_applied'].append("存储数据清理")
            else:
                results['errors'].extend(storage_result['errors'])
            
            # 5. 重置设备指纹
            fingerprint_result = self._reset_cursor_device_fingerprint()
            if fingerprint_result['success']:
                results['reset_components'].extend(fingerprint_result['reset_items'])
                results['techniques_applied'].append("设备指纹重置")
            else:
                results['errors'].extend(fingerprint_result['errors'])
            
            # 6. 清理缓存和日志
            cache_result = self._clean_cursor_cache()
            if cache_result['success']:
                results['reset_components'].extend(cache_result['reset_items'])
                results['techniques_applied'].append("缓存和日志清理")
            else:
                results['errors'].extend(cache_result['errors'])
            
            # 7. 应用反检测措施
            anti_detection_result = self._apply_cursor_anti_detection()
            if anti_detection_result['success']:
                results['techniques_applied'].extend(anti_detection_result['techniques'])
            else:
                results['errors'].extend(anti_detection_result['errors'])
            
            # 8. 清理注册表
            registry_result = self._clean_cursor_registry()
            if registry_result['success']:
                results['reset_components'].extend(registry_result['reset_items'])
                results['techniques_applied'].append("注册表清理")
            else:
                results['errors'].extend(registry_result['errors'])
            
            if results['errors']:
                results['success'] = False
            
            self.logger.info(f"Cursor AI重置完成: 处理了 {len(results['reset_components'])} 个组件")
            
        except Exception as e:
            results['success'] = False
            results['errors'].append(f"Cursor AI重置失败: {str(e)}")
            self.logger.error(f"Cursor AI重置失败: {e}")
        
        return results
    
    def _stop_cursor_processes(self) -> Dict[str, Any]:
        """停止Cursor进程"""
        result = {'success': True, 'errors': []}
        
        try:
            # 停止Cursor相关进程
            cursor_processes = ['Cursor.exe', 'cursor.exe', 'Cursor Helper.exe']
            
            for process_name in cursor_processes:
                try:
                    subprocess.run(['taskkill', '/F', '/IM', process_name], 
                                 capture_output=True, check=False)
                except Exception as e:
                    result['errors'].append(f"停止进程失败 {process_name}: {str(e)}")
            
            # 等待进程完全停止
            time.sleep(2)
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"停止Cursor进程失败: {str(e)}")
        
        return result
    
    def _reset_cursor_machine_id(self) -> Dict[str, Any]:
        """重置Cursor机器ID"""
        result = {'success': True, 'reset_items': [], 'errors': []}
        
        try:
            # 1. 重置machineid文件
            machine_id_path = self.cursor_paths['machine_id']
            if os.path.exists(machine_id_path):
                try:
                    new_machine_id = secrets.token_hex(32)
                    with open(machine_id_path, 'w', encoding='utf-8') as f:
                        f.write(new_machine_id)
                    result['reset_items'].append(f"机器ID文件: {new_machine_id[:8]}...")
                except Exception as e:
                    result['errors'].append(f"重置机器ID文件失败: {str(e)}")
            
            # 2. 更新Local State文件
            local_state_path = self.cursor_paths['local_state']
            if os.path.exists(local_state_path):
                try:
                    with open(local_state_path, 'r', encoding='utf-8') as f:
                        local_state = json.load(f)
                    
                    # 更新机器相关的标识符
                    if 'machine_id' in local_state:
                        local_state['machine_id'] = secrets.token_hex(32)
                    if 'device_id' in local_state:
                        local_state['device_id'] = str(uuid.uuid4())
                    if 'installation_id' in local_state:
                        local_state['installation_id'] = str(uuid.uuid4())
                    
                    with open(local_state_path, 'w', encoding='utf-8') as f:
                        json.dump(local_state, f, indent=2, ensure_ascii=False)
                    
                    result['reset_items'].append("Local State文件已更新")
                    
                except Exception as e:
                    result['errors'].append(f"更新Local State失败: {str(e)}")
            
            # 3. 更新Preferences文件
            preferences_path = self.cursor_paths['preferences']
            if os.path.exists(preferences_path):
                try:
                    with open(preferences_path, 'r', encoding='utf-8') as f:
                        preferences = json.load(f)
                    
                    # 清理试用相关的首选项
                    trial_keys = [
                        'cursor.trial',
                        'cursor.usage',
                        'cursor.device',
                        'cursor.machine',
                        'cursor.fingerprint'
                    ]
                    
                    for key in trial_keys:
                        if key in preferences:
                            del preferences[key]
                    
                    with open(preferences_path, 'w', encoding='utf-8') as f:
                        json.dump(preferences, f, indent=2, ensure_ascii=False)
                    
                    result['reset_items'].append("Preferences文件已清理")
                    
                except Exception as e:
                    result['errors'].append(f"更新Preferences失败: {str(e)}")
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置Cursor机器ID失败: {str(e)}")
        
        return result
    
    def _clean_cursor_storage(self) -> Dict[str, Any]:
        """清理Cursor存储数据"""
        result = {'success': True, 'reset_items': [], 'errors': []}
        
        try:
            # 1. 清理storage.json
            storage_json_path = self.cursor_paths['storage_json']
            if os.path.exists(storage_json_path):
                try:
                    with open(storage_json_path, 'r', encoding='utf-8') as f:
                        storage_data = json.load(f)
                    
                    # 删除试用相关的键
                    trial_keys = [
                        'cursor.trial.status',
                        'cursor.trial.expiry',
                        'cursor.usage.count',
                        'cursor.device.fingerprint',
                        'cursor.machine.id',
                        'cursor.session.token',
                        'cursor.auth.token'
                    ]
                    
                    removed_keys = 0
                    for key in trial_keys:
                        if key in storage_data:
                            del storage_data[key]
                            removed_keys += 1
                    
                    # 也删除包含cursor的其他键
                    keys_to_remove = []
                    for existing_key in storage_data.keys():
                        if 'cursor' in existing_key.lower():
                            keys_to_remove.append(existing_key)
                    
                    for key in keys_to_remove:
                        del storage_data[key]
                        removed_keys += 1
                    
                    if removed_keys > 0:
                        with open(storage_json_path, 'w', encoding='utf-8') as f:
                            json.dump(storage_data, f, indent=2, ensure_ascii=False)
                        result['reset_items'].append(f"存储数据: 移除了{removed_keys}个键")
                    
                except Exception as e:
                    result['errors'].append(f"清理storage.json失败: {str(e)}")
            
            # 2. 清理全局存储目录
            global_storage_path = self.cursor_paths['global_storage']
            if os.path.exists(global_storage_path):
                try:
                    for item in os.listdir(global_storage_path):
                        if 'cursor' in item.lower():
                            item_path = os.path.join(global_storage_path, item)
                            try:
                                if os.path.isfile(item_path):
                                    os.remove(item_path)
                                else:
                                    shutil.rmtree(item_path)
                                result['reset_items'].append(f"全局存储: {item}")
                            except Exception as e:
                                result['errors'].append(f"删除全局存储项失败 {item}: {str(e)}")
                except Exception as e:
                    result['errors'].append(f"清理全局存储目录失败: {str(e)}")
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"清理Cursor存储数据失败: {str(e)}")
        
        return result
