@echo off
chcp 65001 >nul
title 🔥 一键解决 rustc_driver DLL 问题 🔥

echo.
echo 🔥 一键解决 rustc_driver DLL 问题 🔥
echo ==========================================
echo.
echo 正在解决 rustc_driver-e331959a3b2e028f.dll 缺失问题...
echo.

:: 设置管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ⚠️  需要管理员权限，正在重新启动...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

echo ✅ 管理员权限已获取
echo.

:: 1. 清理Rust相关缓存
echo 🧹 步骤1: 清理Rust相关缓存...
if exist "%USERPROFILE%\.cargo" (
    echo    清理 .cargo 目录...
    rmdir /s /q "%USERPROFILE%\.cargo" 2>nul
)

if exist "%USERPROFILE%\.rustup" (
    echo    清理 .rustup 目录...
    rmdir /s /q "%USERPROFILE%\.rustup" 2>nul
)

:: 清理临时文件中的rust相关文件
echo    清理临时文件...
del /f /s /q "%TEMP%\*rust*" 2>nul
del /f /s /q "%TMP%\*rust*" 2>nul
del /f /s /q "C:\Windows\Temp\*rust*" 2>nul

echo ✅ Rust缓存清理完成
echo.

:: 2. 清理Python缓存
echo 📦 步骤2: 清理Python缓存...
python -m pip cache purge 2>nul
py -m pip cache purge 2>nul

:: 清理Python临时文件
del /f /s /q "%TEMP%\*python*" 2>nul
del /f /s /q "%TEMP%\*.pyc" 2>nul
for /d /r "%TEMP%" %%d in (__pycache__) do @if exist "%%d" rmdir /s /q "%%d" 2>nul

echo ✅ Python缓存清理完成
echo.

:: 3. 清理环境变量
echo 🔄 步骤3: 清理环境变量...
setx CARGO_HOME "" >nul 2>&1
setx RUSTUP_HOME "" >nul 2>&1
setx RUST_BACKTRACE "" >nul 2>&1
setx RUSTC_WRAPPER "" >nul 2>&1

echo ✅ 环境变量清理完成
echo.

:: 4. 重新安装Python依赖
echo 📦 步骤4: 重新安装Python依赖...

:: 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python环境
    echo    请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo    升级pip...
python -m pip install --upgrade pip --quiet

echo    卸载可能有问题的包...
python -m pip uninstall -y customtkinter Pillow requests cryptography --quiet 2>nul

echo    重新安装核心依赖...
python -m pip install --no-cache-dir customtkinter>=5.2.0 --quiet
python -m pip install --no-cache-dir Pillow>=10.0.0 --quiet
python -m pip install --no-cache-dir requests>=2.25.0 --quiet

echo ✅ Python依赖重新安装完成
echo.

:: 5. 修复程序文件
echo 🔧 步骤5: 修复程序文件...

:: 修复main.py中的依赖检查问题
if exist "main.py" (
    echo    修复main.py...
    powershell -Command "(Get-Content 'main.py') -replace \"'Pillow'\", \"'PIL'\" | Set-Content 'main.py'"
    powershell -Command "(Get-Content 'main.py') -replace 'app\.run\(\)', 'app.mainloop()' | Set-Content 'main.py'"
)

:: 创建必要目录
echo    创建必要目录...
if not exist "logs" mkdir logs
if not exist "backups" mkdir backups
if not exist "temp" mkdir temp
if not exist "emergency_backups" mkdir emergency_backups

:: 创建__init__.py文件
echo    创建初始化文件...
if not exist "gui\__init__.py" echo # -*- coding: utf-8 -*- > "gui\__init__.py"
if not exist "utils\__init__.py" echo # -*- coding: utf-8 -*- > "utils\__init__.py"
if not exist "augutils\__init__.py" echo # -*- coding: utf-8 -*- > "augutils\__init__.py"

echo ✅ 程序文件修复完成
echo.

:: 6. 验证修复结果
echo 🔍 步骤6: 验证修复结果...
python -c "import customtkinter, PIL, requests; print('✅ 所有依赖导入成功')" 2>nul
if errorlevel 1 (
    echo ❌ 依赖验证失败，可能需要手动检查
) else (
    echo ✅ 依赖验证通过
)
echo.

:: 7. 完成
echo 🎉 修复完成！
echo ==========================================
echo.
echo 修复结果:
echo ✅ Rust相关缓存已清理
echo ✅ Python环境已重建
echo ✅ 环境变量已清理
echo ✅ 程序文件已修复
echo ✅ 依赖包已重新安装
echo.
echo 💡 建议操作:
echo 1. 重启计算机以确保所有更改生效
echo 2. 使用 🎉完美启动器🎉.vbs 启动程序
echo 3. 如果仍有问题，请使用 🚨深度修复工具🚨.vbs
echo.

:: 询问是否立即启动
set /p choice="是否立即尝试启动AugmentNew？(Y/N): "
if /i "%choice%"=="Y" (
    echo.
    echo 🚀 正在启动AugmentNew...
    python main.py
) else (
    echo.
    echo 👋 修复完成，您可以稍后手动启动程序
)

echo.
pause
