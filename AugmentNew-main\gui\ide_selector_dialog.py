"""
IDE选择对话框
用于让用户选择要重置的IDE类型（VSCode或Cursor）
"""

import tkinter as tk
import customtkinter as ctk
from tkinter import messagebox
import os
import sys

class IDESelectorDialog(ctk.CTkToplevel):
    """IDE选择对话框"""
    
    def __init__(self, parent, available_ides=None):
        super().__init__(parent)
        
        self.parent = parent
        self.available_ides = available_ides or {'vscode': False, 'cursor': False}
        self.selected_ide = None
        self.result = None
        
        # 窗口配置
        self.title("🔧 选择要重置的IDE")
        self.geometry("500x400")
        self.resizable(False, False)
        
        # 设置为模态对话框
        self.transient(parent)
        self.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_widgets()
        
        # 绑定关闭事件
        self.protocol("WM_DELETE_WINDOW", self.on_cancel)
        
    def center_window(self):
        """居中显示窗口"""
        self.update_idletasks()
        
        # 获取父窗口位置和大小
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # 计算居中位置
        x = parent_x + (parent_width - 500) // 2
        y = parent_y + (parent_height - 400) // 2
        
        self.geometry(f"500x400+{x}+{y}")
        
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame, 
            text="🔧 选择要重置的IDE",
            font=("Microsoft YaHei UI", 18, "bold")
        )
        title_label.pack(pady=(10, 20))
        
        # 说明文字
        info_label = ctk.CTkLabel(
            main_frame,
            text="请选择您要重置Augment数据的IDE类型：",
            font=("Microsoft YaHei UI", 12)
        )
        info_label.pack(pady=(0, 20))
        
        # IDE选择区域
        selection_frame = ctk.CTkFrame(main_frame)
        selection_frame.pack(fill="x", pady=(0, 20))
        
        # 创建选择变量
        self.ide_var = tk.StringVar(value="auto")
        
        # VSCode选项
        vscode_frame = ctk.CTkFrame(selection_frame)
        vscode_frame.pack(fill="x", padx=15, pady=(15, 10))
        
        vscode_radio = ctk.CTkRadioButton(
            vscode_frame,
            text="",
            variable=self.ide_var,
            value="vscode"
        )
        vscode_radio.pack(side="left", padx=(10, 15), pady=10)
        
        vscode_info_frame = ctk.CTkFrame(vscode_frame, fg_color="transparent")
        vscode_info_frame.pack(side="left", fill="x", expand=True, pady=5)
        
        vscode_title = ctk.CTkLabel(
            vscode_info_frame,
            text="📝 VSCode + Augment插件",
            font=("Microsoft YaHei UI", 14, "bold")
        )
        vscode_title.pack(anchor="w")
        
        vscode_desc = ctk.CTkLabel(
            vscode_info_frame,
            text="重置VSCode中的Augment插件数据",
            font=("Microsoft YaHei UI", 11),
            text_color="gray"
        )
        vscode_desc.pack(anchor="w")
        
        # 显示VSCode检测状态
        vscode_status = "✅ 已检测到" if self.available_ides.get('vscode', False) else "❌ 未检测到"
        vscode_status_label = ctk.CTkLabel(
            vscode_info_frame,
            text=vscode_status,
            font=("Microsoft YaHei UI", 10),
            text_color="green" if self.available_ides.get('vscode', False) else "red"
        )
        vscode_status_label.pack(anchor="w")
        
        # Cursor选项
        cursor_frame = ctk.CTkFrame(selection_frame)
        cursor_frame.pack(fill="x", padx=15, pady=(10, 15))
        
        cursor_radio = ctk.CTkRadioButton(
            cursor_frame,
            text="",
            variable=self.ide_var,
            value="cursor"
        )
        cursor_radio.pack(side="left", padx=(10, 15), pady=10)
        
        cursor_info_frame = ctk.CTkFrame(cursor_frame, fg_color="transparent")
        cursor_info_frame.pack(side="left", fill="x", expand=True, pady=5)
        
        cursor_title = ctk.CTkLabel(
            cursor_info_frame,
            text="🎯 Cursor IDE",
            font=("Microsoft YaHei UI", 14, "bold")
        )
        cursor_title.pack(anchor="w")
        
        cursor_desc = ctk.CTkLabel(
            cursor_info_frame,
            text="重置Cursor IDE中的AI助手数据",
            font=("Microsoft YaHei UI", 11),
            text_color="gray"
        )
        cursor_desc.pack(anchor="w")
        
        # 显示Cursor检测状态
        cursor_status = "✅ 已检测到" if self.available_ides.get('cursor', False) else "❌ 未检测到"
        cursor_status_label = ctk.CTkLabel(
            cursor_info_frame,
            text=cursor_status,
            font=("Microsoft YaHei UI", 10),
            text_color="green" if self.available_ides.get('cursor', False) else "red"
        )
        cursor_status_label.pack(anchor="w")
        
        # 自动选择选项
        auto_frame = ctk.CTkFrame(selection_frame)
        auto_frame.pack(fill="x", padx=15, pady=(10, 15))
        
        auto_radio = ctk.CTkRadioButton(
            auto_frame,
            text="",
            variable=self.ide_var,
            value="auto"
        )
        auto_radio.pack(side="left", padx=(10, 15), pady=10)
        
        auto_info_frame = ctk.CTkFrame(auto_frame, fg_color="transparent")
        auto_info_frame.pack(side="left", fill="x", expand=True, pady=5)
        
        auto_title = ctk.CTkLabel(
            auto_info_frame,
            text="🔄 自动检测",
            font=("Microsoft YaHei UI", 14, "bold")
        )
        auto_title.pack(anchor="w")
        
        auto_desc = ctk.CTkLabel(
            auto_info_frame,
            text="自动检测并重置所有找到的IDE",
            font=("Microsoft YaHei UI", 11),
            text_color="gray"
        )
        auto_desc.pack(anchor="w")
        
        # 警告信息
        if not any(self.available_ides.values()):
            warning_frame = ctk.CTkFrame(main_frame, fg_color="#ff6b6b")
            warning_frame.pack(fill="x", pady=(0, 20))
            
            warning_label = ctk.CTkLabel(
                warning_frame,
                text="⚠️ 未检测到任何支持的IDE，请确保已安装VSCode或Cursor",
                font=("Microsoft YaHei UI", 11),
                text_color="white"
            )
            warning_label.pack(pady=10)
        
        # 按钮区域
        button_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        button_frame.pack(fill="x", pady=(0, 10))
        
        # 取消按钮
        cancel_btn = ctk.CTkButton(
            button_frame,
            text="取消",
            width=100,
            command=self.on_cancel
        )
        cancel_btn.pack(side="right", padx=(10, 0))
        
        # 确认按钮
        confirm_btn = ctk.CTkButton(
            button_frame,
            text="确认",
            width=100,
            command=self.on_confirm
        )
        confirm_btn.pack(side="right")
        
    def on_confirm(self):
        """确认选择"""
        selected = self.ide_var.get()
        
        # 验证选择
        if selected == "vscode" and not self.available_ides.get('vscode', False):
            messagebox.showwarning("警告", "未检测到VSCode，但您选择了重置VSCode数据。\n\n这可能不会产生任何效果。")
        elif selected == "cursor" and not self.available_ides.get('cursor', False):
            messagebox.showwarning("警告", "未检测到Cursor，但您选择了重置Cursor数据。\n\n这可能不会产生任何效果。")
        elif selected == "auto" and not any(self.available_ides.values()):
            messagebox.showerror("错误", "未检测到任何支持的IDE。\n\n请确保已安装VSCode或Cursor后再试。")
            return
        
        self.result = selected
        self.destroy()
        
    def on_cancel(self):
        """取消选择"""
        self.result = None
        self.destroy()
        
    def get_result(self):
        """获取选择结果"""
        return self.result
