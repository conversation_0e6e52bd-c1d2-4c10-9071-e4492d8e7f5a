#!/usr/bin/env python3
"""
浏览器清理功能测试脚本
注意：此脚本仅用于测试功能，不会实际执行清理操作
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.browser_cleaner import <PERSON><PERSON>er<PERSON>leaner

def test_domain_patterns():
    """测试域名和模式匹配"""
    print("🧪 测试域名和模式匹配...")
    
    cleaner = BrowserCleaner()
    
    # 测试域名列表
    domains = cleaner._get_augment_domains()
    print(f"✅ Augment域名列表 ({len(domains)} 个):")
    for domain in domains:
        print(f"  • {domain}")
    
    # 测试模式列表
    patterns = cleaner._get_augment_patterns()
    print(f"\n✅ Augment匹配模式 ({len(patterns)} 个):")
    for pattern in patterns:
        print(f"  • {pattern}")
    
    # 测试GitHub模式
    github_patterns = cleaner._get_github_augment_patterns()
    print(f"\n✅ GitHub匹配模式 ({len(github_patterns)} 个):")
    for pattern in github_patterns:
        print(f"  • {pattern}")

def test_browser_detection():
    """测试浏览器检测"""
    print("\n🔍 测试浏览器检测...")
    
    cleaner = BrowserCleaner()
    
    try:
        # 获取浏览器路径配置
        browser_paths = cleaner.get_browser_paths()
        print(f"✅ 支持的浏览器 ({len(browser_paths)} 个):")
        for browser, paths in browser_paths.items():
            print(f"  • {browser}: {paths['name']}")
        
        # 检测已安装的浏览器
        installed = cleaner.detect_installed_browsers()
        print(f"\n✅ 检测到的浏览器 ({len(installed)} 个):")
        for browser in installed:
            print(f"  • {browser}")
            
    except Exception as e:
        print(f"❌ 浏览器检测失败: {e}")

def test_cleanup_preview():
    """测试清理预览功能"""
    print("\n📋 测试清理预览功能...")
    
    cleaner = BrowserCleaner()
    
    try:
        preview = cleaner.get_cleanup_preview()
        
        print(f"✅ 检测到浏览器: {len(preview['browsers_detected'])} 个")
        for browser in preview['browsers_detected']:
            print(f"  • {browser}")
        
        print(f"\n📊 预计清理项目总数: {preview['total_estimated_items']}")
        
        if preview['cleanup_items']:
            print("\n📋 详细清理计划:")
            for browser, items in preview['cleanup_items'].items():
                total_items = sum(items.values())
                if total_items > 0:
                    print(f"  🌐 {browser} ({total_items} 项):")
                    for item_type, count in items.items():
                        if count > 0:
                            print(f"    • {item_type}: {count} 项")
        
        if preview['warnings']:
            print("\n⚠️ 警告信息:")
            for warning in preview['warnings']:
                print(f"  • {warning}")
                
    except Exception as e:
        print(f"❌ 清理预览失败: {e}")

def test_file_counting():
    """测试文件计数功能"""
    print("\n🔢 测试文件计数功能...")
    
    cleaner = BrowserCleaner()
    
    # 测试Local Storage计数
    test_paths = [
        os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Local Storage"),
        os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Local Storage"),
    ]
    
    for path in test_paths:
        if os.path.exists(path):
            try:
                count = cleaner._count_augment_files(path, "Local Storage")
                print(f"✅ {os.path.basename(os.path.dirname(path))}: {count} 个Augment相关文件")
            except Exception as e:
                print(f"❌ 计数失败 {path}: {e}")

def test_cookie_counting():
    """测试Cookie计数功能"""
    print("\n🍪 测试Cookie计数功能...")
    
    cleaner = BrowserCleaner()
    
    # 测试Cookie计数
    test_cookie_paths = [
        os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cookies"),
        os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Cookies"),
    ]
    
    for path in test_cookie_paths:
        if os.path.exists(path):
            try:
                count = cleaner._count_augment_cookies(path)
                browser_name = "Chrome" if "Chrome" in path else "Edge"
                print(f"✅ {browser_name}: {count} 个Augment相关cookies")
            except Exception as e:
                print(f"❌ Cookie计数失败 {path}: {e}")

def test_cache_counting():
    """测试缓存计数功能"""
    print("\n💾 测试缓存计数功能...")
    
    cleaner = BrowserCleaner()
    
    # 测试缓存计数
    test_cache_paths = [
        os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache"),
        os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Cache"),
    ]
    
    for path in test_cache_paths:
        if os.path.exists(path):
            try:
                count = cleaner._count_augment_cache_files(path)
                browser_name = "Chrome" if "Chrome" in path else "Edge"
                print(f"✅ {browser_name}: {count} 个Augment相关缓存文件")
            except Exception as e:
                print(f"❌ 缓存计数失败 {path}: {e}")

def test_firefox_counting():
    """测试Firefox计数功能"""
    print("\n🦊 测试Firefox计数功能...")
    
    cleaner = BrowserCleaner()
    
    firefox_profiles = os.path.expandvars(r"%APPDATA%\Mozilla\Firefox\Profiles")
    
    if os.path.exists(firefox_profiles):
        try:
            paths = {'profiles': firefox_profiles}
            counts = cleaner._count_firefox_augment_items(paths)
            
            print("✅ Firefox项目统计:")
            for item_type, count in counts.items():
                if count > 0:
                    print(f"  • {item_type}: {count} 项")
                    
        except Exception as e:
            print(f"❌ Firefox计数失败: {e}")
    else:
        print("ℹ️ 未检测到Firefox")

def generate_test_report():
    """生成测试报告"""
    print("\n📄 生成测试报告...")
    
    cleaner = BrowserCleaner()
    
    try:
        # 收集所有测试信息
        report = {
            "test_time": str(Path(__file__).stat().st_mtime),
            "domains": cleaner._get_augment_domains(),
            "patterns": cleaner._get_augment_patterns(),
            "github_patterns": cleaner._get_github_augment_patterns(),
            "detected_browsers": cleaner.detect_installed_browsers(),
            "preview": cleaner.get_cleanup_preview()
        }
        
        # 保存报告
        report_file = "browser_cleaner_test_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 测试报告已保存到: {report_file}")
        
    except Exception as e:
        print(f"❌ 生成测试报告失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始浏览器清理功能测试")
    print("=" * 50)
    
    # 运行所有测试
    test_domain_patterns()
    test_browser_detection()
    test_cleanup_preview()
    test_file_counting()
    test_cookie_counting()
    test_cache_counting()
    test_firefox_counting()
    generate_test_report()
    
    print("\n" + "=" * 50)
    print("✅ 所有测试完成！")
    print("\n⚠️ 注意：此脚本仅用于测试功能，未执行实际清理操作")
    print("💡 如需执行清理，请使用GUI界面的'多浏览器清理'功能")

if __name__ == "__main__":
    main()
