#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正有效的Augment账号重置工具
专门解决Augment Code试用限制问题

作者: AugmentNew 2.0
日期: 2025-01-14
"""

import os
import sys
import json
import sqlite3
import shutil
import secrets
import uuid
import subprocess
from pathlib import Path

class RealAugmentResetter:
    """真正有效的Augment重置器"""
    
    def __init__(self):
        self.augment_domains = [
            'augmentcode.com',
            '.augmentcode.com', 
            'app.augmentcode.com',
            'auth.augmentcode.com',
            'api.augmentcode.com',
            'www.augmentcode.com'
        ]
        
        self.reset_results = {
            'success': True,
            'items_reset': [],
            'errors': []
        }
    
    def reset_augment_completely(self):
        """完全重置Augment账号状态"""
        print("🚀 开始真正有效的Augment重置...")
        print("⚠️  这将彻底清除所有Augment相关数据，让系统认为这是全新设备！")
        
        # 1. 强制关闭所有浏览器
        self._force_close_browsers()
        
        # 2. 深度清理Chrome数据
        self._deep_clean_chrome()
        
        # 3. 深度清理Edge数据  
        self._deep_clean_edge()
        
        # 4. 清理Firefox数据
        self._deep_clean_firefox()
        
        # 5. 清理VSCode数据
        self._clean_vscode_augment()
        
        # 6. 重置系统标识符
        self._reset_system_identifiers()
        
        # 7. 清理注册表
        self._clean_registry()
        
        # 8. 清理网络缓存
        self._clean_network_cache()
        
        return self.reset_results
    
    def _force_close_browsers(self):
        """强制关闭所有浏览器进程"""
        try:
            browsers = ['chrome.exe', 'msedge.exe', 'firefox.exe', 'opera.exe', 'brave.exe']
            for browser in browsers:
                try:
                    subprocess.run(['taskkill', '/f', '/im', browser], 
                                 capture_output=True, check=False)
                except:
                    pass
            self.reset_results['items_reset'].append("✅ 浏览器进程已关闭")
        except Exception as e:
            self.reset_results['errors'].append(f"关闭浏览器失败: {e}")
    
    def _deep_clean_chrome(self):
        """深度清理Chrome中的Augment数据"""
        try:
            chrome_base = os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default")
            if not os.path.exists(chrome_base):
                return
            
            # 1. 清理Cookies数据库
            cookies_db = os.path.join(chrome_base, "Cookies")
            if os.path.exists(cookies_db):
                self._clean_sqlite_database(cookies_db, "cookies", "host_key", "Chrome Cookies")
            
            # 2. 清理Local Storage
            local_storage = os.path.join(chrome_base, "Local Storage", "leveldb")
            if os.path.exists(local_storage):
                self._clean_leveldb_storage(local_storage, "Chrome Local Storage")
            
            # 3. 清理Web Data
            web_data_db = os.path.join(chrome_base, "Web Data")
            if os.path.exists(web_data_db):
                self._clean_web_data(web_data_db, "Chrome Web Data")
            
            # 4. 清理Preferences
            prefs_file = os.path.join(chrome_base, "Preferences")
            if os.path.exists(prefs_file):
                self._clean_preferences(prefs_file, "Chrome Preferences")
            
            # 5. 清理IndexedDB
            indexeddb_path = os.path.join(chrome_base, "IndexedDB")
            if os.path.exists(indexeddb_path):
                self._clean_indexeddb(indexeddb_path, "Chrome IndexedDB")
                
        except Exception as e:
            self.reset_results['errors'].append(f"清理Chrome数据失败: {e}")
    
    def _deep_clean_edge(self):
        """深度清理Edge中的Augment数据"""
        try:
            edge_base = os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default")
            if not os.path.exists(edge_base):
                return
            
            # 清理方式与Chrome相同
            cookies_db = os.path.join(edge_base, "Cookies")
            if os.path.exists(cookies_db):
                self._clean_sqlite_database(cookies_db, "cookies", "host_key", "Edge Cookies")
            
            local_storage = os.path.join(edge_base, "Local Storage", "leveldb")
            if os.path.exists(local_storage):
                self._clean_leveldb_storage(local_storage, "Edge Local Storage")
                
            web_data_db = os.path.join(edge_base, "Web Data")
            if os.path.exists(web_data_db):
                self._clean_web_data(web_data_db, "Edge Web Data")
                
        except Exception as e:
            self.reset_results['errors'].append(f"清理Edge数据失败: {e}")
    
    def _deep_clean_firefox(self):
        """深度清理Firefox中的Augment数据"""
        try:
            firefox_profiles = os.path.expandvars(r"%APPDATA%\Mozilla\Firefox\Profiles")
            if not os.path.exists(firefox_profiles):
                return
                
            for profile in os.listdir(firefox_profiles):
                profile_path = os.path.join(firefox_profiles, profile)
                if os.path.isdir(profile_path):
                    # 清理cookies
                    cookies_db = os.path.join(profile_path, "cookies.sqlite")
                    if os.path.exists(cookies_db):
                        self._clean_sqlite_database(cookies_db, "moz_cookies", "host", "Firefox Cookies")
                    
                    # 清理历史记录
                    places_db = os.path.join(profile_path, "places.sqlite")
                    if os.path.exists(places_db):
                        self._clean_firefox_places(places_db)
                        
        except Exception as e:
            self.reset_results['errors'].append(f"清理Firefox数据失败: {e}")
    
    def _clean_sqlite_database(self, db_path, table_name, column_name, description):
        """清理SQLite数据库中的Augment相关记录"""
        try:
            # 创建备份
            backup_path = f"{db_path}.backup"
            shutil.copy2(db_path, backup_path)
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            deleted_count = 0
            for domain in self.augment_domains:
                cursor.execute(f"DELETE FROM {table_name} WHERE {column_name} LIKE ?", (f'%{domain}%',))
                deleted_count += cursor.rowcount
            
            conn.commit()
            conn.close()
            
            if deleted_count > 0:
                self.reset_results['items_reset'].append(f"✅ {description}: 删除了{deleted_count}条记录")
            
            # 删除备份
            if os.path.exists(backup_path):
                os.remove(backup_path)
                
        except Exception as e:
            self.reset_results['errors'].append(f"清理{description}失败: {e}")
            # 恢复备份
            backup_path = f"{db_path}.backup"
            if os.path.exists(backup_path):
                try:
                    shutil.move(backup_path, db_path)
                except:
                    pass
    
    def _clean_leveldb_storage(self, leveldb_path, description):
        """清理LevelDB存储中的Augment数据"""
        try:
            removed_files = 0
            for file in os.listdir(leveldb_path):
                if file.endswith(('.log', '.ldb')):
                    file_path = os.path.join(leveldb_path, file)
                    try:
                        with open(file_path, 'rb') as f:
                            content = f.read()
                        
                        # 检查是否包含Augment域名
                        contains_augment = any(domain.encode() in content for domain in self.augment_domains)
                        
                        if contains_augment:
                            os.remove(file_path)
                            removed_files += 1
                    except:
                        pass
            
            if removed_files > 0:
                self.reset_results['items_reset'].append(f"✅ {description}: 删除了{removed_files}个文件")
                
        except Exception as e:
            self.reset_results['errors'].append(f"清理{description}失败: {e}")
    
    def _clean_web_data(self, db_path, description):
        """清理Web Data数据库"""
        try:
            backup_path = f"{db_path}.backup"
            shutil.copy2(db_path, backup_path)
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 清理自动填充数据
            cursor.execute("DELETE FROM autofill WHERE name LIKE '%augment%' OR value LIKE '%augment%'")
            deleted_count = cursor.rowcount
            
            conn.commit()
            conn.close()
            
            if deleted_count > 0:
                self.reset_results['items_reset'].append(f"✅ {description}: 删除了{deleted_count}个自动填充记录")
            
            if os.path.exists(backup_path):
                os.remove(backup_path)
                
        except Exception as e:
            self.reset_results['errors'].append(f"清理{description}失败: {e}")
    
    def _clean_preferences(self, prefs_path, description):
        """清理Preferences文件"""
        try:
            with open(prefs_path, 'r', encoding='utf-8') as f:
                prefs = json.load(f)
            
            # 递归删除所有包含augment的键
            def remove_augment_keys(obj):
                if isinstance(obj, dict):
                    keys_to_remove = [k for k in obj.keys() if 'augment' in k.lower()]
                    for key in keys_to_remove:
                        del obj[key]
                    for value in obj.values():
                        remove_augment_keys(value)
                elif isinstance(obj, list):
                    for item in obj:
                        remove_augment_keys(item)
            
            original_str = json.dumps(prefs)
            remove_augment_keys(prefs)
            new_str = json.dumps(prefs)
            
            if original_str != new_str:
                with open(prefs_path, 'w', encoding='utf-8') as f:
                    json.dump(prefs, f, indent=2)
                self.reset_results['items_reset'].append(f"✅ {description}: 已清理Augment配置")
                
        except Exception as e:
            self.reset_results['errors'].append(f"清理{description}失败: {e}")
    
    def _clean_indexeddb(self, indexeddb_path, description):
        """清理IndexedDB"""
        try:
            import glob
            removed_count = 0
            
            for domain in self.augment_domains:
                pattern = domain.replace('.', '_')
                matches = glob.glob(os.path.join(indexeddb_path, f"*{pattern}*"))
                for match in matches:
                    try:
                        if os.path.isdir(match):
                            shutil.rmtree(match)
                        else:
                            os.remove(match)
                        removed_count += 1
                    except:
                        pass
            
            if removed_count > 0:
                self.reset_results['items_reset'].append(f"✅ {description}: 删除了{removed_count}个数据库")
                
        except Exception as e:
            self.reset_results['errors'].append(f"清理{description}失败: {e}")
    
    def _clean_firefox_places(self, db_path):
        """清理Firefox历史记录和书签"""
        try:
            backup_path = f"{db_path}.backup"
            shutil.copy2(db_path, backup_path)
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 删除历史记录
            cursor.execute("DELETE FROM moz_places WHERE url LIKE '%augmentcode.com%'")
            history_deleted = cursor.rowcount
            
            # 删除书签
            cursor.execute("DELETE FROM moz_bookmarks WHERE title LIKE '%augment%'")
            bookmarks_deleted = cursor.rowcount
            
            conn.commit()
            conn.close()
            
            total = history_deleted + bookmarks_deleted
            if total > 0:
                self.reset_results['items_reset'].append(f"✅ Firefox Places: 删除了{total}条记录")
            
            if os.path.exists(backup_path):
                os.remove(backup_path)
                
        except Exception as e:
            self.reset_results['errors'].append(f"清理Firefox Places失败: {e}")
    
    def _clean_vscode_augment(self):
        """清理VSCode中的Augment数据"""
        try:
            # 清理storage.json
            storage_path = os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\storage.json")
            if os.path.exists(storage_path):
                with open(storage_path, 'r', encoding='utf-8') as f:
                    storage = json.load(f)
                
                # 删除所有Augment相关键
                keys_to_remove = [k for k in storage.keys() if 'augment' in k.lower()]
                for key in keys_to_remove:
                    del storage[key]
                
                # 重新生成设备标识符
                storage['telemetry.machineId'] = secrets.token_hex(32)
                storage['telemetry.devDeviceId'] = str(uuid.uuid4())
                storage['telemetry.sqmId'] = f"{{{str(uuid.uuid4()).upper()}}}"
                
                with open(storage_path, 'w', encoding='utf-8') as f:
                    json.dump(storage, f, indent=2)
                
                self.reset_results['items_reset'].append("✅ VSCode Storage: 已重置设备标识符")
            
            # 清理Augment扩展数据
            extensions_path = os.path.expandvars(r"%APPDATA%\Code\User\globalStorage")
            if os.path.exists(extensions_path):
                for item in os.listdir(extensions_path):
                    if 'augment' in item.lower():
                        item_path = os.path.join(extensions_path, item)
                        try:
                            if os.path.isdir(item_path):
                                shutil.rmtree(item_path)
                            else:
                                os.remove(item_path)
                            self.reset_results['items_reset'].append(f"✅ VSCode扩展数据: {item}")
                        except:
                            pass
                            
        except Exception as e:
            self.reset_results['errors'].append(f"清理VSCode数据失败: {e}")
    
    def _reset_system_identifiers(self):
        """重置系统标识符"""
        try:
            # 重置机器ID文件
            machine_id_path = os.path.expandvars(r"%APPDATA%\Code\machineid")
            if os.path.exists(machine_id_path):
                with open(machine_id_path, 'w') as f:
                    f.write(secrets.token_hex(32))
                self.reset_results['items_reset'].append("✅ 机器ID已重置")
                
        except Exception as e:
            self.reset_results['errors'].append(f"重置系统标识符失败: {e}")
    
    def _clean_registry(self):
        """清理注册表中的相关项"""
        try:
            if os.name == 'nt':
                import winreg
                
                # 重置机器GUID
                try:
                    key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                       r"SOFTWARE\Microsoft\Cryptography",
                                       0, winreg.KEY_SET_VALUE)
                    new_guid = str(uuid.uuid4()).upper()
                    winreg.SetValueEx(key, "MachineGuid", 0, winreg.REG_SZ, new_guid)
                    winreg.CloseKey(key)
                    self.reset_results['items_reset'].append("✅ 注册表机器GUID已重置")
                except:
                    pass
                    
        except Exception as e:
            self.reset_results['errors'].append(f"清理注册表失败: {e}")
    
    def _clean_network_cache(self):
        """清理网络缓存"""
        try:
            # 清理DNS缓存
            subprocess.run(['ipconfig', '/flushdns'], capture_output=True, check=False)
            self.reset_results['items_reset'].append("✅ DNS缓存已清理")
            
            # 清理ARP缓存
            subprocess.run(['arp', '-d', '*'], capture_output=True, check=False)
            self.reset_results['items_reset'].append("✅ ARP缓存已清理")
            
        except Exception as e:
            self.reset_results['errors'].append(f"清理网络缓存失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 真正有效的Augment账号重置工具")
    print("=" * 60)
    
    resetter = RealAugmentResetter()
    result = resetter.reset_augment_completely()
    
    print("\n📊 重置结果:")
    print(f"✅ 成功: {result['success']}")
    print(f"🔧 重置项目: {len(result['items_reset'])}")
    
    if result['items_reset']:
        print("\n🎯 已重置的项目:")
        for item in result['items_reset']:
            print(f"  {item}")
    
    if result['errors']:
        print("\n❌ 遇到的错误:")
        for error in result['errors']:
            print(f"  ❌ {error}")
    
    print("\n🎉 重置完成！")
    print("💡 建议:")
    print("  1. 重启所有浏览器")
    print("  2. 重启VSCode")
    print("  3. 访问 app.augmentcode.com 检查效果")
    print("  4. 如果还是显示旧账号，请清理浏览器缓存后重试")

if __name__ == "__main__":
    main()
