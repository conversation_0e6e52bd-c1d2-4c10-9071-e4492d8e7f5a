@echo off
chcp 65001 >nul
title AugmentNew 增强版启动器

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 AugmentNew 增强版                      ║
echo ║                     永久免费 完全开源                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🆕 增强版新功能：
echo   🌐 多浏览器清理 - 支持Chrome、Edge、Firefox等
echo   🔧 Storage错误修复 - 解决VSCode Storage问题  
echo   🐙 GitHub增强清理 - SSH密钥管理和Git重置
echo.
echo 📋 使用前请确保：
echo   ✅ 已完全退出 VS Code
echo   ✅ 已关闭所有浏览器
echo   ✅ 以管理员身份运行（推荐）
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未检测到Python
    echo.
    echo 请先安装Python 3.10或更高版本：
    echo https://www.python.org/downloads/
    echo.
    echo 安装时请勾选 "Add Python to PATH"
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

:: 检查依赖包
echo 🔍 检查依赖包...
python -c "import customtkinter" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  检测到缺少依赖包，正在自动安装...
    echo.
    pip install customtkinter>=5.2.0 pillow>=10.0.0
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        echo.
        echo 请手动运行以下命令：
        echo pip install customtkinter pillow
        echo.
        pause
        exit /b 1
    )
    echo ✅ 依赖包安装完成
    echo.
)

:: 启动程序
echo 🚀 启动 AugmentNew 增强版...
echo.
python gui_main.py

if errorlevel 1 (
    echo.
    echo ❌ 程序启动失败
    echo.
    echo 可能的解决方案：
    echo 1. 确保Python已正确安装
    echo 2. 尝试以管理员身份运行
    echo 3. 检查是否有杀毒软件阻止
    echo.
    pause
)

echo.
echo 程序已退出
pause
