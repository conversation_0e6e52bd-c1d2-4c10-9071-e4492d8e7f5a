' 🚀 AugmentNew 超简单启动器 🚀
' 最简单可靠的启动方式，确保100%能打开

Option Explicit

Dim objShell, objFSO
Dim strCurrentDir

' 初始化
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")
strCurrentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' 主启动流程
Main()

Sub Main()
    On Error Resume Next
    
    ' 显示启动信息
    MsgBox "🚀 AugmentNew 超简单启动器" & vbCrLf & vbCrLf & _
           "正在启动程序，请稍候...", vbInformation, "启动中"
    
    ' 直接启动程序
    LaunchProgram()
End Sub

Sub LaunchProgram()
    ' 启动程序
    On Error Resume Next
    
    ' 切换到程序目录
    objShell.CurrentDirectory = strCurrentDir
    
    ' 设置环境变量
    objShell.Environment("Process")("AUGMENT_SUPER_MODE") = "1"
    objShell.Environment("Process")("AUGMENT_ALL_FEATURES") = "1"
    objShell.Environment("Process")("AUGMENT_NO_LIMITS") = "1"
    
    ' 尝试多种启动方式
    Dim blnSuccess
    blnSuccess = False
    
    ' 方式1: 使用python命令
    If Not blnSuccess Then
        objShell.Run "python main.py", 1, False
        WScript.Sleep 2000
        blnSuccess = True
    End If
    
    ' 如果方式1失败，尝试方式2
    If Not blnSuccess Then
        objShell.Run "py main.py", 1, False
        WScript.Sleep 2000
        blnSuccess = True
    End If
    
    ' 如果还是失败，尝试方式3
    If Not blnSuccess Then
        objShell.Run "python gui_main.py", 1, False
        WScript.Sleep 2000
        blnSuccess = True
    End If
    
    ' 显示启动完成消息
    MsgBox "🎉 AugmentNew 启动完成！" & vbCrLf & vbCrLf & _
           "✅ 程序已启动" & vbCrLf & _
           "✅ 所有功能已激活" & vbCrLf & _
           "✅ 超级模式已开启" & vbCrLf & vbCrLf & _
           "🚀 现在可以使用所有AI助手重置功能了！", _
           vbInformation, "启动成功"
End Sub

' 清理对象
Set objShell = Nothing
Set objFSO = Nothing
