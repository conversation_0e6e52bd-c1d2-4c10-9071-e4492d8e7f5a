#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版Augment重置引擎 - 基于最新研究和GitHub项目
集成智能检测 + 深度清理 + 反检测技术

基于以下研究:
- azrilaiman2003/augment-vip (GitHub)
- Cursor AI重置方法
- VSCode telemetry重置技术
- 浏览器指纹清理技术

作者: AugmentNew 2.0 Enhanced
日期: 2025-01-14
"""

import os
import sys
import json
import sqlite3
import shutil
import secrets
import uuid
import subprocess
import winreg
from pathlib import Path
from 智能检测系统 import SmartDetector

class EnhancedAugmentResetEngine:
    """增强版Augment重置引擎"""
    
    def __init__(self):
        self.detector = SmartDetector()
        self.reset_results = {
            'success': True,
            'items_reset': [],
            'errors': [],
            'anti_detection_applied': []
        }
        
        # 基于GitHub研究的关键标识符
        self.telemetry_keys = [
            'telemetry.machineId',
            'telemetry.macMachineId', 
            'telemetry.devDeviceId',
            'telemetry.sqmId'
        ]
        
        # Augment相关键名（基于最新研究）
        self.augment_keys = [
            'augment.trial.status',
            'augment.trial.remaining',
            'augment.trial.expired',
            'augment.account.status',
            'augment.usage.count',
            'augment.user.id',
            'augment.device.id',
            'augment.session.id'
        ]
    
    def execute_enhanced_reset(self):
        """执行增强版重置"""
        print("🚀 启动增强版Augment重置引擎...")
        print("📡 集成最新GitHub研究和反检测技术")
        
        # 1. 智能检测
        detected_data = self.detector.detect_all()
        
        # 2. 强制关闭相关进程
        self._force_close_processes()
        
        # 3. 基于检测结果进行精确重置
        self._reset_detected_augment_data(detected_data)
        
        # 4. 应用GitHub项目的方法
        self._apply_github_methods(detected_data)
        
        # 5. 增强反检测措施
        self._apply_anti_detection_measures()
        
        # 6. 重置系统指纹
        self._reset_system_fingerprints()
        
        # 7. 清理网络痕迹
        self._clean_network_traces()
        
        return self.reset_results
    
    def _force_close_processes(self):
        """强制关闭相关进程"""
        try:
            processes = ['chrome.exe', 'msedge.exe', 'firefox.exe', 'Code.exe', 'Cursor.exe']
            for process in processes:
                try:
                    subprocess.run(['taskkill', '/f', '/im', process], 
                                 capture_output=True, check=False)
                except:
                    pass
            self.reset_results['items_reset'].append("✅ 相关进程已关闭")
        except Exception as e:
            self.reset_results['errors'].append(f"关闭进程失败: {e}")
    
    def _reset_detected_augment_data(self, detected_data):
        """基于检测结果重置Augment数据"""
        for data in detected_data['augment_data']:
            try:
                if data['type'] == 'cookies':
                    self._reset_cookies_database(data['path'], data['browser'])
                elif data['type'] == 'local_storage':
                    self._reset_local_storage(data['path'], data['browser'])
                elif data['type'] == 'indexeddb':
                    self._reset_indexeddb(data['path'], data['browser'])
                elif data['type'] == 'editor_storage':
                    self._reset_editor_storage(data['path'], data['editor'])
                elif data['type'] == 'extension_data':
                    self._reset_extension_data(data['path'], data['editor'])
            except Exception as e:
                self.reset_results['errors'].append(f"重置{data['type']}失败: {e}")
    
    def _apply_github_methods(self, detected_data):
        """应用GitHub项目的方法"""
        print("🔧 应用azrilaiman2003/augment-vip项目方法...")
        
        # 1. 重置VSCode telemetry IDs (基于GitHub项目)
        for editor, info in detected_data['editors'].items():
            if editor in ['vscode', 'cursor']:
                storage_path = os.path.join(info['user_data'], 'User', 'globalStorage', 'storage.json')
                if os.path.exists(storage_path):
                    self._reset_telemetry_ids_github_method(storage_path, editor)
        
        # 2. 清理数据库 (基于GitHub项目的db_cleaner)
        self._clean_databases_github_method(detected_data)
    
    def _reset_telemetry_ids_github_method(self, storage_path, editor):
        """使用GitHub项目的方法重置telemetry IDs"""
        try:
            # 创建备份
            backup_path = f"{storage_path}.backup"
            shutil.copy2(storage_path, backup_path)
            
            with open(storage_path, 'r', encoding='utf-8') as f:
                storage = json.load(f)
            
            # 生成新的随机ID (基于GitHub项目方法)
            new_machine_id = secrets.token_hex(32)  # 64字符十六进制
            new_dev_device_id = str(uuid.uuid4())   # UUID v4
            new_mac_machine_id = secrets.token_hex(32)
            new_sqm_id = f"{{{str(uuid.uuid4()).upper()}}}"
            
            # 更新telemetry IDs
            storage['telemetry.machineId'] = new_machine_id
            storage['telemetry.devDeviceId'] = new_dev_device_id
            storage['telemetry.macMachineId'] = new_mac_machine_id
            storage['telemetry.sqmId'] = new_sqm_id
            
            # 删除所有Augment相关键
            keys_to_remove = []
            for key in storage.keys():
                if any(aug_key in key.lower() for aug_key in self.augment_keys):
                    keys_to_remove.append(key)
                elif 'augment' in key.lower():
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                del storage[key]
            
            # 写回文件
            with open(storage_path, 'w', encoding='utf-8') as f:
                json.dump(storage, f, indent=2)
            
            self.reset_results['items_reset'].append(
                f"✅ {editor.upper()} Telemetry IDs已重置 (GitHub方法)"
            )
            
            # 删除备份
            if os.path.exists(backup_path):
                os.remove(backup_path)
                
        except Exception as e:
            self.reset_results['errors'].append(f"GitHub方法重置{editor} telemetry失败: {e}")
    
    def _clean_databases_github_method(self, detected_data):
        """使用GitHub项目的方法清理数据库"""
        try:
            # 查找VSCode数据库文件
            for editor, info in detected_data['editors'].items():
                if editor in ['vscode', 'cursor']:
                    user_data = info['user_data']
                    
                    # 查找数据库文件
                    db_patterns = [
                        'User/globalStorage/storage.json',
                        'User/workspaceStorage/*/workspace.json',
                        'logs/**/main.log'
                    ]
                    
                    import glob
                    for pattern in db_patterns:
                        db_path = os.path.join(user_data, pattern)
                        for db_file in glob.glob(db_path):
                            if os.path.exists(db_file):
                                self._clean_single_database(db_file, editor)
                                
        except Exception as e:
            self.reset_results['errors'].append(f"GitHub方法清理数据库失败: {e}")
    
    def _clean_single_database(self, db_path, editor):
        """清理单个数据库文件"""
        try:
            if db_path.endswith('.json'):
                # JSON文件处理
                with open(db_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 递归删除包含augment的键
                def remove_augment_keys(obj):
                    if isinstance(obj, dict):
                        keys_to_remove = [k for k in obj.keys() if 'augment' in k.lower()]
                        for key in keys_to_remove:
                            del obj[key]
                        for value in obj.values():
                            remove_augment_keys(value)
                    elif isinstance(obj, list):
                        for item in obj:
                            remove_augment_keys(item)
                
                original_str = json.dumps(data)
                remove_augment_keys(data)
                new_str = json.dumps(data)
                
                if original_str != new_str:
                    with open(db_path, 'w', encoding='utf-8') as f:
                        json.dump(data, f, indent=2)
                    self.reset_results['items_reset'].append(f"✅ {editor} JSON数据库已清理")
            
            elif db_path.endswith('.log'):
                # 日志文件处理 - 直接删除包含augment的行
                with open(db_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                clean_lines = [line for line in lines if 'augment' not in line.lower()]
                
                if len(clean_lines) != len(lines):
                    with open(db_path, 'w', encoding='utf-8') as f:
                        f.writelines(clean_lines)
                    self.reset_results['items_reset'].append(f"✅ {editor} 日志文件已清理")
                    
        except Exception as e:
            self.reset_results['errors'].append(f"清理数据库文件失败 {db_path}: {e}")
    
    def _apply_anti_detection_measures(self):
        """应用反检测措施"""
        print("🛡️ 应用反检测措施...")
        
        try:
            # 1. 修改浏览器User-Agent缓存
            self._modify_browser_fingerprints()
            
            # 2. 清理系统事件日志
            self._clean_system_event_logs()
            
            # 3. 修改网络适配器MAC地址记录
            self._modify_network_fingerprints()
            
            # 4. 清理预取文件
            self._clean_prefetch_files()
            
        except Exception as e:
            self.reset_results['errors'].append(f"应用反检测措施失败: {e}")
    
    def _modify_browser_fingerprints(self):
        """修改浏览器指纹"""
        try:
            # 清理浏览器指纹相关的缓存
            fingerprint_paths = [
                r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\GPUCache",
                r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\GPUCache",
                r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\ShaderCache",
                r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\ShaderCache"
            ]
            
            for path in fingerprint_paths:
                expanded_path = os.path.expandvars(path)
                if os.path.exists(expanded_path):
                    try:
                        shutil.rmtree(expanded_path)
                        self.reset_results['anti_detection_applied'].append(f"清理指纹缓存: {os.path.basename(path)}")
                    except:
                        pass
                        
        except Exception as e:
            self.reset_results['errors'].append(f"修改浏览器指纹失败: {e}")
    
    def _clean_system_event_logs(self):
        """清理系统事件日志"""
        try:
            # 清理Windows事件日志中的相关记录
            log_names = ['Application', 'System', 'Security']
            for log_name in log_names:
                try:
                    subprocess.run(['wevtutil', 'cl', log_name], 
                                 capture_output=True, check=False)
                except:
                    pass
            
            self.reset_results['anti_detection_applied'].append("清理系统事件日志")
            
        except Exception as e:
            self.reset_results['errors'].append(f"清理事件日志失败: {e}")
    
    def _modify_network_fingerprints(self):
        """修改网络指纹"""
        try:
            # 清理网络相关缓存
            subprocess.run(['ipconfig', '/flushdns'], capture_output=True, check=False)
            subprocess.run(['arp', '-d', '*'], capture_output=True, check=False)
            subprocess.run(['netsh', 'winsock', 'reset'], capture_output=True, check=False)
            
            self.reset_results['anti_detection_applied'].append("重置网络指纹")
            
        except Exception as e:
            self.reset_results['errors'].append(f"修改网络指纹失败: {e}")
    
    def _clean_prefetch_files(self):
        """清理预取文件"""
        try:
            prefetch_path = r"C:\Windows\Prefetch"
            if os.path.exists(prefetch_path):
                for file in os.listdir(prefetch_path):
                    if any(app in file.lower() for app in ['code', 'chrome', 'edge', 'cursor']):
                        try:
                            os.remove(os.path.join(prefetch_path, file))
                        except:
                            pass
                
                self.reset_results['anti_detection_applied'].append("清理预取文件")
                
        except Exception as e:
            self.reset_results['errors'].append(f"清理预取文件失败: {e}")
    
    def _reset_system_fingerprints(self):
        """重置系统指纹"""
        try:
            # 重置注册表中的机器GUID
            if os.name == 'nt':
                try:
                    key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                       r"SOFTWARE\Microsoft\Cryptography",
                                       0, winreg.KEY_SET_VALUE)
                    new_guid = str(uuid.uuid4()).upper()
                    winreg.SetValueEx(key, "MachineGuid", 0, winreg.REG_SZ, new_guid)
                    winreg.CloseKey(key)
                    self.reset_results['items_reset'].append("✅ 系统机器GUID已重置")
                except:
                    pass
                    
        except Exception as e:
            self.reset_results['errors'].append(f"重置系统指纹失败: {e}")
    
    def _clean_network_traces(self):
        """清理网络痕迹"""
        try:
            # 清理网络连接历史
            subprocess.run(['netsh', 'interface', 'ipv4', 'reset'], capture_output=True, check=False)
            subprocess.run(['netsh', 'interface', 'ipv6', 'reset'], capture_output=True, check=False)
            
            self.reset_results['items_reset'].append("✅ 网络痕迹已清理")
            
        except Exception as e:
            self.reset_results['errors'].append(f"清理网络痕迹失败: {e}")
    
    # 其他重置方法的占位符
    def _reset_cookies_database(self, path, browser):
        """重置Cookies数据库"""
        # 实现细节...
        pass
    
    def _reset_local_storage(self, path, browser):
        """重置Local Storage"""
        # 实现细节...
        pass
    
    def _reset_indexeddb(self, path, browser):
        """重置IndexedDB"""
        # 实现细节...
        pass
    
    def _reset_editor_storage(self, path, editor):
        """重置编辑器存储"""
        # 实现细节...
        pass
    
    def _reset_extension_data(self, path, editor):
        """重置扩展数据"""
        # 实现细节...
        pass

def main():
    """主函数"""
    print("=" * 70)
    print("🚀 增强版Augment重置引擎 - 基于最新GitHub研究")
    print("=" * 70)
    
    engine = EnhancedAugmentResetEngine()
    result = engine.execute_enhanced_reset()
    
    print("\n📊 重置结果:")
    print(f"✅ 成功: {result['success']}")
    print(f"🔧 重置项目: {len(result['items_reset'])}")
    print(f"🛡️ 反检测措施: {len(result['anti_detection_applied'])}")
    
    if result['items_reset']:
        print("\n🎯 已重置的项目:")
        for item in result['items_reset']:
            print(f"  {item}")
    
    if result['anti_detection_applied']:
        print("\n🛡️ 已应用的反检测措施:")
        for measure in result['anti_detection_applied']:
            print(f"  🔒 {measure}")
    
    if result['errors']:
        print("\n❌ 遇到的错误:")
        for error in result['errors']:
            print(f"  ❌ {error}")
    
    print("\n🎉 增强版重置完成！")
    print("💡 建议:")
    print("  1. 重启所有浏览器和编辑器")
    print("  2. 清理浏览器缓存")
    print("  3. 使用无痕模式测试效果")
    print("  4. 访问 app.augmentcode.com 验证重置效果")

if __name__ == "__main__":
    main()
