#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import sqlite3
import shutil
import secrets
import uuid

def test_augment_reset():
    """测试Augment重置功能"""
    print("🚀 开始测试Augment重置...")
    
    results = []
    
    # 1. 检查Chrome Cookies数据库
    chrome_cookies = os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cookies")
    if os.path.exists(chrome_cookies):
        try:
            # 创建备份
            backup_path = f"{chrome_cookies}.backup"
            shutil.copy2(chrome_cookies, backup_path)
            
            conn = sqlite3.connect(chrome_cookies)
            cursor = conn.cursor()
            
            # 查找Augment相关cookies
            cursor.execute("SELECT host_key, name FROM cookies WHERE host_key LIKE '%augment%'")
            augment_cookies = cursor.fetchall()
            
            if augment_cookies:
                print(f"🎯 找到 {len(augment_cookies)} 个Augment相关cookies:")
                for host, name in augment_cookies[:5]:  # 显示前5个
                    print(f"  - {host}: {name}")
                
                # 删除Augment cookies
                cursor.execute("DELETE FROM cookies WHERE host_key LIKE '%augment%'")
                deleted_count = cursor.rowcount
                
                conn.commit()
                results.append(f"✅ Chrome Cookies: 删除了 {deleted_count} 个Augment cookies")
            else:
                results.append("ℹ️ Chrome Cookies: 未找到Augment相关cookies")
            
            conn.close()
            
            # 删除备份
            if os.path.exists(backup_path):
                os.remove(backup_path)
                
        except Exception as e:
            results.append(f"❌ Chrome Cookies处理失败: {e}")
    else:
        results.append("ℹ️ Chrome Cookies数据库不存在")
    
    # 2. 检查VSCode storage.json
    storage_path = os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\storage.json")
    if os.path.exists(storage_path):
        try:
            with open(storage_path, 'r', encoding='utf-8') as f:
                storage = json.load(f)
            
            # 查找Augment相关键
            augment_keys = [k for k in storage.keys() if 'augment' in k.lower()]
            
            if augment_keys:
                print(f"🎯 找到 {len(augment_keys)} 个Augment相关存储键:")
                for key in augment_keys[:5]:  # 显示前5个
                    print(f"  - {key}")
                
                # 删除Augment键
                for key in augment_keys:
                    del storage[key]
                
                # 重新生成设备标识符
                storage['telemetry.machineId'] = secrets.token_hex(32)
                storage['telemetry.devDeviceId'] = str(uuid.uuid4())
                storage['telemetry.sqmId'] = f"{{{str(uuid.uuid4()).upper()}}}"
                
                with open(storage_path, 'w', encoding='utf-8') as f:
                    json.dump(storage, f, indent=2)
                
                results.append(f"✅ VSCode Storage: 删除了 {len(augment_keys)} 个Augment键，重置了设备标识符")
            else:
                results.append("ℹ️ VSCode Storage: 未找到Augment相关键")
                
        except Exception as e:
            results.append(f"❌ VSCode Storage处理失败: {e}")
    else:
        results.append("ℹ️ VSCode Storage文件不存在")
    
    # 3. 检查Chrome Local Storage
    chrome_local_storage = os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Local Storage\leveldb")
    if os.path.exists(chrome_local_storage):
        try:
            augment_files = []
            for file in os.listdir(chrome_local_storage):
                if file.endswith(('.log', '.ldb')):
                    file_path = os.path.join(chrome_local_storage, file)
                    try:
                        with open(file_path, 'rb') as f:
                            content = f.read()
                        
                        if b'augmentcode.com' in content or b'augment' in content.lower():
                            augment_files.append(file)
                    except:
                        pass
            
            if augment_files:
                print(f"🎯 找到 {len(augment_files)} 个包含Augment数据的Local Storage文件:")
                for file in augment_files[:3]:  # 显示前3个
                    print(f"  - {file}")
                
                # 删除包含Augment数据的文件
                removed_count = 0
                for file in augment_files:
                    try:
                        os.remove(os.path.join(chrome_local_storage, file))
                        removed_count += 1
                    except:
                        pass
                
                results.append(f"✅ Chrome Local Storage: 删除了 {removed_count} 个Augment数据文件")
            else:
                results.append("ℹ️ Chrome Local Storage: 未找到Augment相关数据")
                
        except Exception as e:
            results.append(f"❌ Chrome Local Storage处理失败: {e}")
    else:
        results.append("ℹ️ Chrome Local Storage目录不存在")
    
    # 4. 检查Chrome IndexedDB
    chrome_indexeddb = os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\IndexedDB")
    if os.path.exists(chrome_indexeddb):
        try:
            augment_dbs = []
            for item in os.listdir(chrome_indexeddb):
                if 'augment' in item.lower():
                    augment_dbs.append(item)
            
            if augment_dbs:
                print(f"🎯 找到 {len(augment_dbs)} 个Augment相关IndexedDB:")
                for db in augment_dbs:
                    print(f"  - {db}")
                
                # 删除Augment IndexedDB
                removed_count = 0
                for db in augment_dbs:
                    try:
                        db_path = os.path.join(chrome_indexeddb, db)
                        if os.path.isdir(db_path):
                            shutil.rmtree(db_path)
                        else:
                            os.remove(db_path)
                        removed_count += 1
                    except:
                        pass
                
                results.append(f"✅ Chrome IndexedDB: 删除了 {removed_count} 个Augment数据库")
            else:
                results.append("ℹ️ Chrome IndexedDB: 未找到Augment相关数据库")
                
        except Exception as e:
            results.append(f"❌ Chrome IndexedDB处理失败: {e}")
    else:
        results.append("ℹ️ Chrome IndexedDB目录不存在")
    
    # 5. 重置机器ID
    machine_id_path = os.path.expandvars(r"%APPDATA%\Code\machineid")
    if os.path.exists(machine_id_path):
        try:
            with open(machine_id_path, 'w') as f:
                f.write(secrets.token_hex(32))
            results.append("✅ 机器ID已重置")
        except Exception as e:
            results.append(f"❌ 机器ID重置失败: {e}")
    else:
        results.append("ℹ️ 机器ID文件不存在")
    
    # 显示结果
    print("\n📊 重置结果:")
    for result in results:
        print(f"  {result}")
    
    print("\n🎉 测试重置完成！")
    print("💡 建议:")
    print("  1. 关闭所有浏览器窗口")
    print("  2. 重启VSCode")
    print("  3. 访问 app.augmentcode.com 检查效果")
    
    return results

if __name__ == "__main__":
    test_augment_reset()
