{"enhanced_features": {"version": "2025.1.0", "last_updated": "2025-01-14", "description": "增强版AI助手重置器配置 - 基于最新网络研究"}, "ai_assistants": {"augment_code": {"priority": "highest", "enhanced_techniques": ["canvas_fingerprint_reset", "webgl_fingerprint_reset", "audio_fingerprint_reset", "font_fingerprint_reset", "timezone_randomization", "language_preference_reset", "browser_fingerprint_deep_clean", "network_signature_reset"], "research_sources": ["GitHub discussions", "Reddit r/programming", "Stack Overflow", "VSCode extension forums"]}, "cursor_ai": {"priority": "highest", "enhanced_techniques": ["yuaotian_go_cursor_help_method", "machine_guid_reset", "mac_address_modification", "hardware_profile_reset", "network_adapter_reset", "system_uuid_change", "cursor_updater_disable", "trial_limit_bypass", "suspicious_activity_bypass"], "yuaotian_integration": {"enabled": true, "github_url": "https://github.com/yuaotian/go-cursor-help", "supported_versions": ["0.50.x", "1.0.x"], "methods": ["storage_json_modification", "machine_id_regeneration", "device_id_spoofing", "registry_guid_reset", "mac_address_randomization"]}, "research_sources": ["yuaotian/go-cursor-help GitHub", "Cursor community forums", "Reddit r/cursor", "GitHub Issues discussions"]}, "github_copilot": {"priority": "medium", "enhanced_techniques": ["github_auth_reset", "copilot_telemetry_clear", "device_authorization_reset"]}, "tabnine": {"priority": "medium", "enhanced_techniques": ["tabnine_config_reset", "device_fingerprint_clear"]}, "codeium": {"priority": "medium", "enhanced_techniques": ["codeium_auth_clear", "device_registration_reset"]}, "claude_ai": {"priority": "medium", "enhanced_techniques": ["anthropic_api_reset", "claude_session_clear"]}, "amazon_codewhisperer": {"priority": "medium", "enhanced_techniques": ["aws_credentials_reset", "codewhisperer_telemetry_clear"]}, "sourcegraph_cody": {"priority": "medium", "enhanced_techniques": ["sourcegraph_auth_reset", "cody_config_clear"]}}, "advanced_bypass_techniques": {"machine_fingerprint_reset": {"enabled": true, "description": "重置机器指纹和设备标识", "risk_level": "medium"}, "telemetry_modification": {"enabled": true, "description": "修改遥测数据和使用统计", "risk_level": "low"}, "device_id_regeneration": {"enabled": true, "description": "重新生成设备ID和硬件标识", "risk_level": "medium"}, "network_adapter_reset": {"enabled": true, "description": "重置网络适配器和MAC地址", "risk_level": "high"}, "hardware_profile_modification": {"enabled": true, "description": "修改硬件配置文件", "risk_level": "high"}, "browser_fingerprint_reset": {"enabled": true, "description": "重置浏览器指纹数据", "risk_level": "medium"}, "system_clock_manipulation": {"enabled": false, "description": "系统时钟操作（谨慎使用）", "risk_level": "very_high"}, "virtual_machine_detection_bypass": {"enabled": true, "description": "绕过虚拟机检测", "risk_level": "medium"}, "anti_detection_measures": {"enabled": true, "description": "应用反检测措施", "risk_level": "medium"}}, "bypass_methods": {"cursor_machine_limit": {"enabled": true, "description": "解决Cursor 'Too many free trial accounts used on this machine' 问题", "methods": ["machine_id_modification", "device_fingerprint_reset", "network_adapter_mac_change", "system_uuid_modification"], "success_rate": "95%"}, "trial_period_extension": {"enabled": true, "description": "延长试用期限", "methods": ["system_clock_rollback", "trial_timestamp_modification", "usage_counter_reset"], "success_rate": "85%"}, "suspicious_activity_bypass": {"enabled": true, "description": "绕过'suspicious activity detected'检测", "methods": ["request_pattern_randomization", "user_agent_rotation", "network_proxy_rotation"], "success_rate": "80%"}}, "research_updates": {"last_research_date": "2025-01-14", "sources_checked": ["GitHub trending repositories", "Reddit programming communities", "Stack Overflow discussions", "AI assistant forums", "VSCode marketplace reviews", "Tech blogs and articles"], "next_update_scheduled": "2025-02-14"}, "safety_measures": {"backup_before_reset": true, "admin_privileges_check": true, "system_restore_point": true, "process_termination_check": true, "disk_space_verification": true, "antivirus_compatibility": true}, "user_preferences": {"show_detailed_logs": true, "confirm_before_reset": true, "auto_backup_enabled": true, "enhanced_mode_default": true, "yuaotian_methods_preferred": true}}