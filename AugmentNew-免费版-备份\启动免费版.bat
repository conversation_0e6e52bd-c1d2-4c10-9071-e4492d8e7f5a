@echo off
chcp 65001 >nul
title AugmentNew 免费版启动器

echo.
echo ========================================
echo    AugmentNew 免费版启动器
echo ========================================
echo.
echo 🆓 完全免费，永久免费，拒绝收费！
echo 🚫 无需激活码，无需验证码
echo 🔓 完全开源，源码透明
echo 🛡️ 安全可靠，自动备份
echo.
echo ⚠️ 如有人向您收费，请立即举报！
echo ⚠️ 真正的开源软件永远免费！
echo.
echo ========================================
echo.

:: 检查Python是否安装
echo 🔍 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ 未找到Python解释器！
    echo.
    echo AugmentNew需要Python 3.10或更高版本
    echo.
    echo 📥 请访问以下地址下载Python：
    echo https://www.python.org/downloads/
    echo.
    echo 💡 安装时请勾选 'Add Python to PATH'
    echo.
    pause
    exit /b 1
)

:: 检查主程序文件
if not exist "gui_main.py" (
    echo.
    echo ❌ 错误：找不到主程序文件 gui_main.py
    echo.
    echo 请确保所有文件完整
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

:: 检查并安装依赖
echo 🔧 检查程序依赖...
python -c "import customtkinter" >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo 📦 正在安装必要的依赖包...
    echo.
    pip install customtkinter pillow
    if %errorlevel% neq 0 (
        echo.
        echo ❌ 依赖安装失败！
        echo.
        echo 请手动运行以下命令：
        echo pip install customtkinter pillow
        echo.
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
)

echo ✅ 依赖检查通过
echo.

:: 启动程序
echo 🚀 启动 AugmentNew 免费版...
echo.
python gui_main.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ 程序启动失败！
    echo.
    echo 请检查错误信息并重试
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ 程序已启动
echo.
echo 🆓 记住：本软件永久免费！
echo 🚫 拒绝任何收费版本！
echo.
pause
