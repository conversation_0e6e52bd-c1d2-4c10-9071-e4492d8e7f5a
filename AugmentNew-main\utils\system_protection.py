"""
系统保护模块
防止程序操作导致系统崩溃，提供多层安全防护
"""

import os
import sys
import json
import time
import shutil
import psutil
import threading
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

class SystemProtection:
    """系统保护类 - 多层安全防护"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.protection_config = {
            'max_memory_usage': 80,  # 最大内存使用率
            'max_cpu_usage': 90,     # 最大CPU使用率
            'min_disk_space': 1024,  # 最小磁盘空间(MB)
            'max_file_operations': 1000,  # 最大文件操作数
            'operation_timeout': 300,     # 操作超时时间(秒)
        }
        
        self.monitoring_active = False
        self.monitor_thread = None
        self.operation_count = 0
        self.critical_files_backup = {}
        
        # 系统关键文件列表
        self.critical_system_files = [
            r"C:\Windows\System32\drivers\etc\hosts",
            r"C:\Windows\System32\config\SAM",
            r"C:\Windows\System32\config\SYSTEM",
            r"C:\Windows\System32\config\SOFTWARE"
        ]
        
        # 危险操作列表
        self.dangerous_operations = [
            'format',
            'del /s',
            'rmdir /s',
            'reg delete HKLM',
            'bcdedit /delete',
            'diskpart'
        ]
    
    def start_protection(self) -> Dict[str, Any]:
        """启动系统保护"""
        result = {'success': True, 'errors': []}
        
        try:
            # 1. 创建系统还原点
            restore_result = self._create_system_restore_point()
            if not restore_result['success']:
                result['errors'].extend(restore_result['errors'])
            
            # 2. 备份关键文件
            backup_result = self._backup_critical_files()
            if not backup_result['success']:
                result['errors'].extend(backup_result['errors'])
            
            # 3. 启动系统监控
            monitor_result = self._start_system_monitoring()
            if not monitor_result['success']:
                result['errors'].extend(monitor_result['errors'])
            
            # 4. 设置安全限制
            self._setup_safety_limits()
            
            self.logger.info("系统保护已启动")
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"启动系统保护失败: {str(e)}")
        
        return result
    
    def stop_protection(self) -> Dict[str, Any]:
        """停止系统保护"""
        result = {'success': True, 'errors': []}
        
        try:
            # 停止监控
            self.monitoring_active = False
            if self.monitor_thread:
                self.monitor_thread.join(timeout=5)
            
            self.logger.info("系统保护已停止")
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"停止系统保护失败: {str(e)}")
        
        return result
    
    def check_operation_safety(self, operation: str, target_path: str = None) -> Dict[str, Any]:
        """检查操作安全性"""
        result = {
            'safe': True,
            'warnings': [],
            'errors': [],
            'recommendations': []
        }
        
        try:
            # 1. 检查危险操作
            for dangerous_op in self.dangerous_operations:
                if dangerous_op.lower() in operation.lower():
                    result['safe'] = False
                    result['errors'].append(f"检测到危险操作: {dangerous_op}")
            
            # 2. 检查系统关键文件
            if target_path:
                target_path = os.path.abspath(target_path)
                for critical_file in self.critical_system_files:
                    if target_path.lower().startswith(critical_file.lower()):
                        result['safe'] = False
                        result['errors'].append(f"尝试操作系统关键文件: {critical_file}")
            
            # 3. 检查系统资源
            resource_check = self._check_system_resources()
            if not resource_check['safe']:
                result['warnings'].extend(resource_check['warnings'])
                result['recommendations'].extend(resource_check['recommendations'])
            
            # 4. 检查操作频率
            if self.operation_count > self.protection_config['max_file_operations']:
                result['safe'] = False
                result['errors'].append("操作频率过高，可能存在风险")
            
        except Exception as e:
            result['errors'].append(f"安全检查失败: {str(e)}")
        
        return result
    
    def safe_execute(self, operation_func, *args, **kwargs) -> Dict[str, Any]:
        """安全执行操作"""
        result = {'success': True, 'data': None, 'errors': []}
        
        try:
            # 1. 操作前检查
            pre_check = self._pre_operation_check()
            if not pre_check['safe']:
                result['success'] = False
                result['errors'].extend(pre_check['errors'])
                return result
            
            # 2. 设置超时保护
            def timeout_handler():
                time.sleep(self.protection_config['operation_timeout'])
                self.logger.warning("操作超时，可能存在风险")
            
            timeout_thread = threading.Thread(target=timeout_handler, daemon=True)
            timeout_thread.start()
            
            # 3. 执行操作
            start_time = time.time()
            result['data'] = operation_func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # 4. 操作后检查
            post_check = self._post_operation_check(execution_time)
            if not post_check['safe']:
                result['errors'].extend(post_check['warnings'])
            
            self.operation_count += 1
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"安全执行失败: {str(e)}")
        
        return result
    
    def emergency_recovery(self) -> Dict[str, Any]:
        """紧急恢复"""
        result = {'success': True, 'recovered_items': [], 'errors': []}
        
        try:
            self.logger.warning("启动紧急恢复程序")
            
            # 1. 恢复关键文件
            for file_path, backup_data in self.critical_files_backup.items():
                try:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(backup_data)
                    result['recovered_items'].append(file_path)
                except Exception as e:
                    result['errors'].append(f"恢复文件失败 {file_path}: {str(e)}")
            
            # 2. 清理临时文件
            temp_dirs = [
                os.path.expandvars(r"%TEMP%"),
                os.path.expandvars(r"%LOCALAPPDATA%\Temp")
            ]
            
            for temp_dir in temp_dirs:
                try:
                    for item in os.listdir(temp_dir):
                        if 'augment' in item.lower():
                            item_path = os.path.join(temp_dir, item)
                            if os.path.isfile(item_path):
                                os.remove(item_path)
                            elif os.path.isdir(item_path):
                                shutil.rmtree(item_path)
                    result['recovered_items'].append(f"临时目录清理: {temp_dir}")
                except Exception as e:
                    result['errors'].append(f"清理临时目录失败 {temp_dir}: {str(e)}")
            
            # 3. 重置系统服务
            try:
                import subprocess
                services_to_restart = ['Themes', 'AudioSrv', 'BITS']
                
                for service in services_to_restart:
                    try:
                        subprocess.run(['sc', 'stop', service], capture_output=True, timeout=10)
                        time.sleep(1)
                        subprocess.run(['sc', 'start', service], capture_output=True, timeout=10)
                        result['recovered_items'].append(f"服务重启: {service}")
                    except:
                        pass
            except Exception as e:
                result['errors'].append(f"服务重启失败: {str(e)}")
            
            self.logger.info(f"紧急恢复完成，恢复了 {len(result['recovered_items'])} 项")
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"紧急恢复失败: {str(e)}")
        
        return result
    
    def _create_system_restore_point(self) -> Dict[str, Any]:
        """创建系统还原点"""
        result = {'success': True, 'errors': []}
        
        try:
            import subprocess
            
            # 创建系统还原点
            restore_cmd = [
                'powershell', '-Command',
                'Checkpoint-Computer -Description "AugmentNew_SafetyPoint" -RestorePointType "MODIFY_SETTINGS"'
            ]
            
            process = subprocess.run(restore_cmd, capture_output=True, text=True, timeout=60)
            
            if process.returncode == 0:
                self.logger.info("系统还原点创建成功")
            else:
                result['errors'].append("系统还原点创建失败")
                
        except Exception as e:
            result['errors'].append(f"创建系统还原点异常: {str(e)}")
        
        return result
    
    def _backup_critical_files(self) -> Dict[str, Any]:
        """备份关键文件"""
        result = {'success': True, 'errors': []}
        
        try:
            # 备份程序关键配置文件
            config_files = [
                'super_config.json',
                'version.txt'
            ]
            
            for config_file in config_files:
                config_path = Path(config_file)
                if config_path.exists():
                    try:
                        with open(config_path, 'r', encoding='utf-8') as f:
                            self.critical_files_backup[str(config_path)] = f.read()
                    except Exception as e:
                        result['errors'].append(f"备份文件失败 {config_file}: {str(e)}")
            
        except Exception as e:
            result['errors'].append(f"备份关键文件失败: {str(e)}")
        
        return result
    
    def _start_system_monitoring(self) -> Dict[str, Any]:
        """启动系统监控"""
        result = {'success': True, 'errors': []}
        
        try:
            self.monitoring_active = True
            self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitor_thread.start()
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"启动系统监控失败: {str(e)}")
        
        return result
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 检查系统资源
                memory = psutil.virtual_memory()
                cpu = psutil.cpu_percent(interval=1)
                
                if memory.percent > self.protection_config['max_memory_usage']:
                    self.logger.warning(f"内存使用率过高: {memory.percent}%")
                
                if cpu > self.protection_config['max_cpu_usage']:
                    self.logger.warning(f"CPU使用率过高: {cpu}%")
                
                time.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                self.logger.error(f"系统监控异常: {e}")
                time.sleep(10)
    
    def _setup_safety_limits(self):
        """设置安全限制"""
        try:
            # 设置进程优先级为低
            import psutil
            current_process = psutil.Process()
            current_process.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS)
            
        except Exception as e:
            self.logger.warning(f"设置安全限制失败: {e}")
    
    def _check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源"""
        result = {'safe': True, 'warnings': [], 'recommendations': []}
        
        try:
            # 检查内存
            memory = psutil.virtual_memory()
            if memory.percent > self.protection_config['max_memory_usage']:
                result['safe'] = False
                result['warnings'].append(f"内存使用率过高: {memory.percent}%")
                result['recommendations'].append("建议关闭其他程序释放内存")
            
            # 检查磁盘空间
            disk = psutil.disk_usage('/')
            free_space_mb = disk.free / (1024 * 1024)
            if free_space_mb < self.protection_config['min_disk_space']:
                result['safe'] = False
                result['warnings'].append(f"磁盘空间不足: {free_space_mb:.0f}MB")
                result['recommendations'].append("建议清理磁盘空间")
            
        except Exception as e:
            result['warnings'].append(f"资源检查失败: {str(e)}")
        
        return result
    
    def _pre_operation_check(self) -> Dict[str, Any]:
        """操作前检查"""
        return self._check_system_resources()
    
    def _post_operation_check(self, execution_time: float) -> Dict[str, Any]:
        """操作后检查"""
        result = {'safe': True, 'warnings': []}
        
        if execution_time > 30:  # 操作时间超过30秒
            result['warnings'].append(f"操作耗时较长: {execution_time:.2f}秒")
        
        return result
