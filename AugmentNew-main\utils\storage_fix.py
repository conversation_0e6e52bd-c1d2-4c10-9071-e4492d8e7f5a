#!/usr/bin/env python3
"""
VSCode Storage文件错误修复模块
专门解决 "Storage file not found" 错误
"""

import os
import json
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Optional

class StorageFixer:
    """VSCode Storage文件修复器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.user_profile = os.environ.get('USERPROFILE', '')
        self.vscode_paths = self._get_vscode_paths()
        
    def _get_vscode_paths(self) -> Dict[str, str]:
        """获取VSCode相关路径"""
        base_paths = {
            'appdata_roaming': os.path.join(self.user_profile, 'AppData', 'Roaming'),
            'appdata_local': os.path.join(self.user_profile, 'AppData', 'Local'),
        }
        
        return {
            'user_data': os.path.join(base_paths['appdata_roaming'], 'Code', 'User'),
            'global_storage': os.path.join(base_paths['appdata_roaming'], 'Code', 'User', 'globalStorage'),
            'workspace_storage': os.path.join(base_paths['appdata_roaming'], 'Code', 'User', 'workspaceStorage'),
            'extensions': os.path.join(base_paths['appdata_roaming'], 'Code', 'User', 'extensions'),
            'logs': os.path.join(base_paths['appdata_roaming'], 'Code', 'logs'),
            'crash_dumps': os.path.join(base_paths['appdata_roaming'], 'Code', 'CrashDumps'),
            'local_storage': os.path.join(base_paths['appdata_local'], 'Programs', 'Microsoft VS Code'),
            'temp_storage': os.path.join(base_paths['appdata_local'], 'Temp', 'vscode'),
        }
    
    def diagnose_storage_issues(self) -> Dict[str, any]:
        """诊断Storage相关问题"""
        diagnosis = {
            'issues_found': [],
            'missing_files': [],
            'corrupted_files': [],
            'permission_issues': [],
            'recommendations': []
        }
        
        # 检查主要目录是否存在
        for path_name, path_value in self.vscode_paths.items():
            if not os.path.exists(path_value):
                diagnosis['missing_files'].append({
                    'type': 'directory',
                    'name': path_name,
                    'path': path_value,
                    'critical': path_name in ['user_data', 'global_storage']
                })
        
        # 检查storage.json文件
        storage_json_path = os.path.join(self.vscode_paths['global_storage'], 'storage.json')
        if not os.path.exists(storage_json_path):
            diagnosis['missing_files'].append({
                'type': 'file',
                'name': 'storage.json',
                'path': storage_json_path,
                'critical': True
            })
        else:
            # 检查文件是否损坏
            try:
                with open(storage_json_path, 'r', encoding='utf-8') as f:
                    json.load(f)
            except json.JSONDecodeError:
                diagnosis['corrupted_files'].append({
                    'type': 'file',
                    'name': 'storage.json',
                    'path': storage_json_path,
                    'error': 'JSON格式错误'
                })
            except Exception as e:
                diagnosis['permission_issues'].append({
                    'type': 'file',
                    'name': 'storage.json',
                    'path': storage_json_path,
                    'error': str(e)
                })
        
        # 生成修复建议
        if diagnosis['missing_files']:
            diagnosis['recommendations'].append("需要重建缺失的目录和文件")
        if diagnosis['corrupted_files']:
            diagnosis['recommendations'].append("需要修复损坏的配置文件")
        if diagnosis['permission_issues']:
            diagnosis['recommendations'].append("需要修复文件权限问题")
        
        return diagnosis
    
    def create_missing_directories(self) -> Dict[str, any]:
        """创建缺失的目录"""
        result = {
            'success': True,
            'created_dirs': [],
            'errors': []
        }
        
        for path_name, path_value in self.vscode_paths.items():
            try:
                if not os.path.exists(path_value):
                    os.makedirs(path_value, exist_ok=True)
                    result['created_dirs'].append(path_value)
                    self.logger.info(f"创建目录: {path_value}")
            except Exception as e:
                result['success'] = False
                result['errors'].append(f"创建目录失败 {path_value}: {str(e)}")
                self.logger.error(f"创建目录失败 {path_value}: {e}")
        
        return result
    
    def create_default_storage_json(self) -> Dict[str, any]:
        """创建默认的storage.json文件"""
        result = {
            'success': True,
            'created_file': None,
            'errors': []
        }
        
        try:
            storage_json_path = os.path.join(self.vscode_paths['global_storage'], 'storage.json')
            
            # 确保目录存在
            os.makedirs(os.path.dirname(storage_json_path), exist_ok=True)
            
            # 创建默认配置
            default_storage = {
                "telemetry.machineId": self._generate_machine_id(),
                "telemetry.devDeviceId": self._generate_device_id(),
                "storage.version": "1.0.0",
                "workbench.startupEditor": "welcomePage",
                "workbench.colorTheme": "Default Dark+",
                "editor.fontSize": 14,
                "editor.tabSize": 4,
                "files.autoSave": "afterDelay",
                "extensions.autoUpdate": True,
                "update.mode": "default"
            }
            
            # 写入文件
            with open(storage_json_path, 'w', encoding='utf-8') as f:
                json.dump(default_storage, f, indent=2, ensure_ascii=False)
            
            result['created_file'] = storage_json_path
            self.logger.info(f"创建默认storage.json: {storage_json_path}")
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"创建storage.json失败: {str(e)}")
            self.logger.error(f"创建storage.json失败: {e}")
        
        return result
    
    def _generate_machine_id(self) -> str:
        """生成新的机器ID"""
        import uuid
        return str(uuid.uuid4())
    
    def _generate_device_id(self) -> str:
        """生成新的设备ID"""
        import uuid
        return str(uuid.uuid4())
    
    def backup_existing_storage(self, backup_dir: str) -> Dict[str, any]:
        """备份现有的storage文件"""
        result = {
            'success': True,
            'backed_up_files': [],
            'errors': []
        }
        
        try:
            # 创建备份目录
            storage_backup_dir = os.path.join(backup_dir, 'vscode_storage_backup')
            os.makedirs(storage_backup_dir, exist_ok=True)
            
            # 备份globalStorage目录
            global_storage_path = self.vscode_paths['global_storage']
            if os.path.exists(global_storage_path):
                backup_path = os.path.join(storage_backup_dir, 'globalStorage')
                shutil.copytree(global_storage_path, backup_path, ignore_errors=True)
                result['backed_up_files'].append(backup_path)
            
            # 备份workspaceStorage目录
            workspace_storage_path = self.vscode_paths['workspace_storage']
            if os.path.exists(workspace_storage_path):
                backup_path = os.path.join(storage_backup_dir, 'workspaceStorage')
                shutil.copytree(workspace_storage_path, backup_path, ignore_errors=True)
                result['backed_up_files'].append(backup_path)
            
            # 备份用户配置文件
            user_data_path = self.vscode_paths['user_data']
            if os.path.exists(user_data_path):
                for file_name in ['settings.json', 'keybindings.json', 'snippets']:
                    file_path = os.path.join(user_data_path, file_name)
                    if os.path.exists(file_path):
                        backup_path = os.path.join(storage_backup_dir, file_name)
                        if os.path.isfile(file_path):
                            shutil.copy2(file_path, backup_path)
                        else:
                            shutil.copytree(file_path, backup_path, ignore_errors=True)
                        result['backed_up_files'].append(backup_path)
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"备份storage失败: {str(e)}")
            self.logger.error(f"备份storage失败: {e}")
        
        return result
    
    def fix_storage_permissions(self) -> Dict[str, any]:
        """修复storage文件权限"""
        result = {
            'success': True,
            'fixed_paths': [],
            'errors': []
        }
        
        try:
            import stat
            
            for path_name, path_value in self.vscode_paths.items():
                if os.path.exists(path_value):
                    try:
                        # 设置目录权限为可读写
                        if os.path.isdir(path_value):
                            os.chmod(path_value, stat.S_IRWXU | stat.S_IRGRP | stat.S_IXGRP)
                        else:
                            os.chmod(path_value, stat.S_IRUSR | stat.S_IWUSR | stat.S_IRGRP)
                        
                        result['fixed_paths'].append(path_value)
                        
                    except Exception as e:
                        result['errors'].append(f"修复权限失败 {path_value}: {str(e)}")
            
            if result['errors']:
                result['success'] = False
                
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"修复权限失败: {str(e)}")
        
        return result
    
    def comprehensive_storage_fix(self, backup_dir: str = None) -> Dict[str, any]:
        """综合修复storage问题"""
        result = {
            'success': True,
            'steps_completed': [],
            'errors': [],
            'diagnosis': None,
            'backup_result': None,
            'fix_results': []
        }
        
        try:
            # 1. 诊断问题
            result['diagnosis'] = self.diagnose_storage_issues()
            result['steps_completed'].append("问题诊断完成")
            
            # 2. 备份现有数据
            if backup_dir:
                result['backup_result'] = self.backup_existing_storage(backup_dir)
                if result['backup_result']['success']:
                    result['steps_completed'].append("数据备份完成")
                else:
                    result['errors'].extend(result['backup_result']['errors'])
            
            # 3. 创建缺失目录
            dir_result = self.create_missing_directories()
            result['fix_results'].append(dir_result)
            if dir_result['success']:
                result['steps_completed'].append("目录创建完成")
            else:
                result['errors'].extend(dir_result['errors'])
            
            # 4. 创建默认storage.json
            storage_result = self.create_default_storage_json()
            result['fix_results'].append(storage_result)
            if storage_result['success']:
                result['steps_completed'].append("storage.json创建完成")
            else:
                result['errors'].extend(storage_result['errors'])
            
            # 5. 修复权限
            perm_result = self.fix_storage_permissions()
            result['fix_results'].append(perm_result)
            if perm_result['success']:
                result['steps_completed'].append("权限修复完成")
            else:
                result['errors'].extend(perm_result['errors'])
            
            # 判断整体是否成功
            result['success'] = len(result['errors']) == 0
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"综合修复失败: {str(e)}")
            self.logger.error(f"综合修复失败: {e}")
        
        return result
