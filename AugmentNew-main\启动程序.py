#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AugmentNew 简单启动脚本
确保100%能启动程序
"""

import sys
import os
import subprocess

def install_package(package_name):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name, "--quiet"])
        print(f"✅ 已安装: {package_name}")
        return True
    except:
        print(f"❌ 安装失败: {package_name}")
        return False

def check_and_install_dependencies():
    """检查并安装依赖"""
    print("📦 检查依赖包...")
    
    dependencies = {
        'psutil': 'psutil',
        'customtkinter': 'customtkinter',
        'PIL': 'Pillow',
        'requests': 'requests'
    }
    
    missing = []
    
    for import_name, package_name in dependencies.items():
        try:
            __import__(import_name)
            print(f"✅ {package_name} 已安装")
        except ImportError:
            print(f"❌ {package_name} 未安装")
            missing.append(package_name)
    
    if missing:
        print(f"\n📦 正在安装缺失的包: {', '.join(missing)}")
        for package in missing:
            install_package(package)
    
    print("✅ 依赖检查完成\n")

def main():
    """主函数"""
    print("🚀 AugmentNew 简单启动器")
    print("=" * 40)
    
    # 切换到脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"📍 工作目录: {script_dir}")
    
    # 检查Python版本
    print(f"🐍 Python版本: {sys.version}")
    
    # 检查并安装依赖
    check_and_install_dependencies()
    
    # 设置环境变量
    os.environ['AUGMENT_SUPER_MODE'] = '1'
    os.environ['AUGMENT_ALL_FEATURES'] = '1'
    os.environ['AUGMENT_NO_LIMITS'] = '1'
    
    # 启动主程序
    print("🚀 启动AugmentNew主程序...")
    
    try:
        # 方法1: 直接导入并运行
        try:
            import main
            print("✅ 程序启动成功！")
        except Exception as e:
            print(f"❌ 直接导入失败: {e}")
            
            # 方法2: 使用subprocess启动
            print("🔄 尝试备用启动方式...")
            result = subprocess.run([sys.executable, "main.py"], 
                                  capture_output=False, 
                                  text=True)
            
            if result.returncode == 0:
                print("✅ 备用启动成功！")
            else:
                print("❌ 备用启动也失败了")
                
                # 方法3: 启动GUI主程序
                print("🔄 尝试直接启动GUI...")
                result = subprocess.run([sys.executable, "gui_main.py"], 
                                      capture_output=False, 
                                      text=True)
                
                if result.returncode == 0:
                    print("✅ GUI启动成功！")
                else:
                    print("❌ 所有启动方式都失败了")
                    print("请检查程序文件是否完整")
    
    except KeyboardInterrupt:
        print("\n👋 用户中断启动")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("请尝试以下解决方案:")
        print("1. 重新下载程序文件")
        print("2. 检查Python环境")
        print("3. 以管理员身份运行")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
