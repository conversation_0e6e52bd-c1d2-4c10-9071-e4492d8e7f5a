<div align="center">

# <img src="icon.svg" width="28" height="28"> AugmentNew - 完全免费版

**🆓 100% 免费开源 | 一键解锁无限账号登录 | 告别账号锁定烦恼**

[![完全免费](https://img.shields.io/badge/💯-完全免费-00ff00?style=for-the-badge)](https://github.com/alltobebetter/AugmentNew)
[![开源](https://img.shields.io/badge/🔓-开源-blue?style=for-the-badge)](https://github.com/alltobebetter/AugmentNew)
[![Python](https://img.shields.io/badge/Python-3.10+-3776ab?style=for-the-badge&logo=python&logoColor=white)](https://www.python.org/)
[![Tkinter](https://img.shields.io/badge/GUI-CustomTkinter-00599C?style=for-the-badge&logo=python&logoColor=white)](https://github.com/TomSchimansky/CustomTkinter)
[![Platform](https://img.shields.io/badge/Platform-Windows%20|%20macOS%20|%20Linux-lightgrey?style=for-the-badge)](https://github.com/alltobebetter/AugmentNew)

[![GitHub stars](https://img.shields.io/github/stars/alltobebetter/AugmentNew?style=for-the-badge&logo=github)](https://github.com/alltobebetter/AugmentNew)
[![License](https://img.shields.io/badge/license-MIT-blue?style=for-the-badge)](LICENSE)
[![无激活码](https://img.shields.io/badge/🚫-无激活码-red?style=for-the-badge)](https://github.com/alltobebetter/AugmentNew)

**🎯 让你在同一台电脑上无限次使用不同账号登录 AugmentCode**
**💯 永久免费，无需激活码，拒绝收费！**

[📥 下载最新版本](https://github.com/alltobebetter/AugmentNew/releases/latest) | [🐛 报告问题](https://github.com/alltobebetter/AugmentNew/issues) | [💡 功能建议](https://github.com/alltobebetter/AugmentNew/issues/new)

</div>

---

## ✨ 为什么选择 AugmentNew 免费版？

💯 **完全免费** - 永久免费使用，无需激活码，拒绝任何收费！
🔥 **彻底解决账号限制** - 不再因为设备绑定而无法切换账号
🎨 **现代化GUI界面** - 简洁美观，操作直观，小白也能轻松上手
🛡️ **安全可靠** - 自动备份所有数据，支持一键恢复
⚡ **一键操作** - 三步搞定，告别复杂的手动操作
🌍 **跨平台支持** - Windows、macOS、Linux 全平台兼容
🔓 **开源透明** - 源码完全开放，无任何隐藏功能或后门

## 🎯 核心功能

### 🖥️ **智能GUI界面**
- 🎨 现代化深色主题，护眼舒适
- 📊 实时显示系统状态和操作进度
- 📝 详细操作日志，过程透明可控
- 🔄 支持单项操作或一键全清理

### 🔧 **强大的清理能力**
- 🆔 **智能ID重置** - 自动生成新的设备ID和机器ID
- 🗄️ **数据库深度清理** - 彻底清除AugmentCode痕迹
- 💾 **工作区完全重置** - 清理所有缓存和配置文件
- 🗑️ **备份管理** - 一键删除所有历史备份文件

### 🛡️ **安全保障**
- 📦 自动创建备份文件，支持随时恢复
- 🔍 操作前智能检测系统状态
- ⚠️ 友好的错误提示和处理机制
- 🔄 支持版本自动更新检查

## 🚀 快速开始

### 方式一：直接下载可执行文件并一键运行（推荐）
1. [📥 下载最新版 AugmentNew.exe](https://github.com/alltobebetter/AugmentNew/releases/latest)
2. 右键选择"以管理员身份运行"
3. 按照界面提示操作即可

### 方式二：从源码运行
```bash
# 克隆项目
git clone https://github.com/alltobebetter/AugmentNew.git
cd AugmentNew

# 安装依赖
pip install -r requirements.txt

# 启动GUI版本
python gui_main.py
```

## 📖 使用教程

### 🎯 三步搞定账号切换

1. **🚪 退出准备**
   - 关闭 VS Code

2. **🔧 一键清理**
   - 运行 AugmentNew
   - 点击"🚀 一键清理全部"
   - 等待操作完成

3. **🎉 重新登录**
   - 重启 VS Code
   - 使用新邮箱登录 AugmentCode

### 🛠️ 高级操作

- **🔄 修改Telemetry ID** - 仅重置设备标识
- **🗃️ 清理数据库** - 仅清理数据库记录  
- **💾 清理工作区** - 仅清理工作区文件
- **🗑️ 删除备份** - 清理历史备份文件

## 🔧 技术特性

- **🐍 Python 3.10+** - 现代化的Python技术栈
- **🎨 CustomTkinter** - 美观的现代化GUI框架
- **🔒 管理员权限** - 确保有足够权限进行系统操作
- **📦 单文件分发** - 无需安装，下载即用
- **🌐 网络更新** - 自动检查和提示版本更新

## 🤝 支持我们

如果这个项目对你有帮助，请考虑：

- ⭐ 给项目点个 Star
- 🐛 报告 Bug 或提出改进建议
- 💖 [赞助我们](https://i.gyazo.com/e3009f0ffc36a8444944f3ca40378dd1.png) 支持项目发展
- 📢 分享给更多需要的朋友

## ⚠️ 免责声明

- 本工具仅供学习和研究使用
- 使用前请确保遵守相关服务条款
- 使用风险自负，开发者不承担任何责任
- 建议在使用前备份重要数据

## 📄 开源协议

本项目采用 [MIT License](LICENSE) 开源协议

## 🙏 致谢

基于开源项目 [free-augmentcode](https://github.com/vber/free-augmentcode) 二次开发，请尊重原作者权益

---

<div align="center">

**🌟 如果觉得有用，别忘了给个 Star 哦！🌟**

Made with ❤️ by [alltobebetter](https://github.com/alltobebetter)

</div>
