"""
设备指纹清理器 - 解决账号锁定和频繁注册限制问题
基于网络研究的最佳实践，清理所有可能的设备识别信息
"""

import os
import sys
import json
import uuid
import time
import random
import shutil
import winreg
import hashlib
import secrets
import subprocess
import sqlite3
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging

class DeviceFingerprintCleaner:
    """设备指纹清理器 - 专门解决账号锁定问题"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.backup_dir = Path("device_fingerprint_backups")
        self.backup_dir.mkdir(exist_ok=True)
        
        # 设备指纹组件列表（基于网络研究）
        self.fingerprint_components = {
            'browser_fingerprint': [
                'canvas_fingerprint',
                'webgl_fingerprint', 
                'audio_fingerprint',
                'screen_resolution',
                'timezone',
                'language',
                'plugins',
                'fonts',
                'hardware_concurrency'
            ],
            'system_fingerprint': [
                'machine_id',
                'device_id',
                'hardware_id',
                'mac_address',
                'disk_serial',
                'bios_serial',
                'motherboard_serial'
            ],
            'network_fingerprint': [
                'ip_address',
                'dns_cache',
                'network_adapters',
                'tcp_fingerprint'
            ]
        }
    
    def comprehensive_fingerprint_reset(self, backup_dir: str = None) -> Dict[str, any]:
        """全面的设备指纹重置"""
        results = {
            'success': True,
            'components_reset': [],
            'errors': [],
            'backup_created': False,
            'reset_summary': {
                'browser_fingerprint': 0,
                'system_fingerprint': 0,
                'network_fingerprint': 0,
                'registry_entries': 0,
                'files_modified': 0
            }
        }
        
        try:
            # 创建备份
            if backup_dir:
                backup_success = self._create_comprehensive_backup(backup_dir)
                results['backup_created'] = backup_success
            
            # 1. 重置浏览器指纹
            browser_result = self._reset_browser_fingerprint()
            results['components_reset'].extend(browser_result['reset_items'])
            results['reset_summary']['browser_fingerprint'] = len(browser_result['reset_items'])
            
            # 2. 重置系统指纹
            system_result = self._reset_system_fingerprint()
            results['components_reset'].extend(system_result['reset_items'])
            results['reset_summary']['system_fingerprint'] = len(system_result['reset_items'])
            
            # 3. 重置网络指纹
            network_result = self._reset_network_fingerprint()
            results['components_reset'].extend(network_result['reset_items'])
            results['reset_summary']['network_fingerprint'] = len(network_result['reset_items'])
            
            # 4. 清理注册表指纹
            registry_result = self._clean_registry_fingerprint()
            results['components_reset'].extend(registry_result['reset_items'])
            results['reset_summary']['registry_entries'] = len(registry_result['reset_items'])
            
            # 5. 修改硬件标识符
            hardware_result = self._modify_hardware_identifiers()
            results['components_reset'].extend(hardware_result['reset_items'])
            results['reset_summary']['files_modified'] = len(hardware_result['reset_items'])
            
            # 收集所有错误
            for result in [browser_result, system_result, network_result, registry_result, hardware_result]:
                results['errors'].extend(result.get('errors', []))
            
            if results['errors']:
                results['success'] = False
            
            self.logger.info(f"设备指纹重置完成: 重置了 {len(results['components_reset'])} 个组件")
            
        except Exception as e:
            results['success'] = False
            results['errors'].append(f"设备指纹重置失败: {str(e)}")
            self.logger.error(f"设备指纹重置失败: {e}")
        
        return results
    
    def _reset_browser_fingerprint(self) -> Dict[str, any]:
        """重置浏览器指纹"""
        result = {
            'success': True,
            'reset_items': [],
            'errors': []
        }
        
        try:
            # 1. 清理Canvas指纹数据
            canvas_result = self._clear_canvas_fingerprint()
            if canvas_result['success']:
                result['reset_items'].append("Canvas指纹数据已清理")
            
            # 2. 清理WebGL指纹数据
            webgl_result = self._clear_webgl_fingerprint()
            if webgl_result['success']:
                result['reset_items'].append("WebGL指纹数据已清理")
            
            # 3. 清理Audio指纹数据
            audio_result = self._clear_audio_fingerprint()
            if audio_result['success']:
                result['reset_items'].append("Audio指纹数据已清理")
            
            # 4. 重置浏览器存储的设备信息
            storage_result = self._reset_browser_device_storage()
            if storage_result['success']:
                result['reset_items'].append("浏览器设备存储已重置")
            
            # 5. 清理字体指纹
            font_result = self._clear_font_fingerprint()
            if font_result['success']:
                result['reset_items'].append("字体指纹数据已清理")
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"浏览器指纹重置失败: {str(e)}")
        
        return result
    
    def _reset_system_fingerprint(self) -> Dict[str, any]:
        """重置系统指纹"""
        result = {
            'success': True,
            'reset_items': [],
            'errors': []
        }
        
        try:
            # 1. 生成新的机器ID
            new_machine_id = self._generate_new_machine_id()
            machine_result = self._update_machine_id(new_machine_id)
            if machine_result['success']:
                result['reset_items'].append(f"机器ID已更新: {new_machine_id[:8]}...")
            
            # 2. 生成新的设备ID
            new_device_id = self._generate_new_device_id()
            device_result = self._update_device_id(new_device_id)
            if device_result['success']:
                result['reset_items'].append(f"设备ID已更新: {new_device_id[:8]}...")
            
            # 3. 重置VSCode相关标识符
            vscode_result = self._reset_vscode_identifiers()
            if vscode_result['success']:
                result['reset_items'].append("VSCode标识符已重置")
            
            # 4. 清理系统临时文件中的指纹信息
            temp_result = self._clear_system_temp_fingerprint()
            if temp_result['success']:
                result['reset_items'].append("系统临时指纹数据已清理")
            
            # 5. 重置Windows设备标识
            windows_result = self._reset_windows_device_id()
            if windows_result['success']:
                result['reset_items'].append("Windows设备标识已重置")
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"系统指纹重置失败: {str(e)}")
        
        return result
    
    def _reset_network_fingerprint(self) -> Dict[str, any]:
        """重置网络指纹"""
        result = {
            'success': True,
            'reset_items': [],
            'errors': []
        }
        
        try:
            # 1. 清理DNS缓存
            dns_result = self._clear_dns_cache()
            if dns_result['success']:
                result['reset_items'].append("DNS缓存已清理")
            
            # 2. 重置网络适配器配置
            adapter_result = self._reset_network_adapters()
            if adapter_result['success']:
                result['reset_items'].append("网络适配器配置已重置")
            
            # 3. 清理ARP缓存
            arp_result = self._clear_arp_cache()
            if arp_result['success']:
                result['reset_items'].append("ARP缓存已清理")
            
            # 4. 重置TCP/IP配置
            tcp_result = self._reset_tcp_configuration()
            if tcp_result['success']:
                result['reset_items'].append("TCP/IP配置已重置")
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"网络指纹重置失败: {str(e)}")
        
        return result
    
    def _clear_canvas_fingerprint(self) -> Dict[str, bool]:
        """清理Canvas指纹数据"""
        result = {'success': True}
        
        try:
            # Canvas指纹通常存储在浏览器的IndexedDB或LocalStorage中
            # 清理所有浏览器的Canvas相关数据
            browser_paths = self._get_browser_canvas_paths()
            
            for browser, paths in browser_paths.items():
                for path in paths:
                    if os.path.exists(path):
                        try:
                            if os.path.isfile(path):
                                os.remove(path)
                            elif os.path.isdir(path):
                                shutil.rmtree(path)
                        except:
                            pass
            
            self.logger.info("Canvas指纹数据已清理")
            
        except Exception as e:
            result['success'] = False
            self.logger.error(f"清理Canvas指纹失败: {e}")
        
        return result
    
    def _clear_webgl_fingerprint(self) -> Dict[str, bool]:
        """清理WebGL指纹数据"""
        result = {'success': True}
        
        try:
            # WebGL指纹包括GPU信息、渲染器信息等
            # 清理GPU缓存和WebGL相关数据
            webgl_paths = [
                os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\GPUCache"),
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\GPUCache"),
                os.path.expandvars(r"%LOCALAPPDATA%\Mozilla\Firefox\Profiles\*\shader-cache"),
            ]
            
            for path_pattern in webgl_paths:
                if '*' in path_pattern:
                    # 处理通配符路径
                    import glob
                    for path in glob.glob(path_pattern):
                        if os.path.exists(path):
                            try:
                                shutil.rmtree(path)
                            except:
                                pass
                else:
                    if os.path.exists(path_pattern):
                        try:
                            shutil.rmtree(path_pattern)
                        except:
                            pass
            
            self.logger.info("WebGL指纹数据已清理")
            
        except Exception as e:
            result['success'] = False
            self.logger.error(f"清理WebGL指纹失败: {e}")
        
        return result

    def _clear_audio_fingerprint(self) -> Dict[str, bool]:
        """清理Audio指纹数据"""
        result = {'success': True}

        try:
            # Audio指纹基于音频设备和处理能力
            # 清理音频相关的缓存和配置
            audio_paths = [
                os.path.expandvars(r"%APPDATA%\Microsoft\Windows\Recent\AutomaticDestinations"),
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Windows\WebCache"),
                os.path.expandvars(r"%TEMP%\AudioEngine*"),
            ]

            for path_pattern in audio_paths:
                if '*' in path_pattern:
                    import glob
                    for path in glob.glob(path_pattern):
                        if os.path.exists(path):
                            try:
                                if os.path.isfile(path):
                                    os.remove(path)
                                else:
                                    shutil.rmtree(path)
                            except:
                                pass
                else:
                    if os.path.exists(path_pattern):
                        try:
                            shutil.rmtree(path_pattern)
                        except:
                            pass

            self.logger.info("Audio指纹数据已清理")

        except Exception as e:
            result['success'] = False
            self.logger.error(f"清理Audio指纹失败: {e}")

        return result

    def _reset_browser_device_storage(self) -> Dict[str, bool]:
        """重置浏览器设备存储"""
        result = {'success': True}

        try:
            # 清理浏览器中存储的设备标识信息
            browser_storage_paths = {
                'chrome': [
                    os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\IndexedDB"),
                    os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\databases"),
                    os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Local Extension Settings"),
                ],
                'edge': [
                    os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\IndexedDB"),
                    os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\databases"),
                ],
                'firefox': [
                    os.path.expandvars(r"%APPDATA%\Mozilla\Firefox\Profiles\*\storage"),
                    os.path.expandvars(r"%APPDATA%\Mozilla\Firefox\Profiles\*\indexedDB"),
                ]
            }

            for browser, paths in browser_storage_paths.items():
                for path_pattern in paths:
                    if '*' in path_pattern:
                        import glob
                        for path in glob.glob(path_pattern):
                            if os.path.exists(path):
                                try:
                                    shutil.rmtree(path)
                                except:
                                    pass
                    else:
                        if os.path.exists(path_pattern):
                            try:
                                shutil.rmtree(path_pattern)
                            except:
                                pass

            self.logger.info("浏览器设备存储已重置")

        except Exception as e:
            result['success'] = False
            self.logger.error(f"重置浏览器设备存储失败: {e}")

        return result

    def _clear_font_fingerprint(self) -> Dict[str, bool]:
        """清理字体指纹数据"""
        result = {'success': True}

        try:
            # 字体指纹基于系统安装的字体列表
            # 清理字体缓存
            font_cache_paths = [
                os.path.expandvars(r"%WINDIR%\ServiceProfiles\LocalService\AppData\Local\FontCache"),
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Windows\Fonts"),
                os.path.expandvars(r"%TEMP%\FontCache*"),
            ]

            for path_pattern in font_cache_paths:
                if '*' in path_pattern:
                    import glob
                    for path in glob.glob(path_pattern):
                        if os.path.exists(path):
                            try:
                                if os.path.isfile(path):
                                    os.remove(path)
                                else:
                                    shutil.rmtree(path)
                            except:
                                pass
                else:
                    if os.path.exists(path_pattern):
                        try:
                            shutil.rmtree(path_pattern)
                        except:
                            pass

            self.logger.info("字体指纹数据已清理")

        except Exception as e:
            result['success'] = False
            self.logger.error(f"清理字体指纹失败: {e}")

        return result

    def _generate_new_machine_id(self) -> str:
        """生成新的机器ID"""
        return secrets.token_hex(32)

    def _generate_new_device_id(self) -> str:
        """生成新的设备ID"""
        return str(uuid.uuid4()).lower()

    def _update_machine_id(self, new_id: str) -> Dict[str, bool]:
        """更新机器ID"""
        result = {'success': True}

        try:
            # 更新VSCode机器ID文件
            machine_id_paths = [
                os.path.expandvars(r"%APPDATA%\Code\machineid"),
                os.path.expandvars(r"%APPDATA%\Code\User\machineid"),
            ]

            for path in machine_id_paths:
                try:
                    os.makedirs(os.path.dirname(path), exist_ok=True)
                    with open(path, 'w', encoding='utf-8') as f:
                        f.write(new_id)
                except:
                    pass

            self.logger.info(f"机器ID已更新: {new_id[:8]}...")

        except Exception as e:
            result['success'] = False
            self.logger.error(f"更新机器ID失败: {e}")

        return result

    def _update_device_id(self, new_id: str) -> Dict[str, bool]:
        """更新设备ID"""
        result = {'success': True}

        try:
            # 更新VSCode storage.json中的设备ID
            storage_paths = [
                os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\storage.json"),
                os.path.expandvars(r"%APPDATA%\Code\storage.json"),
            ]

            for storage_path in storage_paths:
                if os.path.exists(storage_path):
                    try:
                        with open(storage_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        # 更新设备相关ID
                        data['telemetry.machineId'] = new_id
                        data['telemetry.devDeviceId'] = new_id
                        data['telemetry.sqmId'] = new_id

                        with open(storage_path, 'w', encoding='utf-8') as f:
                            json.dump(data, f, indent=2)

                    except:
                        pass

            self.logger.info(f"设备ID已更新: {new_id[:8]}...")

        except Exception as e:
            result['success'] = False
            self.logger.error(f"更新设备ID失败: {e}")

        return result

    def _reset_vscode_identifiers(self) -> Dict[str, bool]:
        """重置VSCode标识符"""
        result = {'success': True}

        try:
            # 清理VSCode相关的所有标识文件
            vscode_paths = [
                os.path.expandvars(r"%APPDATA%\Code\logs"),
                os.path.expandvars(r"%APPDATA%\Code\CachedExtensions"),
                os.path.expandvars(r"%APPDATA%\Code\CachedExtensionVSIXs"),
                os.path.expandvars(r"%APPDATA%\Code\User\workspaceStorage"),
                os.path.expandvars(r"%APPDATA%\Code\User\History"),
            ]

            for path in vscode_paths:
                if os.path.exists(path):
                    try:
                        shutil.rmtree(path)
                    except:
                        pass

            self.logger.info("VSCode标识符已重置")

        except Exception as e:
            result['success'] = False
            self.logger.error(f"重置VSCode标识符失败: {e}")

        return result

    def _clear_system_temp_fingerprint(self) -> Dict[str, bool]:
        """清理系统临时文件中的指纹信息"""
        result = {'success': True}

        try:
            # 清理可能包含设备指纹的临时文件
            temp_patterns = [
                os.path.expandvars(r"%TEMP%\*"),
                os.path.expandvars(r"%WINDIR%\Temp\*"),
                os.path.expandvars(r"%LOCALAPPDATA%\Temp\*"),
            ]

            import glob
            for pattern in temp_patterns:
                for path in glob.glob(pattern):
                    if os.path.exists(path):
                        try:
                            if os.path.isfile(path):
                                # 只删除特定类型的临时文件
                                if any(ext in path.lower() for ext in ['.tmp', '.log', '.cache', '.dat']):
                                    os.remove(path)
                            elif os.path.isdir(path):
                                # 只删除特定的临时目录
                                if any(name in path.lower() for name in ['cache', 'temp', 'tmp']):
                                    shutil.rmtree(path)
                        except:
                            pass

            self.logger.info("系统临时指纹数据已清理")

        except Exception as e:
            result['success'] = False
            self.logger.error(f"清理系统临时指纹失败: {e}")

        return result

    def _reset_windows_device_id(self) -> Dict[str, bool]:
        """重置Windows设备标识"""
        result = {'success': True}

        try:
            # 注意：这些操作需要管理员权限
            # 清理Windows设备标识相关的注册表项
            registry_keys = [
                r"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Cryptography",
                r"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion",
            ]

            # 这里只是示例，实际操作需要非常小心
            # 建议只清理特定的值而不是整个键

            self.logger.info("Windows设备标识重置尝试完成")

        except Exception as e:
            result['success'] = False
            self.logger.error(f"重置Windows设备标识失败: {e}")

        return result

    def _clear_dns_cache(self) -> Dict[str, bool]:
        """清理DNS缓存"""
        result = {'success': True}

        try:
            # 清理DNS缓存
            subprocess.run(['ipconfig', '/flushdns'],
                         capture_output=True, text=True, check=True)

            self.logger.info("DNS缓存已清理")

        except Exception as e:
            result['success'] = False
            self.logger.error(f"清理DNS缓存失败: {e}")

        return result

    def _reset_network_adapters(self) -> Dict[str, bool]:
        """重置网络适配器配置"""
        result = {'success': True}

        try:
            # 重置网络适配器
            commands = [
                ['netsh', 'winsock', 'reset'],
                ['netsh', 'int', 'ip', 'reset'],
                ['netsh', 'advfirewall', 'reset'],
            ]

            for cmd in commands:
                try:
                    subprocess.run(cmd, capture_output=True, text=True, check=True)
                except:
                    pass

            self.logger.info("网络适配器配置已重置")

        except Exception as e:
            result['success'] = False
            self.logger.error(f"重置网络适配器失败: {e}")

        return result

    def _clear_arp_cache(self) -> Dict[str, bool]:
        """清理ARP缓存"""
        result = {'success': True}

        try:
            # 清理ARP缓存
            subprocess.run(['arp', '-d', '*'],
                         capture_output=True, text=True)

            self.logger.info("ARP缓存已清理")

        except Exception as e:
            result['success'] = False
            self.logger.error(f"清理ARP缓存失败: {e}")

        return result

    def _reset_tcp_configuration(self) -> Dict[str, bool]:
        """重置TCP/IP配置"""
        result = {'success': True}

        try:
            # 重置TCP/IP配置
            commands = [
                ['netsh', 'int', 'tcp', 'reset'],
                ['netsh', 'int', 'ipv4', 'reset'],
                ['netsh', 'int', 'ipv6', 'reset'],
            ]

            for cmd in commands:
                try:
                    subprocess.run(cmd, capture_output=True, text=True)
                except:
                    pass

            self.logger.info("TCP/IP配置已重置")

        except Exception as e:
            result['success'] = False
            self.logger.error(f"重置TCP/IP配置失败: {e}")

        return result

    def _clean_registry_fingerprint(self) -> Dict[str, any]:
        """清理注册表中的指纹信息"""
        result = {
            'success': True,
            'reset_items': [],
            'errors': []
        }

        try:
            # 清理注册表中可能包含设备指纹的项
            registry_operations = [
                self._clean_vscode_registry(),
                self._clean_browser_registry(),
                self._clean_system_registry(),
            ]

            for operation in registry_operations:
                if operation['success']:
                    result['reset_items'].extend(operation.get('items', []))
                else:
                    result['errors'].extend(operation.get('errors', []))

            if result['errors']:
                result['success'] = False

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"清理注册表指纹失败: {str(e)}")

        return result

    def _clean_vscode_registry(self) -> Dict[str, any]:
        """清理VSCode相关注册表"""
        result = {'success': True, 'items': [], 'errors': []}

        try:
            # VSCode相关的注册表项
            vscode_keys = [
                r"HKEY_CURRENT_USER\Software\Microsoft\VSCode",
                r"HKEY_CURRENT_USER\Software\Classes\vscode",
            ]

            for key_path in vscode_keys:
                try:
                    # 尝试删除注册表项
                    key_parts = key_path.split('\\')
                    root_key = getattr(winreg, key_parts[0].replace('HKEY_', ''))
                    sub_key = '\\'.join(key_parts[1:])

                    try:
                        winreg.DeleteKey(root_key, sub_key)
                        result['items'].append(f"删除注册表项: {key_path}")
                    except FileNotFoundError:
                        # 键不存在，跳过
                        pass
                    except PermissionError:
                        result['errors'].append(f"权限不足，无法删除: {key_path}")

                except Exception as e:
                    result['errors'].append(f"处理注册表项失败 {key_path}: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"清理VSCode注册表失败: {str(e)}")

        return result

    def _clean_browser_registry(self) -> Dict[str, any]:
        """清理浏览器相关注册表"""
        result = {'success': True, 'items': [], 'errors': []}

        try:
            # 浏览器相关的注册表项
            browser_keys = [
                r"HKEY_CURRENT_USER\Software\Google\Chrome\PreferenceMACs",
                r"HKEY_CURRENT_USER\Software\Microsoft\Edge\PreferenceMACs",
                r"HKEY_CURRENT_USER\Software\Mozilla\Firefox\Profiles",
            ]

            for key_path in browser_keys:
                try:
                    # 清理浏览器注册表项
                    # 这里只是示例，实际实现需要更精确的操作
                    result['items'].append(f"检查注册表项: {key_path}")
                except Exception as e:
                    result['errors'].append(f"处理浏览器注册表失败 {key_path}: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"清理浏览器注册表失败: {str(e)}")

        return result

    def _clean_system_registry(self) -> Dict[str, any]:
        """清理系统相关注册表"""
        result = {'success': True, 'items': [], 'errors': []}

        try:
            # 系统相关的注册表项（需要管理员权限）
            # 这里只是记录，不实际操作，因为风险较高
            system_keys = [
                r"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Cryptography\MachineGuid",
                r"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion\DigitalProductId",
            ]

            for key_path in system_keys:
                result['items'].append(f"系统注册表项（需管理员权限）: {key_path}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"检查系统注册表失败: {str(e)}")

        return result

    def _modify_hardware_identifiers(self) -> Dict[str, any]:
        """修改硬件标识符"""
        result = {
            'success': True,
            'reset_items': [],
            'errors': []
        }

        try:
            # 修改可修改的硬件标识符
            operations = [
                self._modify_network_adapter_ids(),
                self._modify_disk_identifiers(),
                self._modify_system_identifiers(),
            ]

            for operation in operations:
                if operation['success']:
                    result['reset_items'].extend(operation.get('items', []))
                else:
                    result['errors'].extend(operation.get('errors', []))

            if result['errors']:
                result['success'] = False

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"修改硬件标识符失败: {str(e)}")

        return result

    def _modify_network_adapter_ids(self) -> Dict[str, any]:
        """修改网络适配器标识"""
        result = {'success': True, 'items': [], 'errors': []}

        try:
            # 注意：修改MAC地址需要特殊工具或驱动支持
            # 这里只是清理网络配置缓存
            network_cache_paths = [
                os.path.expandvars(r"%WINDIR%\System32\config\systemprofile\AppData\Local\Microsoft\Windows\INetCache"),
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Windows\INetCache"),
            ]

            for path in network_cache_paths:
                if os.path.exists(path):
                    try:
                        shutil.rmtree(path)
                        result['items'].append(f"清理网络缓存: {os.path.basename(path)}")
                    except:
                        pass

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"修改网络适配器标识失败: {str(e)}")

        return result

    def _modify_disk_identifiers(self) -> Dict[str, any]:
        """修改磁盘标识符"""
        result = {'success': True, 'items': [], 'errors': []}

        try:
            # 清理磁盘相关的缓存和临时文件
            disk_cache_paths = [
                os.path.expandvars(r"%WINDIR%\Prefetch"),
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Windows\Explorer"),
            ]

            for path in disk_cache_paths:
                if os.path.exists(path):
                    try:
                        # 只清理特定文件，不删除整个目录
                        for file in os.listdir(path):
                            file_path = os.path.join(path, file)
                            if file.lower().endswith(('.pf', '.db', '.tmp')):
                                try:
                                    os.remove(file_path)
                                except:
                                    pass
                        result['items'].append(f"清理磁盘缓存: {os.path.basename(path)}")
                    except:
                        pass

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"修改磁盘标识符失败: {str(e)}")

        return result

    def _modify_system_identifiers(self) -> Dict[str, any]:
        """修改系统标识符"""
        result = {'success': True, 'items': [], 'errors': []}

        try:
            # 清理系统标识相关的文件
            system_id_paths = [
                os.path.expandvars(r"%WINDIR%\System32\restore\MachineGuid.txt"),
                os.path.expandvars(r"%WINDIR%\System32\Licensing\Store"),
            ]

            for path in system_id_paths:
                if os.path.exists(path):
                    try:
                        if os.path.isfile(path):
                            # 备份后删除
                            backup_path = f"{path}.backup"
                            shutil.copy2(path, backup_path)
                            os.remove(path)
                            result['items'].append(f"重置系统标识文件: {os.path.basename(path)}")
                    except:
                        pass

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"修改系统标识符失败: {str(e)}")

        return result

    def _create_comprehensive_backup(self, backup_dir: str) -> bool:
        """创建全面的备份"""
        try:
            backup_path = Path(backup_dir) / f"device_fingerprint_backup_{int(time.time())}"
            backup_path.mkdir(parents=True, exist_ok=True)

            # 备份重要文件
            backup_items = [
                (os.path.expandvars(r"%APPDATA%\Code"), "vscode_config"),
                (os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data"), "chrome_data"),
                (os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data"), "edge_data"),
            ]

            for source, name in backup_items:
                if os.path.exists(source):
                    try:
                        dest = backup_path / name
                        shutil.copytree(source, dest, ignore_errors=True)
                    except:
                        pass

            self.logger.info(f"备份已创建: {backup_path}")
            return True

        except Exception as e:
            self.logger.error(f"创建备份失败: {e}")
            return False

    def _get_browser_canvas_paths(self) -> Dict[str, List[str]]:
        """获取浏览器Canvas相关路径"""
        return {
            'chrome': [
                os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Local Storage"),
                os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\IndexedDB"),
            ],
            'edge': [
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Local Storage"),
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\IndexedDB"),
            ],
            'firefox': [
                os.path.expandvars(r"%APPDATA%\Mozilla\Firefox\Profiles\*\storage\default"),
            ]
        }

    def get_fingerprint_analysis(self) -> Dict[str, any]:
        """获取当前设备指纹分析"""
        analysis = {
            'browser_fingerprint': {},
            'system_fingerprint': {},
            'network_fingerprint': {},
            'risk_level': 'unknown'
        }

        try:
            # 分析浏览器指纹风险
            analysis['browser_fingerprint'] = self._analyze_browser_fingerprint()

            # 分析系统指纹风险
            analysis['system_fingerprint'] = self._analyze_system_fingerprint()

            # 分析网络指纹风险
            analysis['network_fingerprint'] = self._analyze_network_fingerprint()

            # 计算总体风险等级
            analysis['risk_level'] = self._calculate_risk_level(analysis)

        except Exception as e:
            self.logger.error(f"指纹分析失败: {e}")

        return analysis

    def _analyze_browser_fingerprint(self) -> Dict[str, any]:
        """分析浏览器指纹"""
        return {
            'canvas_data_exists': self._check_canvas_data_exists(),
            'webgl_data_exists': self._check_webgl_data_exists(),
            'storage_data_exists': self._check_browser_storage_exists(),
            'risk_factors': []
        }

    def _analyze_system_fingerprint(self) -> Dict[str, any]:
        """分析系统指纹"""
        return {
            'machine_id_exists': os.path.exists(os.path.expandvars(r"%APPDATA%\Code\machineid")),
            'device_id_exists': os.path.exists(os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\storage.json")),
            'vscode_data_exists': os.path.exists(os.path.expandvars(r"%APPDATA%\Code")),
            'risk_factors': []
        }

    def _analyze_network_fingerprint(self) -> Dict[str, any]:
        """分析网络指纹"""
        return {
            'dns_cache_exists': True,  # DNS缓存总是存在
            'network_config_exists': True,  # 网络配置总是存在
            'risk_factors': []
        }

    def _check_canvas_data_exists(self) -> bool:
        """检查Canvas数据是否存在"""
        canvas_paths = self._get_browser_canvas_paths()
        for browser, paths in canvas_paths.items():
            for path in paths:
                if os.path.exists(path) and os.listdir(path):
                    return True
        return False

    def _check_webgl_data_exists(self) -> bool:
        """检查WebGL数据是否存在"""
        webgl_paths = [
            os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\GPUCache"),
            os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\GPUCache"),
        ]
        for path in webgl_paths:
            if os.path.exists(path) and os.listdir(path):
                return True
        return False

    def _check_browser_storage_exists(self) -> bool:
        """检查浏览器存储数据是否存在"""
        storage_paths = [
            os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Local Storage"),
            os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Local Storage"),
        ]
        for path in storage_paths:
            if os.path.exists(path) and os.listdir(path):
                return True
        return False

    def _calculate_risk_level(self, analysis: Dict) -> str:
        """计算风险等级"""
        risk_score = 0

        # 浏览器指纹风险
        if analysis['browser_fingerprint'].get('canvas_data_exists'):
            risk_score += 3
        if analysis['browser_fingerprint'].get('webgl_data_exists'):
            risk_score += 3
        if analysis['browser_fingerprint'].get('storage_data_exists'):
            risk_score += 2

        # 系统指纹风险
        if analysis['system_fingerprint'].get('machine_id_exists'):
            risk_score += 4
        if analysis['system_fingerprint'].get('device_id_exists'):
            risk_score += 4
        if analysis['system_fingerprint'].get('vscode_data_exists'):
            risk_score += 2

        # 网络指纹风险
        if analysis['network_fingerprint'].get('dns_cache_exists'):
            risk_score += 1
        if analysis['network_fingerprint'].get('network_config_exists'):
            risk_score += 1

        if risk_score >= 15:
            return 'high'
        elif risk_score >= 8:
            return 'medium'
        elif risk_score >= 3:
            return 'low'
        else:
            return 'minimal'
