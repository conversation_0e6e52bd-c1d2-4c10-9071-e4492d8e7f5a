#!/usr/bin/env python3
"""
GitHub集成增强清理模块
提供更多GitHub相关的清理方法和账号管理功能
"""

import os
import json
import shutil
import requests
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, List, Optional
import logging

class GitHubEnhancedCleaner:
    """GitHub增强清理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.user_profile = os.environ.get('USERPROFILE', '')
        self.git_config_paths = self._get_git_config_paths()
        
    def _get_git_config_paths(self) -> Dict[str, str]:
        """获取Git配置文件路径"""
        return {
            'global_config': os.path.join(self.user_profile, '.gitconfig'),
            'ssh_dir': os.path.join(self.user_profile, '.ssh'),
            'git_credentials': os.path.join(self.user_profile, '.git-credentials'),
            'github_cli_config': os.path.join(self.user_profile, '.config', 'gh'),
            'vscode_git_cache': os.path.join(self.user_profile, 'AppData', 'Roaming', 'Code', 'User', 'globalStorage', 'vscode.git'),
        }
    
    def detect_github_accounts(self) -> Dict[str, any]:
        """检测当前配置的GitHub账号信息"""
        accounts = {
            'git_config': {},
            'ssh_keys': [],
            'stored_credentials': [],
            'github_cli': {},
            'vscode_accounts': []
        }
        
        try:
            # 检查Git全局配置
            git_config_path = self.git_config_paths['global_config']
            if os.path.exists(git_config_path):
                accounts['git_config'] = self._parse_git_config(git_config_path)
            
            # 检查SSH密钥
            ssh_dir = self.git_config_paths['ssh_dir']
            if os.path.exists(ssh_dir):
                accounts['ssh_keys'] = self._list_ssh_keys(ssh_dir)
            
            # 检查存储的凭据
            credentials_path = self.git_config_paths['git_credentials']
            if os.path.exists(credentials_path):
                accounts['stored_credentials'] = self._parse_git_credentials(credentials_path)
            
            # 检查GitHub CLI配置
            gh_config_dir = self.git_config_paths['github_cli_config']
            if os.path.exists(gh_config_dir):
                accounts['github_cli'] = self._parse_github_cli_config(gh_config_dir)
            
            # 检查VSCode中的GitHub账号
            vscode_git_cache = self.git_config_paths['vscode_git_cache']
            if os.path.exists(vscode_git_cache):
                accounts['vscode_accounts'] = self._parse_vscode_git_accounts(vscode_git_cache)
                
        except Exception as e:
            self.logger.error(f"检测GitHub账号失败: {e}")
        
        return accounts
    
    def _parse_git_config(self, config_path: str) -> Dict[str, str]:
        """解析Git配置文件"""
        config = {}
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 简单解析用户信息
            lines = content.split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('name ='):
                    config['name'] = line.split('=', 1)[1].strip()
                elif line.startswith('email ='):
                    config['email'] = line.split('=', 1)[1].strip()
                    
        except Exception as e:
            self.logger.error(f"解析Git配置失败: {e}")
        
        return config
    
    def _list_ssh_keys(self, ssh_dir: str) -> List[Dict[str, str]]:
        """列出SSH密钥"""
        keys = []
        try:
            for file_name in os.listdir(ssh_dir):
                if file_name.endswith('.pub'):
                    key_path = os.path.join(ssh_dir, file_name)
                    with open(key_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                    
                    keys.append({
                        'name': file_name,
                        'path': key_path,
                        'type': content.split()[0] if content else 'unknown',
                        'comment': content.split()[-1] if len(content.split()) > 2 else ''
                    })
        except Exception as e:
            self.logger.error(f"列出SSH密钥失败: {e}")
        
        return keys
    
    def _parse_git_credentials(self, credentials_path: str) -> List[str]:
        """解析Git凭据文件"""
        credentials = []
        try:
            with open(credentials_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if 'github.com' in line:
                        credentials.append(line)
        except Exception as e:
            self.logger.error(f"解析Git凭据失败: {e}")
        
        return credentials
    
    def _parse_github_cli_config(self, config_dir: str) -> Dict[str, any]:
        """解析GitHub CLI配置"""
        config = {}
        try:
            hosts_file = os.path.join(config_dir, 'hosts.yml')
            if os.path.exists(hosts_file):
                # 这里可以添加YAML解析，简化处理
                with open(hosts_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'github.com' in content:
                        config['has_github_auth'] = True
                        
        except Exception as e:
            self.logger.error(f"解析GitHub CLI配置失败: {e}")
        
        return config
    
    def _parse_vscode_git_accounts(self, cache_dir: str) -> List[Dict[str, str]]:
        """解析VSCode中的Git账号信息"""
        accounts = []
        try:
            if os.path.isdir(cache_dir):
                for file_name in os.listdir(cache_dir):
                    file_path = os.path.join(cache_dir, file_name)
                    if file_name.endswith('.json'):
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            if isinstance(data, dict) and 'github' in str(data).lower():
                                accounts.append({
                                    'file': file_name,
                                    'data': data
                                })
        except Exception as e:
            self.logger.error(f"解析VSCode Git账号失败: {e}")
        
        return accounts
    
    def backup_github_configs(self, backup_dir: str) -> Dict[str, any]:
        """备份GitHub相关配置"""
        result = {
            'success': True,
            'backed_up_files': [],
            'errors': []
        }
        
        try:
            github_backup_dir = os.path.join(backup_dir, 'github_configs_backup')
            os.makedirs(github_backup_dir, exist_ok=True)
            
            # 备份各种配置文件
            for config_name, config_path in self.git_config_paths.items():
                if os.path.exists(config_path):
                    backup_path = os.path.join(github_backup_dir, config_name)
                    try:
                        if os.path.isfile(config_path):
                            shutil.copy2(config_path, backup_path)
                        else:
                            shutil.copytree(config_path, backup_path, ignore_errors=True)
                        
                        result['backed_up_files'].append(backup_path)
                        
                    except Exception as e:
                        result['errors'].append(f"备份 {config_name} 失败: {str(e)}")
            
            if result['errors']:
                result['success'] = False
                
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"备份GitHub配置失败: {str(e)}")
        
        return result
    
    def clear_github_authentication(self) -> Dict[str, any]:
        """清理GitHub认证信息"""
        result = {
            'success': True,
            'cleared_items': [],
            'errors': []
        }
        
        try:
            # 清理Git凭据
            credentials_path = self.git_config_paths['git_credentials']
            if os.path.exists(credentials_path):
                try:
                    os.remove(credentials_path)
                    result['cleared_items'].append("Git凭据文件")
                except Exception as e:
                    result['errors'].append(f"删除Git凭据失败: {str(e)}")
            
            # 清理GitHub CLI认证
            gh_config_dir = self.git_config_paths['github_cli_config']
            if os.path.exists(gh_config_dir):
                try:
                    shutil.rmtree(gh_config_dir)
                    result['cleared_items'].append("GitHub CLI配置")
                except Exception as e:
                    result['errors'].append(f"删除GitHub CLI配置失败: {str(e)}")
            
            # 清理VSCode Git缓存
            vscode_git_cache = self.git_config_paths['vscode_git_cache']
            if os.path.exists(vscode_git_cache):
                try:
                    shutil.rmtree(vscode_git_cache)
                    result['cleared_items'].append("VSCode Git缓存")
                except Exception as e:
                    result['errors'].append(f"删除VSCode Git缓存失败: {str(e)}")
            
            if result['errors']:
                result['success'] = False
                
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"清理GitHub认证失败: {str(e)}")
        
        return result
    
    def generate_new_ssh_key(self, email: str, key_name: str = "id_rsa_new") -> Dict[str, any]:
        """生成新的SSH密钥"""
        result = {
            'success': False,
            'key_path': None,
            'public_key': None,
            'errors': []
        }
        
        try:
            ssh_dir = self.git_config_paths['ssh_dir']
            os.makedirs(ssh_dir, exist_ok=True)
            
            private_key_path = os.path.join(ssh_dir, key_name)
            public_key_path = f"{private_key_path}.pub"
            
            # 生成SSH密钥
            cmd = [
                'ssh-keygen',
                '-t', 'rsa',
                '-b', '4096',
                '-C', email,
                '-f', private_key_path,
                '-N', ''  # 无密码
            ]
            
            subprocess.run(cmd, check=True, capture_output=True)
            
            # 读取公钥内容
            with open(public_key_path, 'r', encoding='utf-8') as f:
                public_key_content = f.read().strip()
            
            result['success'] = True
            result['key_path'] = private_key_path
            result['public_key'] = public_key_content
            
        except subprocess.CalledProcessError as e:
            result['errors'].append(f"SSH密钥生成失败: {e.stderr.decode()}")
        except Exception as e:
            result['errors'].append(f"生成SSH密钥失败: {str(e)}")
        
        return result
    
    def reset_git_config(self, new_name: str, new_email: str) -> Dict[str, any]:
        """重置Git配置"""
        result = {
            'success': True,
            'updated_config': {},
            'errors': []
        }
        
        try:
            # 设置新的Git配置
            commands = [
                ['git', 'config', '--global', 'user.name', new_name],
                ['git', 'config', '--global', 'user.email', new_email],
                ['git', 'config', '--global', 'init.defaultBranch', 'main']
            ]
            
            for cmd in commands:
                try:
                    subprocess.run(cmd, check=True, capture_output=True)
                except subprocess.CalledProcessError as e:
                    result['errors'].append(f"执行命令失败 {' '.join(cmd)}: {e.stderr.decode()}")
            
            result['updated_config'] = {
                'name': new_name,
                'email': new_email
            }
            
            if result['errors']:
                result['success'] = False
                
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置Git配置失败: {str(e)}")
        
        return result
    
    def comprehensive_github_reset(self, backup_dir: str, new_name: str, new_email: str) -> Dict[str, any]:
        """综合GitHub重置"""
        result = {
            'success': True,
            'steps_completed': [],
            'errors': [],
            'backup_result': None,
            'clear_result': None,
            'ssh_result': None,
            'config_result': None
        }
        
        try:
            # 1. 备份现有配置
            result['backup_result'] = self.backup_github_configs(backup_dir)
            if result['backup_result']['success']:
                result['steps_completed'].append("GitHub配置备份完成")
            else:
                result['errors'].extend(result['backup_result']['errors'])
            
            # 2. 清理认证信息
            result['clear_result'] = self.clear_github_authentication()
            if result['clear_result']['success']:
                result['steps_completed'].append("GitHub认证清理完成")
            else:
                result['errors'].extend(result['clear_result']['errors'])
            
            # 3. 生成新SSH密钥
            result['ssh_result'] = self.generate_new_ssh_key(new_email)
            if result['ssh_result']['success']:
                result['steps_completed'].append("新SSH密钥生成完成")
            else:
                result['errors'].extend(result['ssh_result']['errors'])
            
            # 4. 重置Git配置
            result['config_result'] = self.reset_git_config(new_name, new_email)
            if result['config_result']['success']:
                result['steps_completed'].append("Git配置重置完成")
            else:
                result['errors'].extend(result['config_result']['errors'])
            
            # 判断整体是否成功
            result['success'] = len(result['errors']) == 0
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"综合GitHub重置失败: {str(e)}")
        
        return result
