# 🆓 AugmentNew 免费版使用说明

## 🎯 重要声明

**本软件完全免费，永久免费，拒绝任何收费！**

- ✅ 无需激活码
- ✅ 无需验证码  
- ✅ 无需付费
- ✅ 完全开源
- ✅ 源码透明

## 🚀 快速启动

### 方法一：VBS启动器（推荐）
双击运行 `免费版启动器.vbs`

### 方法二：批处理启动
双击运行 `启动免费版.bat`

### 方法三：直接启动
```bash
python gui_main.py
```

## 📋 使用前准备

1. **安装Python**
   - 需要Python 3.10或更高版本
   - 下载地址：https://www.python.org/downloads/
   - 安装时勾选"Add Python to PATH"

2. **关闭VS Code**
   - 使用前请完全退出VS Code
   - 确保没有VS Code相关进程运行

## 🛠️ 功能说明

### 🔄 修改Telemetry ID
- 重置设备标识
- 生成新的机器ID和设备ID
- 自动备份原始文件

### 🗃️ 清理数据库
- 清理VS Code数据库中的AugmentCode记录
- 自动备份数据库文件
- 显示清理的记录数量

### 💾 清理工作区
- 清理工作区存储文件
- 自动备份工作区数据
- 删除缓存和配置文件

### 🚀 一键清理全部
- 执行上述所有操作
- 最彻底的清理方式
- 推荐使用此功能

### 🗑️ 删除所有备份
- 清理历史备份文件
- 释放磁盘空间
- 谨慎使用

## ⚠️ 注意事项

1. **备份重要数据**
   - 程序会自动创建备份
   - 建议手动备份重要配置

2. **管理员权限**
   - 某些操作可能需要管理员权限
   - 右键"以管理员身份运行"

3. **网络连接**
   - 检查更新功能需要网络连接
   - 其他功能可离线使用

## 🔧 故障排除

### Python相关问题
- 确保Python已正确安装
- 检查PATH环境变量
- 尝试重新安装Python

### 依赖包问题
```bash
pip install customtkinter pillow
```

### 权限问题
- 右键"以管理员身份运行"
- 检查文件权限设置

### 文件不存在
- 确保所有文件完整
- 重新下载完整版本

## 📞 获取帮助

- GitHub Issues: https://github.com/alltobebetter/AugmentNew/issues
- 查看源码: https://github.com/alltobebetter/AugmentNew

## 🚫 防骗提醒

**如果遇到以下情况，请立即举报：**

- 要求支付费用
- 要求激活码或验证码
- 声称是"付费版"或"专业版"
- 要求提供个人信息
- 包含广告或推广内容

**记住：真正的开源软件永远免费！**

## 📜 开源许可

本项目采用MIT License开源许可证

## 🙏 致谢

感谢所有支持开源精神的用户和开发者！

---

**🆓 永久免费 | 🔓 完全开源 | 🛡️ 安全可靠**
