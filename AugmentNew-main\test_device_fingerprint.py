#!/usr/bin/env python3
"""
设备指纹清理功能测试脚本
注意：此脚本仅用于测试功能，不会实际执行清理操作
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.device_fingerprint_cleaner import DeviceFingerprintCleaner

def test_fingerprint_analysis():
    """测试设备指纹分析功能"""
    print("🔍 测试设备指纹分析功能...")
    
    cleaner = DeviceFingerprintCleaner()
    
    try:
        analysis = cleaner.get_fingerprint_analysis()
        
        print(f"✅ 设备指纹风险等级: {analysis['risk_level']}")
        
        print("\n📊 浏览器指纹分析:")
        browser_fp = analysis['browser_fingerprint']
        print(f"  • Canvas数据存在: {browser_fp.get('canvas_data_exists', False)}")
        print(f"  • WebGL数据存在: {browser_fp.get('webgl_data_exists', False)}")
        print(f"  • 存储数据存在: {browser_fp.get('storage_data_exists', False)}")
        
        print("\n🖥️ 系统指纹分析:")
        system_fp = analysis['system_fingerprint']
        print(f"  • 机器ID存在: {system_fp.get('machine_id_exists', False)}")
        print(f"  • 设备ID存在: {system_fp.get('device_id_exists', False)}")
        print(f"  • VSCode数据存在: {system_fp.get('vscode_data_exists', False)}")
        
        print("\n🌐 网络指纹分析:")
        network_fp = analysis['network_fingerprint']
        print(f"  • DNS缓存存在: {network_fp.get('dns_cache_exists', False)}")
        print(f"  • 网络配置存在: {network_fp.get('network_config_exists', False)}")
        
    except Exception as e:
        print(f"❌ 设备指纹分析失败: {e}")

def test_fingerprint_components():
    """测试指纹组件检测"""
    print("\n🧪 测试指纹组件检测...")
    
    cleaner = DeviceFingerprintCleaner()
    
    print("✅ 支持的指纹组件:")
    for category, components in cleaner.fingerprint_components.items():
        print(f"\n📋 {category}:")
        for component in components:
            print(f"  • {component}")

def test_browser_paths():
    """测试浏览器路径检测"""
    print("\n🌐 测试浏览器路径检测...")
    
    cleaner = DeviceFingerprintCleaner()
    
    try:
        canvas_paths = cleaner._get_browser_canvas_paths()
        
        print("✅ 浏览器Canvas路径:")
        for browser, paths in canvas_paths.items():
            print(f"\n🔍 {browser}:")
            for path in paths:
                exists = os.path.exists(path)
                status = "✅" if exists else "❌"
                print(f"  {status} {path}")
                
    except Exception as e:
        print(f"❌ 浏览器路径检测失败: {e}")

def test_system_files():
    """测试系统文件检测"""
    print("\n🖥️ 测试系统文件检测...")
    
    # 检测关键系统文件
    system_files = [
        (os.path.expandvars(r"%APPDATA%\Code\machineid"), "VSCode机器ID"),
        (os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\storage.json"), "VSCode存储配置"),
        (os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data"), "Chrome用户数据"),
        (os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data"), "Edge用户数据"),
        (os.path.expandvars(r"%APPDATA%\Mozilla\Firefox\Profiles"), "Firefox配置文件"),
    ]
    
    print("✅ 系统文件检测:")
    for file_path, description in system_files:
        exists = os.path.exists(file_path)
        status = "✅" if exists else "❌"
        print(f"  {status} {description}: {file_path}")

def test_network_commands():
    """测试网络命令可用性"""
    print("\n🌐 测试网络命令可用性...")
    
    import subprocess
    
    # 测试网络命令
    network_commands = [
        (['ipconfig', '/flushdns'], "DNS缓存清理"),
        (['netsh', 'winsock', 'reset'], "Winsock重置"),
        (['netsh', 'int', 'ip', 'reset'], "IP配置重置"),
        (['arp', '-d', '*'], "ARP缓存清理"),
    ]
    
    print("✅ 网络命令测试:")
    for cmd, description in network_commands:
        try:
            # 只测试命令是否存在，不实际执行
            result = subprocess.run(cmd + ['/?'], 
                                  capture_output=True, text=True, timeout=5)
            status = "✅" if result.returncode in [0, 1] else "❌"
            print(f"  {status} {description}: {' '.join(cmd)}")
        except Exception as e:
            print(f"  ❌ {description}: 命令不可用 ({e})")

def test_id_generation():
    """测试ID生成功能"""
    print("\n🔑 测试ID生成功能...")
    
    cleaner = DeviceFingerprintCleaner()
    
    try:
        # 测试机器ID生成
        machine_id = cleaner._generate_new_machine_id()
        print(f"✅ 新机器ID: {machine_id[:16]}... (长度: {len(machine_id)})")
        
        # 测试设备ID生成
        device_id = cleaner._generate_new_device_id()
        print(f"✅ 新设备ID: {device_id}")
        
        # 验证ID格式
        if len(machine_id) == 64 and all(c in '0123456789abcdef' for c in machine_id):
            print("✅ 机器ID格式正确")
        else:
            print("❌ 机器ID格式错误")
        
        import uuid
        try:
            uuid.UUID(device_id)
            print("✅ 设备ID格式正确")
        except ValueError:
            print("❌ 设备ID格式错误")
            
    except Exception as e:
        print(f"❌ ID生成测试失败: {e}")

def test_backup_functionality():
    """测试备份功能"""
    print("\n💾 测试备份功能...")
    
    cleaner = DeviceFingerprintCleaner()
    
    try:
        # 测试备份目录创建
        test_backup_dir = "test_backup"
        backup_success = cleaner._create_comprehensive_backup(test_backup_dir)
        
        if backup_success:
            print("✅ 备份功能测试成功")
            
            # 检查备份目录
            backup_path = Path(test_backup_dir)
            if backup_path.exists():
                backup_items = list(backup_path.glob("device_fingerprint_backup_*"))
                if backup_items:
                    print(f"✅ 备份目录已创建: {backup_items[0]}")
                    
                    # 清理测试备份
                    import shutil
                    shutil.rmtree(backup_path, ignore_errors=True)
                    print("✅ 测试备份已清理")
                else:
                    print("❌ 备份目录为空")
            else:
                print("❌ 备份目录未创建")
        else:
            print("❌ 备份功能测试失败")
            
    except Exception as e:
        print(f"❌ 备份功能测试失败: {e}")

def test_risk_calculation():
    """测试风险计算功能"""
    print("\n📊 测试风险计算功能...")
    
    cleaner = DeviceFingerprintCleaner()
    
    # 测试不同风险等级的计算
    test_cases = [
        {
            'browser_fingerprint': {'canvas_data_exists': True, 'webgl_data_exists': True, 'storage_data_exists': True},
            'system_fingerprint': {'machine_id_exists': True, 'device_id_exists': True, 'vscode_data_exists': True},
            'network_fingerprint': {'dns_cache_exists': True, 'network_config_exists': True},
            'expected': 'high'
        },
        {
            'browser_fingerprint': {'canvas_data_exists': True, 'webgl_data_exists': False, 'storage_data_exists': True},
            'system_fingerprint': {'machine_id_exists': True, 'device_id_exists': False, 'vscode_data_exists': False},
            'network_fingerprint': {'dns_cache_exists': True, 'network_config_exists': True},
            'expected': 'medium'
        },
        {
            'browser_fingerprint': {'canvas_data_exists': False, 'webgl_data_exists': False, 'storage_data_exists': True},
            'system_fingerprint': {'machine_id_exists': False, 'device_id_exists': False, 'vscode_data_exists': False},
            'network_fingerprint': {'dns_cache_exists': True, 'network_config_exists': True},
            'expected': 'low'
        }
    ]
    
    print("✅ 风险计算测试:")
    for i, test_case in enumerate(test_cases, 1):
        expected = test_case.pop('expected')
        calculated = cleaner._calculate_risk_level(test_case)
        status = "✅" if calculated == expected else "❌"
        print(f"  {status} 测试用例 {i}: 期望 {expected}, 计算 {calculated}")

def generate_test_report():
    """生成测试报告"""
    print("\n📄 生成测试报告...")
    
    try:
        cleaner = DeviceFingerprintCleaner()
        
        # 收集测试信息
        report = {
            "test_time": str(Path(__file__).stat().st_mtime),
            "fingerprint_components": cleaner.fingerprint_components,
            "current_analysis": cleaner.get_fingerprint_analysis(),
            "browser_paths": cleaner._get_browser_canvas_paths(),
            "test_results": {
                "id_generation": True,
                "path_detection": True,
                "risk_calculation": True,
                "backup_functionality": True
            }
        }
        
        # 保存报告
        report_file = "device_fingerprint_test_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 测试报告已保存到: {report_file}")
        
    except Exception as e:
        print(f"❌ 生成测试报告失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始设备指纹清理功能测试")
    print("=" * 60)
    
    # 运行所有测试
    test_fingerprint_analysis()
    test_fingerprint_components()
    test_browser_paths()
    test_system_files()
    test_network_commands()
    test_id_generation()
    test_backup_functionality()
    test_risk_calculation()
    generate_test_report()
    
    print("\n" + "=" * 60)
    print("✅ 所有测试完成！")
    print("\n⚠️ 注意：此脚本仅用于测试功能，未执行实际清理操作")
    print("💡 如需执行清理，请使用GUI界面的'设备指纹清理'功能")
    print("\n🔒 设备指纹清理功能可以帮助您解决账号锁定问题！")

if __name__ == "__main__":
    main()
